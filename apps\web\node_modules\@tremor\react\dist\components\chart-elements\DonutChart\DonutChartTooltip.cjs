"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),t=require("../../../lib/tremorTwMerge.cjs"),r=require("../common/ChartTooltip.cjs");function l(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var o=l(e);exports.DonutChartTooltip=({active:e,payload:l,valueFormatter:a})=>{if(e&&(null==l?void 0:l[0])){const e=null==l?void 0:l[0];return o.default.createElement(r.ChartTooltipFrame,null,o.default.createElement("div",{className:t.tremorTwMerge("px-4 py-2")},o.default.createElement(r.ChartTooltipRow,{value:a(e.value),name:e.name,color:e.payload.color})))}return null};
