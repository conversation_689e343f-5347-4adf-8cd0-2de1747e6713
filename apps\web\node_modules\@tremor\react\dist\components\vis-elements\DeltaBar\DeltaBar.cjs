'use client';
"use strict";var e=require("tslib"),r=require("react"),t=require("../../util-elements/Tooltip/Tooltip.cjs"),a=require("../../../lib/constants.cjs"),l=require("../../../lib/tremorTwMerge.cjs"),s=require("../../../lib/utils.cjs"),o=require("./styles.cjs");function i(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var n=i(r);const u=s.makeClassName("DeltaBar"),d=n.default.forwardRef(((r,i)=>{const{value:d,isIncreasePositive:c=!0,showAnimation:m=!1,className:f,tooltip:g}=r,b=e.__rest(r,["value","isIncreasePositive","showAnimation","className","tooltip"]),p=s.mapInputsToDeltaType((e=>e>=0?a.DeltaTypes.Increase:a.DeltaTypes.Decrease)(d),c),{tooltipProps:v,getReferenceProps:w}=t.useTooltip();return n.default.createElement(n.default.Fragment,null,n.default.createElement(t.default,Object.assign({text:g},v)),n.default.createElement("div",Object.assign({ref:i,className:l.tremorTwMerge(u("root"),"relative flex items-center w-full rounded-tremor-full h-2","bg-tremor-background-subtle","dark:bg-dark-tremor-background-subtle",f)},b),n.default.createElement("div",{className:"flex justify-end h-full w-1/2"},d<0?n.default.createElement("div",Object.assign({ref:v.refs.setReference,className:l.tremorTwMerge(u("negativeDeltaBar"),"rounded-l-tremor-full",o.colors[p].bgColor),style:{width:`${Math.abs(d)}%`,transition:m?"all duration-300":""}},w)):null),n.default.createElement("div",{className:l.tremorTwMerge(u("separator"),"ring-2 z-10 rounded-tremor-full h-4 w-1","ring-tremor-brand-inverted bg-tremor-background-emphasis","dark:ring-dark-tremor-brand-inverted dark:bg-dark-tremor-background-emphasis")}),n.default.createElement("div",{className:l.tremorTwMerge(u("positiveDeltaBarWrapper"),"flex justify-start h-full w-1/2")},d>=0?n.default.createElement("div",Object.assign({ref:v.refs.setReference,className:l.tremorTwMerge(u("positiveDeltaBar"),"rounded-r-tremor-full",o.colors[p].bgColor),style:{width:`${Math.abs(d)}%`,transition:m?"all 1s":""}},w)):null)))}));d.displayName="DeltaBar",module.exports=d;
