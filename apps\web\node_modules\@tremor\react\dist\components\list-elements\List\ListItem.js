import{__rest as e}from"tslib";import t from"react";import{tremorTwMerge as r}from"../../../lib/tremorTwMerge.js";import{makeClassName as l}from"../../../lib/utils.js";const m=l("ListItem"),s=t.forwardRef(((l,s)=>{const{children:a,className:i}=l,o=e(l,["children","className"]);return t.createElement(t.Fragment,null,t.createElement("li",Object.assign({ref:s,className:r(m("root"),"w-full flex justify-between items-center text-tremor-default py-2",i)},o),a))}));s.displayName="ListItem";export{s as default};
