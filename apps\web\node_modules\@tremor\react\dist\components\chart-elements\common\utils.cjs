"use strict";Object.defineProperty(exports,"__esModule",{value:!0});exports.constructCategories=(e,t)=>{if(!t)return[];const r=new Set;return e.forEach((e=>{r.add(e[t])})),Array.from(r)},exports.constructCategoryColors=(e,t)=>{const r=new Map;return e.forEach(((e,o)=>{r.set(e,t[o%t.length])})),r},exports.deepEqual=function e(t,r){if(t===r)return!0;if("object"!=typeof t||"object"!=typeof r||null===t||null===r)return!1;const o=Object.keys(t),n=Object.keys(r);if(o.length!==n.length)return!1;for(const s of o)if(!n.includes(s)||!e(t[s],r[s]))return!1;return!0},exports.getYAxisDomain=(e,t,r)=>[e?"auto":null!=t?t:0,null!=r?r:"auto"],exports.hasOnlyOneValueForThisKey=function(e,t){const r=[];for(const o of e)if(Object.prototype.hasOwnProperty.call(o,t)&&(r.push(o[t]),r.length>1))return!1;return!0};
