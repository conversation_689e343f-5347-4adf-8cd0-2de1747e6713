'use client';
import{__rest as e}from"tslib";import t from"react";import{tremorTwMerge as r}from"../../../lib/tremorTwMerge.js";import{makeClassName as a}from"../../../lib/utils.js";import{ComboboxOption as o}from"@headlessui/react";const c=a("SearchSelectItem"),s=t.forwardRef(((a,s)=>{const{value:m,icon:n,className:d,children:l}=a,u=e(a,["value","icon","className","children"]),i=n;return t.createElement(o,Object.assign({className:r(c("root"),"flex justify-start items-center cursor-default text-tremor-default p-2.5","data-[focus]:bg-tremor-background-muted  data-[focus]:text-tremor-content-strong data-[selected]:text-tremor-content-strong data-[selected]:bg-tremor-background-muted text-tremor-content-emphasis","dark:data-[focus]:bg-dark-tremor-background-muted  dark:data-[focus]:text-dark-tremor-content-strong dark:data-[selected]:text-dark-tremor-content-strong dark:data-[selected]:bg-dark-tremor-background-muted dark:text-dark-tremor-content-emphasis",d),ref:s,key:m,value:m},u),i&&t.createElement(i,{className:r(c("icon"),"flex-none h-5 w-5 mr-3","text-tremor-content-subtle","dark:text-dark-tremor-content-subtle")}),t.createElement("span",{className:"whitespace-nowrap truncate"},null!=l?l:m))}));s.displayName="SearchSelectItem";export{s as default};
