import{colorPalette as r}from"../../../lib/theme.js";import{tremorTwMerge as e}from"../../../lib/tremorTwMerge.js";import{getColorClassNames as o}from"../../../lib/utils.js";const t={xs:{height:"h-4",width:"w-4"},sm:{height:"h-5",width:"w-5"},md:{height:"h-5",width:"w-5"},lg:{height:"h-6",width:"w-6"},xl:{height:"h-6",width:"w-6"}},d=r=>"light"!==r?{xs:{paddingX:"px-2.5",paddingY:"py-1.5",fontSize:"text-xs"},sm:{paddingX:"px-4",paddingY:"py-2",fontSize:"text-sm"},md:{paddingX:"px-4",paddingY:"py-2",fontSize:"text-md"},lg:{paddingX:"px-4",paddingY:"py-2.5",fontSize:"text-lg"},xl:{paddingX:"px-4",paddingY:"py-3",fontSize:"text-xl"}}:{xs:{paddingX:"",paddingY:"",fontSize:"text-xs"},sm:{paddingX:"",paddingY:"",fontSize:"text-sm"},md:{paddingX:"",paddingY:"",fontSize:"text-md"},lg:{paddingX:"",paddingY:"",fontSize:"text-lg"},xl:{paddingX:"",paddingY:"",fontSize:"text-xl"}},a=(t,d)=>{switch(t){case"primary":return{textColor:d?o("white").textColor:"text-tremor-brand-inverted dark:text-dark-tremor-brand-inverted",hoverTextColor:d?o("white").textColor:"text-tremor-brand-inverted dark:text-dark-tremor-brand-inverted",bgColor:d?o(d,r.background).bgColor:"bg-tremor-brand dark:bg-dark-tremor-brand",hoverBgColor:d?o(d,r.darkBackground).hoverBgColor:"hover:bg-tremor-brand-emphasis dark:hover:bg-dark-tremor-brand-emphasis",borderColor:d?o(d,r.border).borderColor:"border-tremor-brand dark:border-dark-tremor-brand",hoverBorderColor:d?o(d,r.darkBorder).hoverBorderColor:"hover:border-tremor-brand-emphasis dark:hover:border-dark-tremor-brand-emphasis"};case"secondary":return{textColor:d?o(d,r.text).textColor:"text-tremor-brand dark:text-dark-tremor-brand",hoverTextColor:d?o(d,r.text).textColor:"hover:text-tremor-brand-emphasis dark:hover:text-dark-tremor-brand-emphasis",bgColor:o("transparent").bgColor,hoverBgColor:d?e(o(d,r.background).hoverBgColor,"hover:bg-opacity-20 dark:hover:bg-opacity-20"):"hover:bg-tremor-brand-faint dark:hover:bg-dark-tremor-brand-faint",borderColor:d?o(d,r.border).borderColor:"border-tremor-brand dark:border-dark-tremor-brand"};case"light":return{textColor:d?o(d,r.text).textColor:"text-tremor-brand dark:text-dark-tremor-brand",hoverTextColor:d?o(d,r.darkText).hoverTextColor:"hover:text-tremor-brand-emphasis dark:hover:text-dark-tremor-brand-emphasis",bgColor:o("transparent").bgColor,borderColor:"",hoverBorderColor:""}}};export{a as getButtonColors,d as getButtonProportions,t as iconSizes};
