'use client';
import{__rest as e}from"tslib";import{colorPalette as r}from"../../../lib/theme.js";import{tremorTwMerge as t}from"../../../lib/tremorTwMerge.js";import{mergeRefs as o,getColorClassNames as l,makeClassName as a}from"../../../lib/utils.js";import s from"react";import c,{useTooltip as i}from"../../util-elements/Tooltip/Tooltip.js";const m=a("Tracker"),n=s.forwardRef(((a,n)=>{const{color:p,tooltip:f}=a,d=e(a,["color","tooltip"]),{tooltipProps:u,getReferenceProps:b}=i();return s.createElement("div",Object.assign({ref:o([n,u.refs.setReference]),className:t(m("trackingBlock"),"w-full h-full rounded-[1px] first:rounded-l-[4px] last:rounded-r-[4px]",l(null!=p?p:"gray",r.background).bgColor)},d,b),s.createElement(c,Object.assign({text:f},u)))}));n.displayName="TrackerBlock";const p=s.forwardRef(((r,o)=>{const{data:l=[],className:a}=r,c=e(r,["data","className"]);return s.createElement("div",Object.assign({ref:o,className:t(m("root"),"h-10 flex items-center space-x-0.5",a)},c),l.map(((e,r)=>{var t;return s.createElement(n,{key:null!==(t=e.key)&&void 0!==t?t:r,color:e.color,tooltip:e.tooltip})})))}));p.displayName="Tracker";export{p as default,m as makeTrackerClassName};
