"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var t=require("date-fns");require("../../../lib/tremorTwMerge.cjs");const e=require("../../../lib/utils.cjs").makeClassName("DateRangePicker"),r=[{value:"tdy",text:"Today",from:t.startOfToday()},{value:"w",text:"Last 7 days",from:t.sub(t.startOfToday(),{days:7})},{value:"t",text:"Last 30 days",from:t.sub(t.startOfToday(),{days:30})},{value:"m",text:"Month to Date",from:t.startOfMonth(t.startOfToday())},{value:"y",text:"Year to Date",from:t.startOfYear(t.startOfToday())}];exports.defaultOptions=r,exports.formatSelectedDates=(e,r,a,o)=>{const s=(null==a?void 0:a.code)||"en-US";if(!e&&!r)return"";if(e&&!r){if(o)return t.format(e,o);const r={year:"numeric",month:"short",day:"numeric"};return e.toLocaleDateString(s,r)}if(e&&r){if(t.isEqual(e,r)){if(o)return t.format(e,o);const r={year:"numeric",month:"short",day:"numeric"};return e.toLocaleDateString(s,r)}if(e.getMonth()===r.getMonth()&&e.getFullYear()===r.getFullYear()){if(o)return`${t.format(e,o)} - ${t.format(r,o)}`;const a={month:"short",day:"numeric"};return`${e.toLocaleDateString(s,a)} - \n                    ${r.getDate()}, ${r.getFullYear()}`}{if(o)return`${t.format(e,o)} - ${t.format(r,o)}`;const a={year:"numeric",month:"short",day:"numeric"};return`${e.toLocaleDateString(s,a)} - \n                    ${r.toLocaleDateString(s,a)}`}}return""},exports.makeDateRangePickerClassName=e,exports.parseEndDate=(e,r,a,o)=>{var s,n;if(a&&(e=t.startOfDay(null!==(n=null===(s=o.get(a))||void 0===s?void 0:s.to)&&void 0!==n?n:t.startOfToday())),e)return e&&!r?t.startOfDay(e):t.startOfDay(t.min([e,r]))},exports.parseStartDate=(e,r,a,o)=>{var s;if(a&&(e=null===(s=o.get(a))||void 0===s?void 0:s.from),e)return e&&!r?t.startOfDay(e):t.startOfDay(t.max([e,r]))};
