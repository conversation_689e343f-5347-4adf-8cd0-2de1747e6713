import{autoUpdate as Z,flip as ee,inner as te,offset as ne,shift as le,size as re,useFloating as oe,useInnerOffset as ie,useInteractions as se}from"@floating-ui/react";import*as j from"react";import{createContext as _,use<PERSON><PERSON>back as ae,useContext as R,useMemo as M,useRef as ue,useState as A}from"react";import{useDisposables as fe}from'../hooks/use-disposables.js';import{useEvent as z}from'../hooks/use-event.js';import{useIsoMorphicEffect as C}from'../hooks/use-iso-morphic-effect.js';let y=_({styles:void 0,setReference:()=>{},setFloating:()=>{},getReferenceProps:()=>({}),getFloatingProps:()=>({}),slot:{}});y.displayName="FloatingContext";let H=_(null);H.displayName="PlacementContext";function xe(e){return M(()=>e?typeof e=="string"?{to:e}:e:null,[e])}function ye(){return R(y).setReference}function Fe(){return R(y).getReferenceProps}function be(){let{getFloatingProps:e,slot:t}=R(y);return ae((...n)=>Object.assign({},e(...n),{"data-anchor":t.anchor}),[e,t])}function Re(e=null){e===!1&&(e=null),typeof e=="string"&&(e={to:e});let t=R(H),n=M(()=>e,[JSON.stringify(e,(r,o)=>{var u;return(u=o==null?void 0:o.outerHTML)!=null?u:o})]);C(()=>{t==null||t(n!=null?n:null)},[t,n]);let l=R(y);return M(()=>[l.setFloating,e?l.styles:{}],[l.setFloating,e,l.styles])}let q=4;function Me({children:e,enabled:t=!0}){let[n,l]=A(null),[r,o]=A(0),u=ue(null),[f,s]=A(null);pe(f);let i=t&&n!==null&&f!==null,{to:F="bottom",gap:E=0,offset:v=0,padding:c=0,inner:P}=ce(n,f),[a,p="center"]=F.split(" ");C(()=>{i&&o(0)},[i]);let{refs:b,floatingStyles:w,context:g}=oe({open:i,placement:a==="selection"?p==="center"?"bottom":`bottom-${p}`:p==="center"?`${a}`:`${a}-${p}`,strategy:"absolute",transform:!1,middleware:[ne({mainAxis:a==="selection"?0:E,crossAxis:v}),le({padding:c}),a!=="selection"&&ee({padding:c}),a==="selection"&&P?te({...P,padding:c,overflowRef:u,offset:r,minItemsVisible:q,referenceOverflowThreshold:c,onFallbackChange(h){var O,W;if(!h)return;let d=g.elements.floating;if(!d)return;let T=parseFloat(getComputedStyle(d).scrollPaddingBottom)||0,$=Math.min(q,d.childElementCount),L=0,N=0;for(let m of(W=(O=g.elements.floating)==null?void 0:O.childNodes)!=null?W:[])if(m instanceof HTMLElement){let x=m.offsetTop,k=x+m.clientHeight+T,S=d.scrollTop,U=S+d.clientHeight;if(x>=S&&k<=U)$--;else{N=Math.max(0,Math.min(k,U)-Math.max(x,S)),L=m.clientHeight;break}}$>=1&&o(m=>{let x=L*$-N+T;return m>=x?m:x})}}):null,re({padding:c,apply({availableWidth:h,availableHeight:d,elements:T}){Object.assign(T.floating.style,{overflow:"auto",maxWidth:`${h}px`,maxHeight:`min(var(--anchor-max-height, 100vh), ${d}px)`})}})].filter(Boolean),whileElementsMounted:Z}),[I=a,B=p]=g.placement.split("-");a==="selection"&&(I="selection");let G=M(()=>({anchor:[I,B].filter(Boolean).join(" ")}),[I,B]),K=ie(g,{overflowRef:u,onChange:o}),{getReferenceProps:Q,getFloatingProps:X}=se([K]),Y=z(h=>{s(h),b.setFloating(h)});return j.createElement(H.Provider,{value:l},j.createElement(y.Provider,{value:{setFloating:Y,setReference:b.setReference,styles:w,getReferenceProps:Q,getFloatingProps:X,slot:G}},e))}function pe(e){C(()=>{if(!e)return;let t=new MutationObserver(()=>{let n=window.getComputedStyle(e).maxHeight,l=parseFloat(n);if(isNaN(l))return;let r=parseInt(n);isNaN(r)||l!==r&&(e.style.maxHeight=`${Math.ceil(l)}px`)});return t.observe(e,{attributes:!0,attributeFilter:["style"]}),()=>{t.disconnect()}},[e])}function ce(e,t){var o,u,f;let n=V((o=e==null?void 0:e.gap)!=null?o:"var(--anchor-gap, 0)",t),l=V((u=e==null?void 0:e.offset)!=null?u:"var(--anchor-offset, 0)",t),r=V((f=e==null?void 0:e.padding)!=null?f:"var(--anchor-padding, 0)",t);return{...e,gap:n,offset:l,padding:r}}function V(e,t,n=void 0){let l=fe(),r=z((s,i)=>{if(s==null)return[n,null];if(typeof s=="number")return[s,null];if(typeof s=="string"){if(!i)return[n,null];let F=J(s,i);return[F,E=>{let v=D(s);{let c=v.map(P=>window.getComputedStyle(i).getPropertyValue(P));l.requestAnimationFrame(function P(){l.nextFrame(P);let a=!1;for(let[b,w]of v.entries()){let g=window.getComputedStyle(i).getPropertyValue(w);if(c[b]!==g){c[b]=g,a=!0;break}}if(!a)return;let p=J(s,i);F!==p&&(E(p),F=p)})}return l.dispose}]}return[n,null]}),o=M(()=>r(e,t)[0],[e,t]),[u=o,f]=A();return C(()=>{let[s,i]=r(e,t);if(f(s),!!i)return i(f)},[e,t]),u}function D(e){let t=/var\((.*)\)/.exec(e);if(t){let n=t[1].indexOf(",");if(n===-1)return[t[1]];let l=t[1].slice(0,n).trim(),r=t[1].slice(n+1).trim();return r?[l,...D(r)]:[l]}return[]}function J(e,t){let n=document.createElement("div");t.appendChild(n),n.style.setProperty("margin-top","0px","important"),n.style.setProperty("margin-top",e,"important");let l=parseFloat(window.getComputedStyle(n).marginTop)||0;return t.removeChild(n),l}export{Me as FloatingProvider,Re as useFloatingPanel,be as useFloatingPanelProps,ye as useFloatingReference,Fe as useFloatingReferenceProps,xe as useResolvedAnchor};
