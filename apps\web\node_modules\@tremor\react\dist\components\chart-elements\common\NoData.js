import{tremorTwMerge as e}from"../../../lib/tremorTwMerge.js";import r from"react";const t=({className:t,noDataText:o="No data"})=>r.createElement("div",{className:e("flex items-center justify-center w-full h-full border border-dashed rounded-tremor-default","border-tremor-border","dark:border-dark-tremor-border",t)},r.createElement("p",{className:e("text-tremor-content text-tremor-default","dark:text-dark-tremor-content")},o));export{t as default};
