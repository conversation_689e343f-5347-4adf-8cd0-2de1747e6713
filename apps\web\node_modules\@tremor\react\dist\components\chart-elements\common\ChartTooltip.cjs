"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),r=require("../../../lib/constants.cjs"),t=require("../../../lib/theme.cjs"),a=require("../../../lib/tremorTwMerge.cjs"),o=require("../../../lib/utils.cjs");function d(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var l=d(e);const m=({children:e})=>l.default.createElement("div",{className:a.tremorTwMerge("rounded-tremor-default text-tremor-default border","bg-tremor-background shadow-tremor-dropdown border-tremor-border","dark:bg-dark-tremor-background dark:shadow-dark-tremor-dropdown dark:border-dark-tremor-border")},e),n=({value:e,name:r,color:d})=>l.default.createElement("div",{className:"flex items-center justify-between space-x-8"},l.default.createElement("div",{className:"flex items-center space-x-2"},l.default.createElement("span",{className:a.tremorTwMerge("shrink-0 rounded-tremor-full border-2 h-3 w-3","border-tremor-background shadow-tremor-card","dark:border-dark-tremor-background dark:shadow-dark-tremor-card",o.getColorClassNames(d,t.colorPalette.background).bgColor)}),l.default.createElement("p",{className:a.tremorTwMerge("text-right whitespace-nowrap","text-tremor-content","dark:text-dark-tremor-content")},r)),l.default.createElement("p",{className:a.tremorTwMerge("font-medium tabular-nums text-right whitespace-nowrap","text-tremor-content-emphasis","dark:text-dark-tremor-content-emphasis")},e));exports.ChartTooltipFrame=m,exports.ChartTooltipRow=n,exports.default=({active:e,payload:t,label:o,categoryColors:d,valueFormatter:s})=>{if(e&&t){const e=t.filter((e=>"none"!==e.type));return l.default.createElement(m,null,l.default.createElement("div",{className:a.tremorTwMerge("border-tremor-border border-b px-4 py-2","dark:border-dark-tremor-border")},l.default.createElement("p",{className:a.tremorTwMerge("font-medium","text-tremor-content-emphasis","dark:text-dark-tremor-content-emphasis")},o)),l.default.createElement("div",{className:a.tremorTwMerge("px-4 py-2 space-y-1")},e.map((({value:e,name:t},a)=>{var o;return l.default.createElement(n,{key:`id-${a}`,value:s(e),name:t,color:null!==(o=d.get(t))&&void 0!==o?o:r.BaseColors.Blue})}))))}return null};
