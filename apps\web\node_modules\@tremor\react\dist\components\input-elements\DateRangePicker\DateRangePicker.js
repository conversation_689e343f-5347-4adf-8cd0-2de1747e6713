'use client';
import{__rest as e}from"tslib";import r,{useState as t,useMemo as a}from"react";import o from"../../../assets/CalendarIcon.js";import n from"../../../assets/XCircleIcon.js";import{startOfToday as d,startOfMonth as l}from"date-fns";import{tremorTwMerge as m}from"../../../lib/tremorTwMerge.js";import{getNodeText as s,constructValueToNameMapping as i,getSelectButtonColors as u,hasValue as c}from"../selectUtils.js";import{defaultOptions as b,parseStartDate as f,parseEndDate as p,formatSelectedDates as k,makeDateRangePickerClassName as v}from"./dateRangePickerUtils.js";import g from"../Calendar/Calendar.js";import"../Select/Select.js";import h from"../Select/SelectItem.js";import{enUS as w}from"date-fns/locale";import x from"../../../hooks/useInternalState.js";import{Popover as y,PopoverButton as E,Transition as N,PopoverPanel as S,Listbox as j,ListboxButton as C,ListboxOptions as D}from"@headlessui/react";const F=d(),V=r.forwardRef(((d,V)=>{var I,O;const{value:_,defaultValue:T,onValueChange:M,enableSelect:P=!0,minDate:R,maxDate:Y,placeholder:z="Select range",selectPlaceholder:B="Select range",disabled:U=!1,locale:X=w,enableClear:q=!0,displayFormat:A,children:G,className:H,enableYearNavigation:J=!1,weekStartsOn:K=0,disabledDates:L}=d,Q=e(d,["value","defaultValue","onValueChange","enableSelect","minDate","maxDate","placeholder","selectPlaceholder","disabled","locale","enableClear","displayFormat","children","className","enableYearNavigation","weekStartsOn","disabledDates"]),[W,Z]=x(T,_),[$,ee]=t(!1),[re,te]=t(!1),ae=a((()=>{const e=[];return R&&e.push({before:R}),Y&&e.push({after:Y}),[...e,...null!=L?L:[]]}),[R,Y,L]),oe=a((()=>{const e=new Map;return G?r.Children.forEach(G,(r=>{var t;e.set(r.props.value,{text:null!==(t=s(r))&&void 0!==t?t:r.props.value,from:r.props.from,to:r.props.to})})):b.forEach((r=>{e.set(r.value,{text:r.text,from:r.from,to:F})})),e}),[G]),ne=a((()=>{if(G)return i(G);const e=new Map;return b.forEach((r=>e.set(r.value,r.text))),e}),[G]),de=(null==W?void 0:W.selectValue)||"",le=f(null==W?void 0:W.from,R,de,oe),me=p(null==W?void 0:W.to,Y,de,oe),se=le||me?k(le,me,X,A):z,ie=l(null!==(O=null!==(I=null!=me?me:le)&&void 0!==I?I:Y)&&void 0!==O?O:F),ue=q&&!U;return r.createElement("div",Object.assign({ref:V,className:m("w-full min-w-[10rem] relative flex justify-between text-tremor-default max-w-sm shadow-tremor-input dark:shadow-dark-tremor-input rounded-tremor-default",H)},Q),r.createElement(y,{as:"div",className:m("w-full",P?"rounded-l-tremor-default":"rounded-tremor-default",$&&"ring-2 ring-tremor-brand-muted dark:ring-dark-tremor-brand-muted z-10")},r.createElement("div",{className:"relative w-full"},r.createElement(E,{onFocus:()=>ee(!0),onBlur:()=>ee(!1),disabled:U,className:m("w-full outline-none text-left whitespace-nowrap truncate focus:ring-2 transition duration-100 rounded-l-tremor-default flex flex-nowrap border pl-3 py-2","rounded-l-tremor-default border-tremor-border text-tremor-content-emphasis focus:border-tremor-brand-subtle focus:ring-tremor-brand-muted","dark:border-dark-tremor-border dark:text-dark-tremor-content-emphasis dark:focus:border-dark-tremor-brand-subtle dark:focus:ring-dark-tremor-brand-muted",P?"rounded-l-tremor-default":"rounded-tremor-default",ue?"pr-8":"pr-4",u(c(le||me),U))},r.createElement(o,{className:m(v("calendarIcon"),"flex-none shrink-0 h-5 w-5 -ml-0.5 mr-2","text-tremor-content-subtle","dark:text-dark-tremor-content-subtle"),"aria-hidden":"true"}),r.createElement("p",{className:"truncate"},se)),ue&&le?r.createElement("button",{type:"button",className:m("absolute outline-none inset-y-0 right-0 flex items-center transition duration-100 mr-4"),onClick:e=>{e.preventDefault(),null==M||M({}),Z({})}},r.createElement(n,{className:m(v("clearIcon"),"flex-none h-4 w-4","text-tremor-content-subtle","dark:text-dark-tremor-content-subtle")})):null),r.createElement(N,{enter:"transition ease duration-100 transform",enterFrom:"opacity-0 -translate-y-4",enterTo:"opacity-100 translate-y-0",leave:"transition ease duration-100 transform",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 -translate-y-4"},r.createElement(S,{anchor:"bottom start",focus:!0,className:m("min-w-min divide-y overflow-y-auto outline-none rounded-tremor-default p-3 border [--anchor-gap:4px]","bg-tremor-background border-tremor-border divide-tremor-border shadow-tremor-dropdown","dark:bg-dark-tremor-background dark:border-dark-tremor-border dark:divide-dark-tremor-border dark:shadow-dark-tremor-dropdown")},r.createElement(g,Object.assign({mode:"range",showOutsideDays:!0,defaultMonth:ie,selected:{from:le,to:me},onSelect:e=>{null==M||M({from:null==e?void 0:e.from,to:null==e?void 0:e.to}),Z({from:null==e?void 0:e.from,to:null==e?void 0:e.to})},locale:X,disabled:ae,enableYearNavigation:J,classNames:{day_range_middle:m("!rounded-none aria-selected:!bg-tremor-background-subtle aria-selected:dark:!bg-dark-tremor-background-subtle aria-selected:!text-tremor-content aria-selected:dark:!bg-dark-tremor-background-subtle"),day_range_start:"rounded-r-none rounded-l-tremor-small aria-selected:text-tremor-brand-inverted dark:aria-selected:text-dark-tremor-brand-inverted",day_range_end:"rounded-l-none rounded-r-tremor-small aria-selected:text-tremor-brand-inverted dark:aria-selected:text-dark-tremor-brand-inverted"},weekStartsOn:K},d))))),P&&r.createElement(j,{as:"div",className:m("w-48 -ml-px rounded-r-tremor-default",re&&"ring-2 ring-tremor-brand-muted dark:ring-dark-tremor-brand-muted z-10"),value:de,onChange:e=>{const{from:r,to:t}=oe.get(e),a=null!=t?t:F;null==M||M({from:r,to:a,selectValue:e}),Z({from:r,to:a,selectValue:e})},disabled:U},(({value:e})=>{var t;return r.createElement(r.Fragment,null,r.createElement(C,{onFocus:()=>te(!0),onBlur:()=>te(!1),className:m("w-full outline-none text-left whitespace-nowrap truncate rounded-r-tremor-default transition duration-100 border px-4 py-2","border-tremor-border text-tremor-content-emphasis focus:border-tremor-brand-subtle","dark:border-dark-tremor-border  dark:text-dark-tremor-content-emphasis dark:focus:border-dark-tremor-brand-subtle",u(c(e),U))},e&&null!==(t=ne.get(e))&&void 0!==t?t:B),r.createElement(N,{enter:"transition ease duration-100 transform",enterFrom:"opacity-0 -translate-y-4",enterTo:"opacity-100 translate-y-0",leave:"transition ease duration-100 transform",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 -translate-y-4"},r.createElement(D,{anchor:"bottom end",className:m("[--anchor-gap:4px] divide-y overflow-y-auto outline-none border min-w-44","shadow-tremor-dropdown bg-tremor-background border-tremor-border divide-tremor-border rounded-tremor-default","dark:shadow-dark-tremor-dropdown dark:bg-dark-tremor-background dark:border-dark-tremor-border dark:divide-dark-tremor-border")},null!=G?G:b.map((e=>r.createElement(h,{key:e.value,value:e.value},e.text))))))})))}));V.displayName="DateRangePicker";export{V as default};
