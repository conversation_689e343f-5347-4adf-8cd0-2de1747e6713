'use client';
"use strict";var e=require("tslib"),r=require("../../../assets/ArrowDownHeadIcon.cjs"),t=require("react"),a=require("../../../assets/XCircleIcon.cjs"),l=require("../../../lib/tremorTwMerge.cjs"),o=require("../../../lib/utils.cjs"),n=require("../selectUtils.cjs"),s=require("@headlessui/react"),d=require("../../../hooks/useInternalState.cjs");function u(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var c=u(t);const m=o.makeClassName("Select"),i=c.default.forwardRef(((o,u)=>{const{defaultValue:i="",value:f,onValueChange:b,placeholder:p="Select...",disabled:w=!1,icon:g,enableClear:h=!1,required:k,children:v,name:x,error:y=!1,errorMessage:N,className:E,id:M}=o,T=e.__rest(o,["defaultValue","value","onValueChange","placeholder","disabled","icon","enableClear","required","children","name","error","errorMessage","className","id"]),q=t.useRef(null),C=t.Children.toArray(v),[j,V]=d(i,f),I=g,S=t.useMemo((()=>{const e=c.default.Children.toArray(v).filter(t.isValidElement);return n.constructValueToNameMapping(e)}),[v]);return c.default.createElement("div",{className:l.tremorTwMerge("w-full min-w-[10rem] text-tremor-default",E)},c.default.createElement("div",{className:"relative"},c.default.createElement("select",{title:"select-hidden",required:k,className:l.tremorTwMerge("h-full w-full absolute left-0 top-0 -z-10 opacity-0"),value:j,onChange:e=>{e.preventDefault()},name:x,disabled:w,id:M,onFocus:()=>{const e=q.current;e&&e.focus()}},c.default.createElement("option",{className:"hidden",value:"",disabled:!0,hidden:!0},p),C.map((e=>{const r=e.props.value,t=e.props.children;return c.default.createElement("option",{className:"hidden",key:r,value:r},t)}))),c.default.createElement(s.Listbox,Object.assign({as:"div",ref:u,defaultValue:j,value:j,onChange:e=>{null==b||b(e),V(e)},disabled:w,id:M},T),(({value:e})=>{var t;return c.default.createElement(c.default.Fragment,null,c.default.createElement(s.ListboxButton,{ref:q,className:l.tremorTwMerge("w-full outline-none text-left whitespace-nowrap truncate rounded-tremor-default focus:ring-2 transition duration-100 border pr-8 py-2","border-tremor-border shadow-tremor-input focus:border-tremor-brand-subtle focus:ring-tremor-brand-muted","dark:border-dark-tremor-border dark:shadow-dark-tremor-input dark:focus:border-dark-tremor-brand-subtle dark:focus:ring-dark-tremor-brand-muted",I?"pl-10":"pl-3",n.getSelectButtonColors(n.hasValue(e),w,y))},I&&c.default.createElement("span",{className:l.tremorTwMerge("absolute inset-y-0 left-0 flex items-center ml-px pl-2.5")},c.default.createElement(I,{className:l.tremorTwMerge(m("Icon"),"flex-none h-5 w-5","text-tremor-content-subtle","dark:text-dark-tremor-content-subtle")})),c.default.createElement("span",{className:"w-[90%] block truncate"},e&&null!==(t=S.get(e))&&void 0!==t?t:p),c.default.createElement("span",{className:l.tremorTwMerge("absolute inset-y-0 right-0 flex items-center mr-3")},c.default.createElement(r,{className:l.tremorTwMerge(m("arrowDownIcon"),"flex-none h-5 w-5","text-tremor-content-subtle","dark:text-dark-tremor-content-subtle")}))),h&&j?c.default.createElement("button",{type:"button",className:l.tremorTwMerge("absolute inset-y-0 right-0 flex items-center mr-8"),onClick:e=>{e.preventDefault(),V(""),null==b||b("")}},c.default.createElement(a,{className:l.tremorTwMerge(m("clearIcon"),"flex-none h-4 w-4","text-tremor-content-subtle","dark:text-dark-tremor-content-subtle")})):null,c.default.createElement(s.Transition,{enter:"transition ease duration-100 transform",enterFrom:"opacity-0 -translate-y-4",enterTo:"opacity-100 translate-y-0",leave:"transition ease duration-100 transform",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 -translate-y-4"},c.default.createElement(s.ListboxOptions,{anchor:"bottom start",className:l.tremorTwMerge("z-10 w-[var(--button-width)] divide-y overflow-y-auto outline-none rounded-tremor-default max-h-[228px]  border [--anchor-gap:4px]","bg-tremor-background border-tremor-border divide-tremor-border shadow-tremor-dropdown","dark:bg-dark-tremor-background dark:border-dark-tremor-border dark:divide-dark-tremor-border dark:shadow-dark-tremor-dropdown")},v)))}))),y&&N?c.default.createElement("p",{className:l.tremorTwMerge("errorMessage","text-sm text-rose-500 mt-1")},N):null)}));i.displayName="Select",module.exports=i;
