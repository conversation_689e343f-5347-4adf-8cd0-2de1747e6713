"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/simple-login/page",{

/***/ "(app-pages-browser)/./src/stores/auth.ts":
/*!****************************!*\
  !*** ./src/stores/auth.ts ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuthStore: function() { return /* binding */ useAuthStore; }\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zustand/middleware */ \"(app-pages-browser)/./node_modules/zustand/esm/middleware.mjs\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n\n\n\n\nconst useAuthStore = (0,zustand__WEBPACK_IMPORTED_MODULE_2__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_3__.persist)((set, get)=>({\n        // Initial state\n        user: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: null,\n        // Actions\n        login: async (credentials)=>{\n            console.log(\"\\uD83D\\uDD10 Auth Store: Login called with:\", credentials);\n            try {\n                set({\n                    isLoading: true,\n                    error: null\n                });\n                console.log(\"\\uD83D\\uDD10 Auth Store: Set loading to true\");\n                const authResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.apiClient.login(credentials);\n                console.log(\"\\uD83D\\uDD10 Auth Store: API response:\", authResponse);\n                set({\n                    user: authResponse.user,\n                    isAuthenticated: true,\n                    isLoading: false,\n                    error: null\n                });\n                console.log(\"\\uD83D\\uDD10 Auth Store: Updated state with user\");\n                sonner__WEBPACK_IMPORTED_MODULE_1__.toast.success(\"Successfully logged in!\");\n                console.log(\"\\uD83D\\uDD10 Auth Store: Login completed successfully\");\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD10 Auth Store: Login error:\", error);\n                set({\n                    user: null,\n                    isAuthenticated: false,\n                    isLoading: false,\n                    error: error.message\n                });\n                sonner__WEBPACK_IMPORTED_MODULE_1__.toast.error(error.message || \"Login failed\");\n                throw error;\n            }\n        },\n        register: async (userData)=>{\n            try {\n                set({\n                    isLoading: true,\n                    error: null\n                });\n                const authResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.apiClient.register(userData);\n                set({\n                    user: authResponse.user,\n                    isAuthenticated: true,\n                    isLoading: false,\n                    error: null\n                });\n                sonner__WEBPACK_IMPORTED_MODULE_1__.toast.success(\"Account created successfully!\");\n            } catch (error) {\n                set({\n                    user: null,\n                    isAuthenticated: false,\n                    isLoading: false,\n                    error: error.message\n                });\n                sonner__WEBPACK_IMPORTED_MODULE_1__.toast.error(error.message || \"Registration failed\");\n                throw error;\n            }\n        },\n        logout: async ()=>{\n            try {\n                set({\n                    isLoading: true\n                });\n                await _lib_api__WEBPACK_IMPORTED_MODULE_0__.apiClient.logout();\n                set({\n                    user: null,\n                    isAuthenticated: false,\n                    isLoading: false,\n                    error: null\n                });\n                sonner__WEBPACK_IMPORTED_MODULE_1__.toast.success(\"Successfully logged out\");\n            } catch (error) {\n                // Even if logout fails on server, clear local state\n                set({\n                    user: null,\n                    isAuthenticated: false,\n                    isLoading: false,\n                    error: null\n                });\n                sonner__WEBPACK_IMPORTED_MODULE_1__.toast.error(\"Logout failed, but you have been signed out locally\");\n            }\n        },\n        getCurrentUser: async ()=>{\n            try {\n                set({\n                    isLoading: true,\n                    error: null\n                });\n                const user = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.apiClient.getCurrentUser();\n                set({\n                    user,\n                    isAuthenticated: true,\n                    isLoading: false,\n                    error: null\n                });\n            } catch (error) {\n                set({\n                    user: null,\n                    isAuthenticated: false,\n                    isLoading: false,\n                    error: error.message\n                });\n                throw error;\n            }\n        },\n        updateProfile: async (data)=>{\n            try {\n                set({\n                    isLoading: true,\n                    error: null\n                });\n                const updatedUser = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.apiClient.updateProfile(data);\n                set({\n                    user: updatedUser,\n                    isLoading: false,\n                    error: null\n                });\n                sonner__WEBPACK_IMPORTED_MODULE_1__.toast.success(\"Profile updated successfully!\");\n            } catch (error) {\n                set({\n                    isLoading: false,\n                    error: error.message\n                });\n                sonner__WEBPACK_IMPORTED_MODULE_1__.toast.error(error.message || \"Failed to update profile\");\n                throw error;\n            }\n        },\n        clearError: ()=>{\n            set({\n                error: null\n            });\n        },\n        setLoading: (loading)=>{\n            set({\n                isLoading: loading\n            });\n        }\n    }), {\n    name: \"auth-storage\",\n    partialize: (state)=>({\n            user: state.user,\n            isAuthenticated: state.isAuthenticated\n        })\n}));\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/stores/auth.ts\n"));

/***/ })

});