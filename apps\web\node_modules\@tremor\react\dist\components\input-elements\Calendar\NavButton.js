import{__rest as r}from"tslib";import"../../icon-elements/Badge/Badge.js";import"../../icon-elements/BadgeDelta/BadgeDelta.js";import e from"../../icon-elements/Icon/Icon.js";import{tremorTwMerge as t}from"../../../lib/tremorTwMerge.js";import o from"react";const n=n=>{var{onClick:m,icon:a}=n,d=r(n,["onClick","icon"]);const c=a;return o.createElement("button",Object.assign({type:"button",className:t("flex items-center justify-center p-1 h-7 w-7 outline-none focus:ring-2 transition duration-100 border border-tremor-border dark:border-dark-tremor-border hover:bg-tremor-background-muted dark:hover:bg-dark-tremor-background-muted rounded-tremor-small focus:border-tremor-brand-subtle select-none dark:focus:border-dark-tremor-brand-subtle focus:ring-tremor-brand-muted dark:focus:ring-dark-tremor-brand-muted text-tremor-content-subtle dark:text-dark-tremor-content-subtle hover:text-tremor-content dark:hover:text-dark-tremor-content")},d),o.createElement(e,{onClick:m,icon:c,variant:"simple",color:"slate",size:"sm"}))};export{n as NavButton};
