/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/auth/login/page";
exports.ids = ["app/auth/login/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Flogin%2Fpage&page=%2Fauth%2Flogin%2Fpage&appPaths=%2Fauth%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fauth%2Flogin%2Fpage.tsx&appDir=C%3A%5Capps%5CInterviewSpark%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Capps%5CInterviewSpark%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Flogin%2Fpage&page=%2Fauth%2Flogin%2Fpage&appPaths=%2Fauth%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fauth%2Flogin%2Fpage.tsx&appDir=C%3A%5Capps%5CInterviewSpark%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Capps%5CInterviewSpark%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'auth',\n        {\n        children: [\n        'login',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/login/page.tsx */ \"(rsc)/./src/app/auth/login/page.tsx\")), \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/auth/login/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/auth/login/page\",\n        pathname: \"/auth/login\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Flogin%2Fpage&page=%2Fauth%2Flogin%2Fpage&appPaths=%2Fauth%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fauth%2Flogin%2Fpage.tsx&appDir=C%3A%5Capps%5CInterviewSpark%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Capps%5CInterviewSpark%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Capps%5C%5CInterviewSpark%5C%5Capps%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Capps%5C%5CInterviewSpark%5C%5Capps%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Capps%5C%5CInterviewSpark%5C%5Capps%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Capps%5C%5CInterviewSpark%5C%5Capps%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Capps%5C%5CInterviewSpark%5C%5Capps%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Capps%5C%5CInterviewSpark%5C%5Capps%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Capps%5C%5CInterviewSpark%5C%5Capps%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Capps%5C%5CInterviewSpark%5C%5Capps%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Capps%5C%5CInterviewSpark%5C%5Capps%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Capps%5C%5CInterviewSpark%5C%5Capps%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Capps%5C%5CInterviewSpark%5C%5Capps%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Capps%5C%5CInterviewSpark%5C%5Capps%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Capps%5C%5CInterviewSpark%5C%5Capps%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Capps%5C%5CInterviewSpark%5C%5Capps%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Capps%5C%5CInterviewSpark%5C%5Capps%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Capps%5C%5CInterviewSpark%5C%5Capps%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Capps%5C%5CInterviewSpark%5C%5Capps%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Capps%5C%5CInterviewSpark%5C%5Capps%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Capps%5C%5CInterviewSpark%5C%5Capps%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Capps%5C%5CInterviewSpark%5C%5Capps%5C%5Cweb%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Capps%5C%5CInterviewSpark%5C%5Capps%5C%5Cweb%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Capps%5C%5CInterviewSpark%5C%5Capps%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Capps%5C%5CInterviewSpark%5C%5Capps%5C%5Cweb%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Capps%5C%5CInterviewSpark%5C%5Capps%5C%5Cweb%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers.tsx */ \"(ssr)/./src/components/providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNhcHBzJTVDJTVDSW50ZXJ2aWV3U3BhcmslNUMlNUNhcHBzJTVDJTVDd2ViJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2ZvbnQlNUMlNUNnb29nbGUlNUMlNUN0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMnNyYyU1QyU1QyU1QyU1Q2FwcCU1QyU1QyU1QyU1Q2xheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJJbnRlciU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNhcHBzJTVDJTVDSW50ZXJ2aWV3U3BhcmslNUMlNUNhcHBzJTVDJTVDd2ViJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q2FwcHMlNUMlNUNJbnRlcnZpZXdTcGFyayU1QyU1Q2FwcHMlNUMlNUN3ZWIlNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDcHJvdmlkZXJzLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlByb3ZpZGVycyUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0tBQXVJIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQHdlYi9hcHAvPzM5MDkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJQcm92aWRlcnNcIl0gKi8gXCJDOlxcXFxhcHBzXFxcXEludGVydmlld1NwYXJrXFxcXGFwcHNcXFxcd2ViXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXHByb3ZpZGVycy50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Capps%5C%5CInterviewSpark%5C%5Capps%5C%5Cweb%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Capps%5C%5CInterviewSpark%5C%5Capps%5C%5Cweb%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Capps%5C%5CInterviewSpark%5C%5Capps%5C%5Cweb%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Capps%5C%5CInterviewSpark%5C%5Capps%5C%5Cweb%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Capps%5C%5CInterviewSpark%5C%5Capps%5C%5Cweb%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/login/page.tsx */ \"(ssr)/./src/app/auth/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNhcHBzJTVDJTVDSW50ZXJ2aWV3U3BhcmslNUMlNUNhcHBzJTVDJTVDd2ViJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDYXV0aCU1QyU1Q2xvZ2luJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHNLQUF5RyIsInNvdXJjZXMiOlsid2VicGFjazovL0B3ZWIvYXBwLz83MTdjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcYXBwc1xcXFxJbnRlcnZpZXdTcGFya1xcXFxhcHBzXFxcXHdlYlxcXFxzcmNcXFxcYXBwXFxcXGF1dGhcXFxcbG9naW5cXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Capps%5C%5CInterviewSpark%5C%5Capps%5C%5Cweb%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/auth/login/page.tsx":
/*!*************************************!*\
  !*** ./src/app/auth/login/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react-hook-form */ \"(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(ssr)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! zod */ \"(ssr)/./node_modules/zod/v3/types.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Eye_EyeOff_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Eye,EyeOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Eye_EyeOff_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Eye,EyeOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Eye_EyeOff_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Eye,EyeOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _stores_auth__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/stores/auth */ \"(ssr)/./src/stores/auth.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\nconst loginSchema = zod__WEBPACK_IMPORTED_MODULE_9__.object({\n    email: zod__WEBPACK_IMPORTED_MODULE_9__.string().email(\"Please enter a valid email address\"),\n    password: zod__WEBPACK_IMPORTED_MODULE_9__.string().min(6, \"Password must be at least 6 characters\")\n});\nfunction LoginPage() {\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { login, isLoading } = (0,_stores_auth__WEBPACK_IMPORTED_MODULE_8__.useAuthStore)();\n    const { register, handleSubmit, formState: { errors } } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__.zodResolver)(loginSchema)\n    });\n    const onSubmit = async (data)=>{\n        try {\n            await login(data);\n            router.push(\"/dashboard\");\n        } catch (error) {\n        // Error is handled by the store and toast\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full max-w-md\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Eye_EyeOff_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            className: \"h-10 w-10 text-blue-600 mr-3\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-3xl font-bold text-gray-900\",\n                            children: \"AI-InterviewSpark\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                    className: \"text-2xl\",\n                                    children: \"Welcome Back\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardDescription, {\n                                    children: \"Sign in to your account to continue your interview preparation\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-sm font-medium text-blue-900 mb-2\",\n                                            children: \"Demo Credentials\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-blue-700 mb-2\",\n                                            children: \"Use any valid email and password (minimum 6 characters) to login:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-1 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Email:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 70,\n                                                            columnNumber: 22\n                                                        }, this),\n                                                        \" <EMAIL>\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 70,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Password:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 71,\n                                                            columnNumber: 22\n                                                        }, this),\n                                                        \" password123\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 71,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mt-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-blue-600\",\n                                                    children: \"Or use any email format with a password of 6+ characters\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 74,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                    type: \"button\",\n                                                    size: \"sm\",\n                                                    variant: \"outline\",\n                                                    onClick: ()=>{\n                                                        setValue(\"email\", \"<EMAIL>\");\n                                                        setValue(\"password\", \"password123\");\n                                                    },\n                                                    className: \"text-xs\",\n                                                    children: \"Quick Fill\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 77,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleSubmit(onSubmit),\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"email\",\n                                                    className: \"text-sm font-medium text-gray-700\",\n                                                    children: \"Email\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                    id: \"email\",\n                                                    type: \"email\",\n                                                    placeholder: \"<EMAIL>\",\n                                                    ...register(\"email\"),\n                                                    className: errors.email ? \"border-red-500\" : \"\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 17\n                                                }, this),\n                                                errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-red-500\",\n                                                    children: errors.email.message\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"password\",\n                                                    className: \"text-sm font-medium text-gray-700\",\n                                                    children: \"Password\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                            id: \"password\",\n                                                            type: showPassword ? \"text\" : \"password\",\n                                                            placeholder: \"password123\",\n                                                            ...register(\"password\"),\n                                                            className: errors.password ? \"border-red-500 pr-10\" : \"pr-10\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 114,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: ()=>setShowPassword(!showPassword),\n                                                            className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\",\n                                                            children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Eye_EyeOff_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                lineNumber: 127,\n                                                                columnNumber: 23\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Eye_EyeOff_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                lineNumber: 129,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 121,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 17\n                                                }, this),\n                                                errors.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-red-500\",\n                                                    children: errors.password.message\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            className: \"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 140,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-2 text-sm text-gray-600\",\n                                                            children: \"Remember me\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 144,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 139,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    href: \"/auth/forgot-password\",\n                                                    className: \"text-sm text-blue-600 hover:text-blue-500\",\n                                                    children: \"Forgot password?\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            type: \"submit\",\n                                            className: \"w-full\",\n                                            loading: isLoading,\n                                            disabled: isLoading,\n                                            children: \"Sign In\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 flex items-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full border-t border-gray-300\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 167,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative flex justify-center text-sm\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 bg-white text-gray-500\",\n                                                        children: \"Or continue with\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 170,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-6 grid grid-cols-2 gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                    variant: \"outline\",\n                                                    className: \"w-full\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 mr-2\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fill: \"currentColor\",\n                                                                    d: \"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 177,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fill: \"currentColor\",\n                                                                    d: \"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 181,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fill: \"currentColor\",\n                                                                    d: \"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 185,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fill: \"currentColor\",\n                                                                    d: \"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 189,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 176,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Google\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 175,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                    variant: \"outline\",\n                                                    className: \"w-full\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 mr-2\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                lineNumber: 198,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 197,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Facebook\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-6 text-center text-sm text-gray-600\",\n                                    children: [\n                                        \"Don't have an account?\",\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            href: \"/auth/register\",\n                                            className: \"text-blue-600 hover:text-blue-500 font-medium\",\n                                            children: \"Sign up\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n            lineNumber: 48,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/auth/login/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers.tsx":
/*!**************************************!*\
  !*** ./src/components/providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.module.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! sonner */ \"(ssr)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\n\n\nfunction Providers({ children }) {\n    const [queryClient] = react__WEBPACK_IMPORTED_MODULE_1__.useState(()=>new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.QueryClient({\n            defaultOptions: {\n                queries: {\n                    staleTime: 60 * 1000,\n                    retry: (failureCount, error)=>{\n                        // Don't retry on 4xx errors\n                        if (error?.response?.status >= 400 && error?.response?.status < 500) {\n                            return false;\n                        }\n                        return failureCount < 3;\n                    }\n                },\n                mutations: {\n                    retry: false\n                }\n            }\n        }));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.QueryClientProvider, {\n        client: queryClient,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n            attribute: \"class\",\n            defaultTheme: \"system\",\n            enableSystem: true,\n            disableTransitionOnChange: true,\n            children: [\n                children,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(sonner__WEBPACK_IMPORTED_MODULE_3__.Toaster, {\n                    position: \"top-right\",\n                    toastOptions: {\n                        duration: 4000,\n                        style: {\n                            background: \"hsl(var(--background))\",\n                            color: \"hsl(var(--foreground))\",\n                            border: \"1px solid hsl(var(--border))\"\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\providers.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\providers.tsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\providers.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, loading = false, children, disabled, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        disabled: disabled || loading,\n        ...props,\n        children: [\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"mr-2 h-4 w-4 animate-spin\"\n            }, void 0, false, {\n                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\button.tsx\",\n                lineNumber: 55,\n                columnNumber: 11\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 48,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 71,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/input.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/input.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 11,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9pbnB1dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUE4QjtBQUVFO0FBS2hDLE1BQU1FLHNCQUFRRiw2Q0FBZ0IsQ0FDNUIsQ0FBQyxFQUFFSSxTQUFTLEVBQUVDLElBQUksRUFBRSxHQUFHQyxPQUFPLEVBQUVDO0lBQzlCLHFCQUNFLDhEQUFDQztRQUNDSCxNQUFNQTtRQUNORCxXQUFXSCw4Q0FBRUEsQ0FDWCxnV0FDQUc7UUFFRkcsS0FBS0E7UUFDSixHQUFHRCxLQUFLOzs7Ozs7QUFHZjtBQUVGSixNQUFNTyxXQUFXLEdBQUc7QUFFSiIsInNvdXJjZXMiOlsid2VicGFjazovL0B3ZWIvYXBwLy4vc3JjL2NvbXBvbmVudHMvdWkvaW5wdXQudHN4P2M5ODMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcclxuXHJcbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCJcclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgSW5wdXRQcm9wc1xyXG4gIGV4dGVuZHMgUmVhY3QuSW5wdXRIVE1MQXR0cmlidXRlczxIVE1MSW5wdXRFbGVtZW50PiB7fVxyXG5cclxuY29uc3QgSW5wdXQgPSBSZWFjdC5mb3J3YXJkUmVmPEhUTUxJbnB1dEVsZW1lbnQsIElucHV0UHJvcHM+KFxyXG4gICh7IGNsYXNzTmFtZSwgdHlwZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiB7XHJcbiAgICByZXR1cm4gKFxyXG4gICAgICA8aW5wdXRcclxuICAgICAgICB0eXBlPXt0eXBlfVxyXG4gICAgICAgIGNsYXNzTmFtZT17Y24oXHJcbiAgICAgICAgICBcImZsZXggaC0xMCB3LWZ1bGwgcm91bmRlZC1tZCBib3JkZXIgYm9yZGVyLWlucHV0IGJnLWJhY2tncm91bmQgcHgtMyBweS0yIHRleHQtc20gcmluZy1vZmZzZXQtYmFja2dyb3VuZCBmaWxlOmJvcmRlci0wIGZpbGU6YmctdHJhbnNwYXJlbnQgZmlsZTp0ZXh0LXNtIGZpbGU6Zm9udC1tZWRpdW0gcGxhY2Vob2xkZXI6dGV4dC1tdXRlZC1mb3JlZ3JvdW5kIGZvY3VzLXZpc2libGU6b3V0bGluZS1ub25lIGZvY3VzLXZpc2libGU6cmluZy0yIGZvY3VzLXZpc2libGU6cmluZy1yaW5nIGZvY3VzLXZpc2libGU6cmluZy1vZmZzZXQtMiBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgZGlzYWJsZWQ6b3BhY2l0eS01MFwiLFxyXG4gICAgICAgICAgY2xhc3NOYW1lXHJcbiAgICAgICAgKX1cclxuICAgICAgICByZWY9e3JlZn1cclxuICAgICAgICB7Li4ucHJvcHN9XHJcbiAgICAgIC8+XHJcbiAgICApXHJcbiAgfVxyXG4pXHJcbklucHV0LmRpc3BsYXlOYW1lID0gXCJJbnB1dFwiXHJcblxyXG5leHBvcnQgeyBJbnB1dCB9Il0sIm5hbWVzIjpbIlJlYWN0IiwiY24iLCJJbnB1dCIsImZvcndhcmRSZWYiLCJjbGFzc05hbWUiLCJ0eXBlIiwicHJvcHMiLCJyZWYiLCJpbnB1dCIsImRpc3BsYXlOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiClient: () => (/* binding */ apiClient),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! sonner */ \"(ssr)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _lib_mockApi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/mockApi */ \"(ssr)/./src/lib/mockApi.ts\");\n\n\n\n\nclass ApiClient {\n    constructor(){\n        this.baseURL = \"http://localhost:3001\" || 0;\n        this.useMockApi =  true || 0;\n        // Debug logging (can be removed in production)\n        if (true) {\n            console.log(\"API Client: Using Mock API =\", this.useMockApi);\n        }\n        this.client = axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].create({\n            baseURL: `${this.baseURL}/api`,\n            timeout: 30000,\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        this.setupInterceptors();\n    }\n    setupInterceptors() {\n        // Request interceptor to add auth token\n        this.client.interceptors.request.use((config)=>{\n            const token = this.getToken();\n            if (token && !(0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.isTokenExpired)(token)) {\n                config.headers.Authorization = `Bearer ${token}`;\n            }\n            return config;\n        }, (error)=>{\n            return Promise.reject(error);\n        });\n        // Response interceptor for error handling\n        this.client.interceptors.response.use((response)=>{\n            return response;\n        }, (error)=>{\n            if (error.response?.status === 401) {\n                this.handleUnauthorized();\n            } else if (error.response?.status >= 500) {\n                sonner__WEBPACK_IMPORTED_MODULE_0__.toast.error(\"Server error. Please try again later.\");\n            } else if (error.code === \"NETWORK_ERROR\") {\n                sonner__WEBPACK_IMPORTED_MODULE_0__.toast.error(\"Network error. Please check your connection.\");\n            }\n            return Promise.reject(error);\n        });\n    }\n    getToken() {\n        if (false) {}\n        return null;\n    }\n    setToken(token) {\n        if (false) {}\n    }\n    removeToken() {\n        if (false) {}\n    }\n    handleUnauthorized() {\n        this.removeToken();\n        if (false) {}\n    }\n    // Generic request method\n    async request(config) {\n        try {\n            const response = await this.client.request(config);\n            return response.data;\n        } catch (error) {\n            const message = error.response?.data?.message || (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.getErrorMessage)(error);\n            throw new Error(message);\n        }\n    }\n    // Authentication methods\n    async login(credentials) {\n        if (this.useMockApi) {\n            const result = await _lib_mockApi__WEBPACK_IMPORTED_MODULE_2__.mockApiClient.login(credentials);\n            this.setToken(result.token);\n            return result;\n        }\n        const response = await this.request({\n            method: \"POST\",\n            url: \"/auth/login\",\n            data: credentials\n        });\n        if (response.success && response.data) {\n            this.setToken(response.data.token);\n            return response.data;\n        }\n        throw new Error(response.message || \"Login failed\");\n    }\n    async register(userData) {\n        if (this.useMockApi) {\n            const result = await _lib_mockApi__WEBPACK_IMPORTED_MODULE_2__.mockApiClient.register(userData);\n            this.setToken(result.token);\n            return result;\n        }\n        const response = await this.request({\n            method: \"POST\",\n            url: \"/auth/register\",\n            data: userData\n        });\n        if (response.success && response.data) {\n            this.setToken(response.data.token);\n            return response.data;\n        }\n        throw new Error(response.message || \"Registration failed\");\n    }\n    async logout() {\n        if (this.useMockApi) {\n            await _lib_mockApi__WEBPACK_IMPORTED_MODULE_2__.mockApiClient.logout();\n            this.removeToken();\n            return;\n        }\n        try {\n            await this.request({\n                method: \"POST\",\n                url: \"/auth/logout\"\n            });\n        } finally{\n            this.removeToken();\n        }\n    }\n    async getCurrentUser() {\n        if (this.useMockApi) {\n            return await _lib_mockApi__WEBPACK_IMPORTED_MODULE_2__.mockApiClient.getCurrentUser();\n        }\n        const response = await this.request({\n            method: \"GET\",\n            url: \"/auth/me\"\n        });\n        if (response.success && response.data) {\n            return response.data;\n        }\n        throw new Error(response.message || \"Failed to get user data\");\n    }\n    // User methods\n    async updateProfile(data) {\n        const response = await this.request({\n            method: \"PUT\",\n            url: \"/users/me\",\n            data\n        });\n        if (response.success && response.data) {\n            return response.data;\n        }\n        throw new Error(response.message || \"Failed to update profile\");\n    }\n    async getUserStats() {\n        const response = await this.request({\n            method: \"GET\",\n            url: \"/users/me/stats\"\n        });\n        if (response.success && response.data) {\n            return response.data;\n        }\n        throw new Error(response.message || \"Failed to get user stats\");\n    }\n    // Interview methods\n    async createInterviewSession(config) {\n        if (this.useMockApi) {\n            return await _lib_mockApi__WEBPACK_IMPORTED_MODULE_2__.mockApiClient.createInterviewSession(config);\n        }\n        const response = await this.request({\n            method: \"POST\",\n            url: \"/interviews/sessions\",\n            data: config\n        });\n        if (response.success && response.data) {\n            return response.data;\n        }\n        throw new Error(response.message || \"Failed to create interview session\");\n    }\n    async getInterviewSessions(params) {\n        if (this.useMockApi) {\n            return await _lib_mockApi__WEBPACK_IMPORTED_MODULE_2__.mockApiClient.getInterviewSessions();\n        }\n        const response = await this.request({\n            method: \"GET\",\n            url: \"/interviews/sessions\",\n            params\n        });\n        if (response.success && response.data) {\n            return response.data;\n        }\n        throw new Error(response.message || \"Failed to get interview sessions\");\n    }\n    async getInterviewSession(sessionId) {\n        const response = await this.request({\n            method: \"GET\",\n            url: `/interviews/sessions/${sessionId}`\n        });\n        if (response.success && response.data) {\n            return response.data;\n        }\n        throw new Error(response.message || \"Failed to get interview session\");\n    }\n    async startInterviewSession(sessionId) {\n        const response = await this.request({\n            method: \"PUT\",\n            url: `/interviews/sessions/${sessionId}/start`\n        });\n        if (response.success && response.data) {\n            return response.data;\n        }\n        throw new Error(response.message || \"Failed to start interview session\");\n    }\n    async submitAnswer(sessionId, data) {\n        const formData = new FormData();\n        formData.append(\"questionId\", data.questionId);\n        formData.append(\"duration\", data.duration.toString());\n        if (data.textResponse) {\n            formData.append(\"textResponse\", data.textResponse);\n        }\n        if (data.audioBlob) {\n            formData.append(\"audio\", data.audioBlob, \"answer.webm\");\n        }\n        if (data.videoBlob) {\n            formData.append(\"video\", data.videoBlob, \"answer.webm\");\n        }\n        const response = await this.request({\n            method: \"POST\",\n            url: `/interviews/sessions/${sessionId}/answers`,\n            data: formData,\n            headers: {\n                \"Content-Type\": \"multipart/form-data\"\n            }\n        });\n        if (response.success && response.data) {\n            return response.data;\n        }\n        throw new Error(response.message || \"Failed to submit answer\");\n    }\n    async completeInterviewSession(sessionId) {\n        const response = await this.request({\n            method: \"PUT\",\n            url: `/interviews/sessions/${sessionId}/complete`\n        });\n        if (response.success && response.data) {\n            return response.data;\n        }\n        throw new Error(response.message || \"Failed to complete interview session\");\n    }\n    async getSessionResults(sessionId) {\n        const response = await this.request({\n            method: \"GET\",\n            url: `/interviews/sessions/${sessionId}/results`\n        });\n        if (response.success && response.data) {\n            return response.data;\n        }\n        throw new Error(response.message || \"Failed to get session results\");\n    }\n    // Resume methods\n    async uploadResume(file) {\n        const formData = new FormData();\n        formData.append(\"resume\", file);\n        const response = await this.request({\n            method: \"POST\",\n            url: \"/resumes/upload\",\n            data: formData,\n            headers: {\n                \"Content-Type\": \"multipart/form-data\"\n            }\n        });\n        if (response.success && response.data) {\n            return response.data;\n        }\n        throw new Error(response.message || \"Failed to upload resume\");\n    }\n    async getResumes() {\n        const response = await this.request({\n            method: \"GET\",\n            url: \"/resumes\"\n        });\n        if (response.success && response.data) {\n            return response.data;\n        }\n        throw new Error(response.message || \"Failed to get resumes\");\n    }\n    async analyzeResume(resumeId) {\n        const response = await this.request({\n            method: \"POST\",\n            url: `/resumes/${resumeId}/analyze`\n        });\n        if (response.success && response.data) {\n            return response.data;\n        }\n        throw new Error(response.message || \"Failed to analyze resume\");\n    }\n    // Expert methods\n    async getExperts(params) {\n        const response = await this.request({\n            method: \"GET\",\n            url: \"/experts\",\n            params\n        });\n        if (response.success && response.data) {\n            return response.data;\n        }\n        throw new Error(response.message || \"Failed to get experts\");\n    }\n    async bookExpertSession(expertId, data) {\n        const response = await this.request({\n            method: \"POST\",\n            url: `/experts/${expertId}/book`,\n            data\n        });\n        if (response.success) {\n            return response.data;\n        }\n        throw new Error(response.message || \"Failed to book expert session\");\n    }\n    // Analytics methods\n    async getAnalytics(params) {\n        const response = await this.request({\n            method: \"GET\",\n            url: \"/analytics/user\",\n            params\n        });\n        if (response.success && response.data) {\n            return response.data;\n        }\n        throw new Error(response.message || \"Failed to get analytics\");\n    }\n    // AI methods\n    async generateQuestions(data) {\n        const response = await this.request({\n            method: \"POST\",\n            url: \"/ai/questions\",\n            data\n        });\n        if (response.success && response.data) {\n            return response.data;\n        }\n        throw new Error(response.message || \"Failed to generate questions\");\n    }\n    async analyzeAnswer(data) {\n        const response = await this.request({\n            method: \"POST\",\n            url: \"/ai/analyze-answer\",\n            data\n        });\n        if (response.success && response.data) {\n            return response.data;\n        }\n        throw new Error(response.message || \"Failed to analyze answer\");\n    }\n    async analyzeEmotion(data) {\n        const response = await this.request({\n            method: \"POST\",\n            url: \"/ai/analyze-emotion\",\n            data\n        });\n        if (response.success && response.data) {\n            return response.data;\n        }\n        throw new Error(response.message || \"Failed to analyze emotion\");\n    }\n}\n// Create and export a singleton instance\nconst apiClient = new ApiClient();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL2FwaS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBK0U7QUFDakQ7QUFrQitCO0FBQ2hCO0FBRTdDLE1BQU1LO0lBS0pDLGFBQWM7UUFDWixJQUFJLENBQUNDLE9BQU8sR0FBR0MsdUJBQStCLElBQUk7UUFDbEQsSUFBSSxDQUFDRyxVQUFVLEdBQUdILEtBQXlDLElBQVUsQ0FBZ0M7UUFFckcsK0NBQStDO1FBQy9DLElBQUlBLElBQXlCLEVBQWU7WUFDMUNLLFFBQVFDLEdBQUcsQ0FBQyxnQ0FBZ0MsSUFBSSxDQUFDSCxVQUFVO1FBQzdEO1FBRUEsSUFBSSxDQUFDSSxNQUFNLEdBQUdmLDZDQUFLQSxDQUFDZ0IsTUFBTSxDQUFDO1lBQ3pCVCxTQUFTLENBQUMsRUFBRSxJQUFJLENBQUNBLE9BQU8sQ0FBQyxJQUFJLENBQUM7WUFDOUJVLFNBQVM7WUFDVEMsU0FBUztnQkFDUCxnQkFBZ0I7WUFDbEI7UUFDRjtRQUVBLElBQUksQ0FBQ0MsaUJBQWlCO0lBQ3hCO0lBRVFBLG9CQUFvQjtRQUMxQix3Q0FBd0M7UUFDeEMsSUFBSSxDQUFDSixNQUFNLENBQUNLLFlBQVksQ0FBQ0MsT0FBTyxDQUFDQyxHQUFHLENBQ2xDLENBQUNDO1lBQ0MsTUFBTUMsUUFBUSxJQUFJLENBQUNDLFFBQVE7WUFDM0IsSUFBSUQsU0FBUyxDQUFDckIsMERBQWNBLENBQUNxQixRQUFRO2dCQUNuQ0QsT0FBT0wsT0FBTyxDQUFDUSxhQUFhLEdBQUcsQ0FBQyxPQUFPLEVBQUVGLE1BQU0sQ0FBQztZQUNsRDtZQUNBLE9BQU9EO1FBQ1QsR0FDQSxDQUFDSTtZQUNDLE9BQU9DLFFBQVFDLE1BQU0sQ0FBQ0Y7UUFDeEI7UUFHRiwwQ0FBMEM7UUFDMUMsSUFBSSxDQUFDWixNQUFNLENBQUNLLFlBQVksQ0FBQ1UsUUFBUSxDQUFDUixHQUFHLENBQ25DLENBQUNRO1lBQ0MsT0FBT0E7UUFDVCxHQUNBLENBQUNIO1lBQ0MsSUFBSUEsTUFBTUcsUUFBUSxFQUFFQyxXQUFXLEtBQUs7Z0JBQ2xDLElBQUksQ0FBQ0Msa0JBQWtCO1lBQ3pCLE9BQU8sSUFBSUwsTUFBTUcsUUFBUSxFQUFFQyxVQUFVLEtBQUs7Z0JBQ3hDOUIseUNBQUtBLENBQUMwQixLQUFLLENBQUM7WUFDZCxPQUFPLElBQUlBLE1BQU1NLElBQUksS0FBSyxpQkFBaUI7Z0JBQ3pDaEMseUNBQUtBLENBQUMwQixLQUFLLENBQUM7WUFDZDtZQUNBLE9BQU9DLFFBQVFDLE1BQU0sQ0FBQ0Y7UUFDeEI7SUFFSjtJQUVRRixXQUEwQjtRQUNoQyxJQUFJLEtBQWtCLEVBQWEsRUFFbEM7UUFDRCxPQUFPO0lBQ1Q7SUFFUVcsU0FBU1osS0FBYSxFQUFRO1FBQ3BDLElBQUksS0FBa0IsRUFBYSxFQUVsQztJQUNIO0lBRVFjLGNBQW9CO1FBQzFCLElBQUksS0FBa0IsRUFBYSxFQUdsQztJQUNIO0lBRVFOLHFCQUEyQjtRQUNqQyxJQUFJLENBQUNNLFdBQVc7UUFDaEIsSUFBSSxLQUFrQixFQUFhLEVBRWxDO0lBQ0g7SUFFQSx5QkFBeUI7SUFDekIsTUFBY2pCLFFBQVdFLE1BQTBCLEVBQTJCO1FBQzVFLElBQUk7WUFDRixNQUFNTyxXQUFXLE1BQU0sSUFBSSxDQUFDZixNQUFNLENBQUNNLE9BQU8sQ0FBaUJFO1lBQzNELE9BQU9PLFNBQVNhLElBQUk7UUFDdEIsRUFBRSxPQUFPaEIsT0FBWTtZQUNuQixNQUFNaUIsVUFBVWpCLE1BQU1HLFFBQVEsRUFBRWEsTUFBTUMsV0FBVzFDLDJEQUFlQSxDQUFDeUI7WUFDakUsTUFBTSxJQUFJa0IsTUFBTUQ7UUFDbEI7SUFDRjtJQUVBLHlCQUF5QjtJQUN6QixNQUFNRSxNQUFNQyxXQUF5QixFQUF5QjtRQUM1RCxJQUFJLElBQUksQ0FBQ3BDLFVBQVUsRUFBRTtZQUNuQixNQUFNcUMsU0FBUyxNQUFNNUMsdURBQWFBLENBQUMwQyxLQUFLLENBQUNDO1lBQ3pDLElBQUksQ0FBQ1gsUUFBUSxDQUFDWSxPQUFPeEIsS0FBSztZQUMxQixPQUFPd0I7UUFDVDtRQUVBLE1BQU1sQixXQUFXLE1BQU0sSUFBSSxDQUFDVCxPQUFPLENBQWU7WUFDaEQ0QixRQUFRO1lBQ1JDLEtBQUs7WUFDTFAsTUFBTUk7UUFDUjtRQUVBLElBQUlqQixTQUFTcUIsT0FBTyxJQUFJckIsU0FBU2EsSUFBSSxFQUFFO1lBQ3JDLElBQUksQ0FBQ1AsUUFBUSxDQUFDTixTQUFTYSxJQUFJLENBQUNuQixLQUFLO1lBQ2pDLE9BQU9NLFNBQVNhLElBQUk7UUFDdEI7UUFFQSxNQUFNLElBQUlFLE1BQU1mLFNBQVNjLE9BQU8sSUFBSTtJQUN0QztJQUVBLE1BQU1RLFNBQVNDLFFBQXlCLEVBQXlCO1FBQy9ELElBQUksSUFBSSxDQUFDMUMsVUFBVSxFQUFFO1lBQ25CLE1BQU1xQyxTQUFTLE1BQU01Qyx1REFBYUEsQ0FBQ2dELFFBQVEsQ0FBQ0M7WUFDNUMsSUFBSSxDQUFDakIsUUFBUSxDQUFDWSxPQUFPeEIsS0FBSztZQUMxQixPQUFPd0I7UUFDVDtRQUVBLE1BQU1sQixXQUFXLE1BQU0sSUFBSSxDQUFDVCxPQUFPLENBQWU7WUFDaEQ0QixRQUFRO1lBQ1JDLEtBQUs7WUFDTFAsTUFBTVU7UUFDUjtRQUVBLElBQUl2QixTQUFTcUIsT0FBTyxJQUFJckIsU0FBU2EsSUFBSSxFQUFFO1lBQ3JDLElBQUksQ0FBQ1AsUUFBUSxDQUFDTixTQUFTYSxJQUFJLENBQUNuQixLQUFLO1lBQ2pDLE9BQU9NLFNBQVNhLElBQUk7UUFDdEI7UUFFQSxNQUFNLElBQUlFLE1BQU1mLFNBQVNjLE9BQU8sSUFBSTtJQUN0QztJQUVBLE1BQU1VLFNBQXdCO1FBQzVCLElBQUksSUFBSSxDQUFDM0MsVUFBVSxFQUFFO1lBQ25CLE1BQU1QLHVEQUFhQSxDQUFDa0QsTUFBTTtZQUMxQixJQUFJLENBQUNoQixXQUFXO1lBQ2hCO1FBQ0Y7UUFFQSxJQUFJO1lBQ0YsTUFBTSxJQUFJLENBQUNqQixPQUFPLENBQUM7Z0JBQ2pCNEIsUUFBUTtnQkFDUkMsS0FBSztZQUNQO1FBQ0YsU0FBVTtZQUNSLElBQUksQ0FBQ1osV0FBVztRQUNsQjtJQUNGO0lBRUEsTUFBTWlCLGlCQUFnQztRQUNwQyxJQUFJLElBQUksQ0FBQzVDLFVBQVUsRUFBRTtZQUNuQixPQUFPLE1BQU1QLHVEQUFhQSxDQUFDbUQsY0FBYztRQUMzQztRQUVBLE1BQU16QixXQUFXLE1BQU0sSUFBSSxDQUFDVCxPQUFPLENBQU87WUFDeEM0QixRQUFRO1lBQ1JDLEtBQUs7UUFDUDtRQUVBLElBQUlwQixTQUFTcUIsT0FBTyxJQUFJckIsU0FBU2EsSUFBSSxFQUFFO1lBQ3JDLE9BQU9iLFNBQVNhLElBQUk7UUFDdEI7UUFFQSxNQUFNLElBQUlFLE1BQU1mLFNBQVNjLE9BQU8sSUFBSTtJQUN0QztJQUVBLGVBQWU7SUFDZixNQUFNWSxjQUFjYixJQUFtQixFQUFpQjtRQUN0RCxNQUFNYixXQUFXLE1BQU0sSUFBSSxDQUFDVCxPQUFPLENBQU87WUFDeEM0QixRQUFRO1lBQ1JDLEtBQUs7WUFDTFA7UUFDRjtRQUVBLElBQUliLFNBQVNxQixPQUFPLElBQUlyQixTQUFTYSxJQUFJLEVBQUU7WUFDckMsT0FBT2IsU0FBU2EsSUFBSTtRQUN0QjtRQUVBLE1BQU0sSUFBSUUsTUFBTWYsU0FBU2MsT0FBTyxJQUFJO0lBQ3RDO0lBRUEsTUFBTWEsZUFBdUM7UUFDM0MsTUFBTTNCLFdBQVcsTUFBTSxJQUFJLENBQUNULE9BQU8sQ0FBZ0I7WUFDakQ0QixRQUFRO1lBQ1JDLEtBQUs7UUFDUDtRQUVBLElBQUlwQixTQUFTcUIsT0FBTyxJQUFJckIsU0FBU2EsSUFBSSxFQUFFO1lBQ3JDLE9BQU9iLFNBQVNhLElBQUk7UUFDdEI7UUFFQSxNQUFNLElBQUlFLE1BQU1mLFNBQVNjLE9BQU8sSUFBSTtJQUN0QztJQUVBLG9CQUFvQjtJQUNwQixNQUFNYyx1QkFBdUJuQyxNQUF1QixFQUE2QjtRQUMvRSxJQUFJLElBQUksQ0FBQ1osVUFBVSxFQUFFO1lBQ25CLE9BQU8sTUFBTVAsdURBQWFBLENBQUNzRCxzQkFBc0IsQ0FBQ25DO1FBQ3BEO1FBRUEsTUFBTU8sV0FBVyxNQUFNLElBQUksQ0FBQ1QsT0FBTyxDQUFtQjtZQUNwRDRCLFFBQVE7WUFDUkMsS0FBSztZQUNMUCxNQUFNcEI7UUFDUjtRQUVBLElBQUlPLFNBQVNxQixPQUFPLElBQUlyQixTQUFTYSxJQUFJLEVBQUU7WUFDckMsT0FBT2IsU0FBU2EsSUFBSTtRQUN0QjtRQUVBLE1BQU0sSUFBSUUsTUFBTWYsU0FBU2MsT0FBTyxJQUFJO0lBQ3RDO0lBRUEsTUFBTWUscUJBQXFCQyxNQUkxQixFQUErQjtRQUM5QixJQUFJLElBQUksQ0FBQ2pELFVBQVUsRUFBRTtZQUNuQixPQUFPLE1BQU1QLHVEQUFhQSxDQUFDdUQsb0JBQW9CO1FBQ2pEO1FBRUEsTUFBTTdCLFdBQVcsTUFBTSxJQUFJLENBQUNULE9BQU8sQ0FBcUI7WUFDdEQ0QixRQUFRO1lBQ1JDLEtBQUs7WUFDTFU7UUFDRjtRQUVBLElBQUk5QixTQUFTcUIsT0FBTyxJQUFJckIsU0FBU2EsSUFBSSxFQUFFO1lBQ3JDLE9BQU9iLFNBQVNhLElBQUk7UUFDdEI7UUFFQSxNQUFNLElBQUlFLE1BQU1mLFNBQVNjLE9BQU8sSUFBSTtJQUN0QztJQUVBLE1BQU1pQixvQkFBb0JDLFNBQWlCLEVBQTZCO1FBQ3RFLE1BQU1oQyxXQUFXLE1BQU0sSUFBSSxDQUFDVCxPQUFPLENBQW1CO1lBQ3BENEIsUUFBUTtZQUNSQyxLQUFLLENBQUMscUJBQXFCLEVBQUVZLFVBQVUsQ0FBQztRQUMxQztRQUVBLElBQUloQyxTQUFTcUIsT0FBTyxJQUFJckIsU0FBU2EsSUFBSSxFQUFFO1lBQ3JDLE9BQU9iLFNBQVNhLElBQUk7UUFDdEI7UUFFQSxNQUFNLElBQUlFLE1BQU1mLFNBQVNjLE9BQU8sSUFBSTtJQUN0QztJQUVBLE1BQU1tQixzQkFBc0JELFNBQWlCLEVBQXFCO1FBQ2hFLE1BQU1oQyxXQUFXLE1BQU0sSUFBSSxDQUFDVCxPQUFPLENBQVc7WUFDNUM0QixRQUFRO1lBQ1JDLEtBQUssQ0FBQyxxQkFBcUIsRUFBRVksVUFBVSxNQUFNLENBQUM7UUFDaEQ7UUFFQSxJQUFJaEMsU0FBU3FCLE9BQU8sSUFBSXJCLFNBQVNhLElBQUksRUFBRTtZQUNyQyxPQUFPYixTQUFTYSxJQUFJO1FBQ3RCO1FBRUEsTUFBTSxJQUFJRSxNQUFNZixTQUFTYyxPQUFPLElBQUk7SUFDdEM7SUFFQSxNQUFNb0IsYUFBYUYsU0FBaUIsRUFBRW5CLElBTXJDLEVBQTREO1FBQzNELE1BQU1zQixXQUFXLElBQUlDO1FBQ3JCRCxTQUFTRSxNQUFNLENBQUMsY0FBY3hCLEtBQUt5QixVQUFVO1FBQzdDSCxTQUFTRSxNQUFNLENBQUMsWUFBWXhCLEtBQUswQixRQUFRLENBQUNDLFFBQVE7UUFFbEQsSUFBSTNCLEtBQUs0QixZQUFZLEVBQUU7WUFDckJOLFNBQVNFLE1BQU0sQ0FBQyxnQkFBZ0J4QixLQUFLNEIsWUFBWTtRQUNuRDtRQUVBLElBQUk1QixLQUFLNkIsU0FBUyxFQUFFO1lBQ2xCUCxTQUFTRSxNQUFNLENBQUMsU0FBU3hCLEtBQUs2QixTQUFTLEVBQUU7UUFDM0M7UUFFQSxJQUFJN0IsS0FBSzhCLFNBQVMsRUFBRTtZQUNsQlIsU0FBU0UsTUFBTSxDQUFDLFNBQVN4QixLQUFLOEIsU0FBUyxFQUFFO1FBQzNDO1FBRUEsTUFBTTNDLFdBQVcsTUFBTSxJQUFJLENBQUNULE9BQU8sQ0FBa0Q7WUFDbkY0QixRQUFRO1lBQ1JDLEtBQUssQ0FBQyxxQkFBcUIsRUFBRVksVUFBVSxRQUFRLENBQUM7WUFDaERuQixNQUFNc0I7WUFDTi9DLFNBQVM7Z0JBQ1AsZ0JBQWdCO1lBQ2xCO1FBQ0Y7UUFFQSxJQUFJWSxTQUFTcUIsT0FBTyxJQUFJckIsU0FBU2EsSUFBSSxFQUFFO1lBQ3JDLE9BQU9iLFNBQVNhLElBQUk7UUFDdEI7UUFFQSxNQUFNLElBQUlFLE1BQU1mLFNBQVNjLE9BQU8sSUFBSTtJQUN0QztJQUVBLE1BQU04Qix5QkFBeUJaLFNBQWlCLEVBQStCO1FBQzdFLE1BQU1oQyxXQUFXLE1BQU0sSUFBSSxDQUFDVCxPQUFPLENBQXFCO1lBQ3RENEIsUUFBUTtZQUNSQyxLQUFLLENBQUMscUJBQXFCLEVBQUVZLFVBQVUsU0FBUyxDQUFDO1FBQ25EO1FBRUEsSUFBSWhDLFNBQVNxQixPQUFPLElBQUlyQixTQUFTYSxJQUFJLEVBQUU7WUFDckMsT0FBT2IsU0FBU2EsSUFBSTtRQUN0QjtRQUVBLE1BQU0sSUFBSUUsTUFBTWYsU0FBU2MsT0FBTyxJQUFJO0lBQ3RDO0lBRUEsTUFBTStCLGtCQUFrQmIsU0FBaUIsRUFJdEM7UUFDRCxNQUFNaEMsV0FBVyxNQUFNLElBQUksQ0FBQ1QsT0FBTyxDQUloQztZQUNENEIsUUFBUTtZQUNSQyxLQUFLLENBQUMscUJBQXFCLEVBQUVZLFVBQVUsUUFBUSxDQUFDO1FBQ2xEO1FBRUEsSUFBSWhDLFNBQVNxQixPQUFPLElBQUlyQixTQUFTYSxJQUFJLEVBQUU7WUFDckMsT0FBT2IsU0FBU2EsSUFBSTtRQUN0QjtRQUVBLE1BQU0sSUFBSUUsTUFBTWYsU0FBU2MsT0FBTyxJQUFJO0lBQ3RDO0lBRUEsaUJBQWlCO0lBQ2pCLE1BQU1nQyxhQUFhQyxJQUFVLEVBQW1CO1FBQzlDLE1BQU1aLFdBQVcsSUFBSUM7UUFDckJELFNBQVNFLE1BQU0sQ0FBQyxVQUFVVTtRQUUxQixNQUFNL0MsV0FBVyxNQUFNLElBQUksQ0FBQ1QsT0FBTyxDQUFTO1lBQzFDNEIsUUFBUTtZQUNSQyxLQUFLO1lBQ0xQLE1BQU1zQjtZQUNOL0MsU0FBUztnQkFDUCxnQkFBZ0I7WUFDbEI7UUFDRjtRQUVBLElBQUlZLFNBQVNxQixPQUFPLElBQUlyQixTQUFTYSxJQUFJLEVBQUU7WUFDckMsT0FBT2IsU0FBU2EsSUFBSTtRQUN0QjtRQUVBLE1BQU0sSUFBSUUsTUFBTWYsU0FBU2MsT0FBTyxJQUFJO0lBQ3RDO0lBRUEsTUFBTWtDLGFBQWdDO1FBQ3BDLE1BQU1oRCxXQUFXLE1BQU0sSUFBSSxDQUFDVCxPQUFPLENBQVc7WUFDNUM0QixRQUFRO1lBQ1JDLEtBQUs7UUFDUDtRQUVBLElBQUlwQixTQUFTcUIsT0FBTyxJQUFJckIsU0FBU2EsSUFBSSxFQUFFO1lBQ3JDLE9BQU9iLFNBQVNhLElBQUk7UUFDdEI7UUFFQSxNQUFNLElBQUlFLE1BQU1mLFNBQVNjLE9BQU8sSUFBSTtJQUN0QztJQUVBLE1BQU1tQyxjQUFjQyxRQUFnQixFQUlqQztRQUNELE1BQU1sRCxXQUFXLE1BQU0sSUFBSSxDQUFDVCxPQUFPLENBSWhDO1lBQ0Q0QixRQUFRO1lBQ1JDLEtBQUssQ0FBQyxTQUFTLEVBQUU4QixTQUFTLFFBQVEsQ0FBQztRQUNyQztRQUVBLElBQUlsRCxTQUFTcUIsT0FBTyxJQUFJckIsU0FBU2EsSUFBSSxFQUFFO1lBQ3JDLE9BQU9iLFNBQVNhLElBQUk7UUFDdEI7UUFFQSxNQUFNLElBQUlFLE1BQU1mLFNBQVNjLE9BQU8sSUFBSTtJQUN0QztJQUVBLGlCQUFpQjtJQUNqQixNQUFNcUMsV0FBV3JCLE1BS2hCLEVBQTRCO1FBQzNCLE1BQU05QixXQUFXLE1BQU0sSUFBSSxDQUFDVCxPQUFPLENBQWtCO1lBQ25ENEIsUUFBUTtZQUNSQyxLQUFLO1lBQ0xVO1FBQ0Y7UUFFQSxJQUFJOUIsU0FBU3FCLE9BQU8sSUFBSXJCLFNBQVNhLElBQUksRUFBRTtZQUNyQyxPQUFPYixTQUFTYSxJQUFJO1FBQ3RCO1FBRUEsTUFBTSxJQUFJRSxNQUFNZixTQUFTYyxPQUFPLElBQUk7SUFDdEM7SUFFQSxNQUFNc0Msa0JBQWtCQyxRQUFnQixFQUFFeEMsSUFLekMsRUFBZ0I7UUFDZixNQUFNYixXQUFXLE1BQU0sSUFBSSxDQUFDVCxPQUFPLENBQUM7WUFDbEM0QixRQUFRO1lBQ1JDLEtBQUssQ0FBQyxTQUFTLEVBQUVpQyxTQUFTLEtBQUssQ0FBQztZQUNoQ3hDO1FBQ0Y7UUFFQSxJQUFJYixTQUFTcUIsT0FBTyxFQUFFO1lBQ3BCLE9BQU9yQixTQUFTYSxJQUFJO1FBQ3RCO1FBRUEsTUFBTSxJQUFJRSxNQUFNZixTQUFTYyxPQUFPLElBQUk7SUFDdEM7SUFFQSxvQkFBb0I7SUFDcEIsTUFBTXdDLGFBQWF4QixNQUlsQixFQUEwQjtRQUN6QixNQUFNOUIsV0FBVyxNQUFNLElBQUksQ0FBQ1QsT0FBTyxDQUFnQjtZQUNqRDRCLFFBQVE7WUFDUkMsS0FBSztZQUNMVTtRQUNGO1FBRUEsSUFBSTlCLFNBQVNxQixPQUFPLElBQUlyQixTQUFTYSxJQUFJLEVBQUU7WUFDckMsT0FBT2IsU0FBU2EsSUFBSTtRQUN0QjtRQUVBLE1BQU0sSUFBSUUsTUFBTWYsU0FBU2MsT0FBTyxJQUFJO0lBQ3RDO0lBRUEsYUFBYTtJQUNiLE1BQU15QyxrQkFBa0IxQyxJQU92QixFQUF1QjtRQUN0QixNQUFNYixXQUFXLE1BQU0sSUFBSSxDQUFDVCxPQUFPLENBQWE7WUFDOUM0QixRQUFRO1lBQ1JDLEtBQUs7WUFDTFA7UUFDRjtRQUVBLElBQUliLFNBQVNxQixPQUFPLElBQUlyQixTQUFTYSxJQUFJLEVBQUU7WUFDckMsT0FBT2IsU0FBU2EsSUFBSTtRQUN0QjtRQUVBLE1BQU0sSUFBSUUsTUFBTWYsU0FBU2MsT0FBTyxJQUFJO0lBQ3RDO0lBRUEsTUFBTTBDLGNBQWMzQyxJQUluQixFQUFxQjtRQUNwQixNQUFNYixXQUFXLE1BQU0sSUFBSSxDQUFDVCxPQUFPLENBQVc7WUFDNUM0QixRQUFRO1lBQ1JDLEtBQUs7WUFDTFA7UUFDRjtRQUVBLElBQUliLFNBQVNxQixPQUFPLElBQUlyQixTQUFTYSxJQUFJLEVBQUU7WUFDckMsT0FBT2IsU0FBU2EsSUFBSTtRQUN0QjtRQUVBLE1BQU0sSUFBSUUsTUFBTWYsU0FBU2MsT0FBTyxJQUFJO0lBQ3RDO0lBRUEsTUFBTTJDLGVBQWU1QyxJQUdwQixFQUFnQjtRQUNmLE1BQU1iLFdBQVcsTUFBTSxJQUFJLENBQUNULE9BQU8sQ0FBQztZQUNsQzRCLFFBQVE7WUFDUkMsS0FBSztZQUNMUDtRQUNGO1FBRUEsSUFBSWIsU0FBU3FCLE9BQU8sSUFBSXJCLFNBQVNhLElBQUksRUFBRTtZQUNyQyxPQUFPYixTQUFTYSxJQUFJO1FBQ3RCO1FBRUEsTUFBTSxJQUFJRSxNQUFNZixTQUFTYyxPQUFPLElBQUk7SUFDdEM7QUFDRjtBQUVBLHlDQUF5QztBQUNsQyxNQUFNNEMsWUFBWSxJQUFJbkYsWUFBVztBQUN4QyxpRUFBZW1GLFNBQVNBLEVBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9Ad2ViL2FwcC8uL3NyYy9saWIvYXBpLnRzPzJmYWIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGF4aW9zLCB7IEF4aW9zSW5zdGFuY2UsIEF4aW9zUmVxdWVzdENvbmZpZywgQXhpb3NSZXNwb25zZSB9IGZyb20gJ2F4aW9zJ1xyXG5pbXBvcnQgeyB0b2FzdCB9IGZyb20gJ3Nvbm5lcidcclxuaW1wb3J0IHtcclxuICBBcGlSZXNwb25zZSxcclxuICBQYWdpbmF0ZWRSZXNwb25zZSxcclxuICBMb2dpblJlcXVlc3QsXHJcbiAgUmVnaXN0ZXJSZXF1ZXN0LFxyXG4gIEF1dGhSZXNwb25zZSxcclxuICBVc2VyLFxyXG4gIEludGVydmlld1Nlc3Npb24sXHJcbiAgSW50ZXJ2aWV3Q29uZmlnLFxyXG4gIFF1ZXN0aW9uLFxyXG4gIEFuc3dlcixcclxuICBGZWVkYmFjayxcclxuICBQZXJmb3JtYW5jZU1ldHJpY3MsXHJcbiAgUmVzdW1lLFxyXG4gIEV4cGVydFByb2ZpbGUsXHJcbiAgQW5hbHl0aWNzRGF0YVxyXG59IGZyb20gJ0AvdHlwZXMnXHJcbmltcG9ydCB7IGdldEVycm9yTWVzc2FnZSwgaXNUb2tlbkV4cGlyZWQgfSBmcm9tICdAL2xpYi91dGlscydcclxuaW1wb3J0IHsgbW9ja0FwaUNsaWVudCB9IGZyb20gJ0AvbGliL21vY2tBcGknXHJcblxyXG5jbGFzcyBBcGlDbGllbnQge1xyXG4gIHByaXZhdGUgY2xpZW50OiBBeGlvc0luc3RhbmNlXHJcbiAgcHJpdmF0ZSBiYXNlVVJMOiBzdHJpbmdcclxuICBwcml2YXRlIHVzZU1vY2tBcGk6IGJvb2xlYW5cclxuXHJcbiAgY29uc3RydWN0b3IoKSB7XHJcbiAgICB0aGlzLmJhc2VVUkwgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BUElfVVJMIHx8ICdodHRwOi8vbG9jYWxob3N0OjMwMDEnXHJcbiAgICB0aGlzLnVzZU1vY2tBcGkgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19VU0VfTU9DS19BUEkgPT09ICd0cnVlJyB8fCAhcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQVBJX1VSTFxyXG5cclxuICAgIC8vIERlYnVnIGxvZ2dpbmcgKGNhbiBiZSByZW1vdmVkIGluIHByb2R1Y3Rpb24pXHJcbiAgICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdkZXZlbG9wbWVudCcpIHtcclxuICAgICAgY29uc29sZS5sb2coJ0FQSSBDbGllbnQ6IFVzaW5nIE1vY2sgQVBJID0nLCB0aGlzLnVzZU1vY2tBcGkpXHJcbiAgICB9XHJcblxyXG4gICAgdGhpcy5jbGllbnQgPSBheGlvcy5jcmVhdGUoe1xyXG4gICAgICBiYXNlVVJMOiBgJHt0aGlzLmJhc2VVUkx9L2FwaWAsXHJcbiAgICAgIHRpbWVvdXQ6IDMwMDAwLFxyXG4gICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcclxuICAgICAgfSxcclxuICAgIH0pXHJcblxyXG4gICAgdGhpcy5zZXR1cEludGVyY2VwdG9ycygpXHJcbiAgfVxyXG5cclxuICBwcml2YXRlIHNldHVwSW50ZXJjZXB0b3JzKCkge1xyXG4gICAgLy8gUmVxdWVzdCBpbnRlcmNlcHRvciB0byBhZGQgYXV0aCB0b2tlblxyXG4gICAgdGhpcy5jbGllbnQuaW50ZXJjZXB0b3JzLnJlcXVlc3QudXNlKFxyXG4gICAgICAoY29uZmlnKSA9PiB7XHJcbiAgICAgICAgY29uc3QgdG9rZW4gPSB0aGlzLmdldFRva2VuKClcclxuICAgICAgICBpZiAodG9rZW4gJiYgIWlzVG9rZW5FeHBpcmVkKHRva2VuKSkge1xyXG4gICAgICAgICAgY29uZmlnLmhlYWRlcnMuQXV0aG9yaXphdGlvbiA9IGBCZWFyZXIgJHt0b2tlbn1gXHJcbiAgICAgICAgfVxyXG4gICAgICAgIHJldHVybiBjb25maWdcclxuICAgICAgfSxcclxuICAgICAgKGVycm9yKSA9PiB7XHJcbiAgICAgICAgcmV0dXJuIFByb21pc2UucmVqZWN0KGVycm9yKVxyXG4gICAgICB9XHJcbiAgICApXHJcblxyXG4gICAgLy8gUmVzcG9uc2UgaW50ZXJjZXB0b3IgZm9yIGVycm9yIGhhbmRsaW5nXHJcbiAgICB0aGlzLmNsaWVudC5pbnRlcmNlcHRvcnMucmVzcG9uc2UudXNlKFxyXG4gICAgICAocmVzcG9uc2U6IEF4aW9zUmVzcG9uc2UpID0+IHtcclxuICAgICAgICByZXR1cm4gcmVzcG9uc2VcclxuICAgICAgfSxcclxuICAgICAgKGVycm9yKSA9PiB7XHJcbiAgICAgICAgaWYgKGVycm9yLnJlc3BvbnNlPy5zdGF0dXMgPT09IDQwMSkge1xyXG4gICAgICAgICAgdGhpcy5oYW5kbGVVbmF1dGhvcml6ZWQoKVxyXG4gICAgICAgIH0gZWxzZSBpZiAoZXJyb3IucmVzcG9uc2U/LnN0YXR1cyA+PSA1MDApIHtcclxuICAgICAgICAgIHRvYXN0LmVycm9yKCdTZXJ2ZXIgZXJyb3IuIFBsZWFzZSB0cnkgYWdhaW4gbGF0ZXIuJylcclxuICAgICAgICB9IGVsc2UgaWYgKGVycm9yLmNvZGUgPT09ICdORVRXT1JLX0VSUk9SJykge1xyXG4gICAgICAgICAgdG9hc3QuZXJyb3IoJ05ldHdvcmsgZXJyb3IuIFBsZWFzZSBjaGVjayB5b3VyIGNvbm5lY3Rpb24uJylcclxuICAgICAgICB9XHJcbiAgICAgICAgcmV0dXJuIFByb21pc2UucmVqZWN0KGVycm9yKVxyXG4gICAgICB9XHJcbiAgICApXHJcbiAgfVxyXG5cclxuICBwcml2YXRlIGdldFRva2VuKCk6IHN0cmluZyB8IG51bGwge1xyXG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XHJcbiAgICAgIHJldHVybiBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnYXV0aF90b2tlbicpXHJcbiAgICB9XHJcbiAgICByZXR1cm4gbnVsbFxyXG4gIH1cclxuXHJcbiAgcHJpdmF0ZSBzZXRUb2tlbih0b2tlbjogc3RyaW5nKTogdm9pZCB7XHJcbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcclxuICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ2F1dGhfdG9rZW4nLCB0b2tlbilcclxuICAgIH1cclxuICB9XHJcblxyXG4gIHByaXZhdGUgcmVtb3ZlVG9rZW4oKTogdm9pZCB7XHJcbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcclxuICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ2F1dGhfdG9rZW4nKVxyXG4gICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgncmVmcmVzaF90b2tlbicpXHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICBwcml2YXRlIGhhbmRsZVVuYXV0aG9yaXplZCgpOiB2b2lkIHtcclxuICAgIHRoaXMucmVtb3ZlVG9rZW4oKVxyXG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XHJcbiAgICAgIHdpbmRvdy5sb2NhdGlvbi5ocmVmID0gJy9hdXRoL2xvZ2luJ1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLy8gR2VuZXJpYyByZXF1ZXN0IG1ldGhvZFxyXG4gIHByaXZhdGUgYXN5bmMgcmVxdWVzdDxUPihjb25maWc6IEF4aW9zUmVxdWVzdENvbmZpZyk6IFByb21pc2U8QXBpUmVzcG9uc2U8VD4+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5jbGllbnQucmVxdWVzdDxBcGlSZXNwb25zZTxUPj4oY29uZmlnKVxyXG4gICAgICByZXR1cm4gcmVzcG9uc2UuZGF0YVxyXG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xyXG4gICAgICBjb25zdCBtZXNzYWdlID0gZXJyb3IucmVzcG9uc2U/LmRhdGE/Lm1lc3NhZ2UgfHwgZ2V0RXJyb3JNZXNzYWdlKGVycm9yKVxyXG4gICAgICB0aHJvdyBuZXcgRXJyb3IobWVzc2FnZSlcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8vIEF1dGhlbnRpY2F0aW9uIG1ldGhvZHNcclxuICBhc3luYyBsb2dpbihjcmVkZW50aWFsczogTG9naW5SZXF1ZXN0KTogUHJvbWlzZTxBdXRoUmVzcG9uc2U+IHtcclxuICAgIGlmICh0aGlzLnVzZU1vY2tBcGkpIHtcclxuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgbW9ja0FwaUNsaWVudC5sb2dpbihjcmVkZW50aWFscylcclxuICAgICAgdGhpcy5zZXRUb2tlbihyZXN1bHQudG9rZW4pXHJcbiAgICAgIHJldHVybiByZXN1bHRcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMucmVxdWVzdDxBdXRoUmVzcG9uc2U+KHtcclxuICAgICAgbWV0aG9kOiAnUE9TVCcsXHJcbiAgICAgIHVybDogJy9hdXRoL2xvZ2luJyxcclxuICAgICAgZGF0YTogY3JlZGVudGlhbHMsXHJcbiAgICB9KVxyXG5cclxuICAgIGlmIChyZXNwb25zZS5zdWNjZXNzICYmIHJlc3BvbnNlLmRhdGEpIHtcclxuICAgICAgdGhpcy5zZXRUb2tlbihyZXNwb25zZS5kYXRhLnRva2VuKVxyXG4gICAgICByZXR1cm4gcmVzcG9uc2UuZGF0YVxyXG4gICAgfVxyXG5cclxuICAgIHRocm93IG5ldyBFcnJvcihyZXNwb25zZS5tZXNzYWdlIHx8ICdMb2dpbiBmYWlsZWQnKVxyXG4gIH1cclxuXHJcbiAgYXN5bmMgcmVnaXN0ZXIodXNlckRhdGE6IFJlZ2lzdGVyUmVxdWVzdCk6IFByb21pc2U8QXV0aFJlc3BvbnNlPiB7XHJcbiAgICBpZiAodGhpcy51c2VNb2NrQXBpKSB7XHJcbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IG1vY2tBcGlDbGllbnQucmVnaXN0ZXIodXNlckRhdGEpXHJcbiAgICAgIHRoaXMuc2V0VG9rZW4ocmVzdWx0LnRva2VuKVxyXG4gICAgICByZXR1cm4gcmVzdWx0XHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLnJlcXVlc3Q8QXV0aFJlc3BvbnNlPih7XHJcbiAgICAgIG1ldGhvZDogJ1BPU1QnLFxyXG4gICAgICB1cmw6ICcvYXV0aC9yZWdpc3RlcicsXHJcbiAgICAgIGRhdGE6IHVzZXJEYXRhLFxyXG4gICAgfSlcclxuXHJcbiAgICBpZiAocmVzcG9uc2Uuc3VjY2VzcyAmJiByZXNwb25zZS5kYXRhKSB7XHJcbiAgICAgIHRoaXMuc2V0VG9rZW4ocmVzcG9uc2UuZGF0YS50b2tlbilcclxuICAgICAgcmV0dXJuIHJlc3BvbnNlLmRhdGFcclxuICAgIH1cclxuXHJcbiAgICB0aHJvdyBuZXcgRXJyb3IocmVzcG9uc2UubWVzc2FnZSB8fCAnUmVnaXN0cmF0aW9uIGZhaWxlZCcpXHJcbiAgfVxyXG5cclxuICBhc3luYyBsb2dvdXQoKTogUHJvbWlzZTx2b2lkPiB7XHJcbiAgICBpZiAodGhpcy51c2VNb2NrQXBpKSB7XHJcbiAgICAgIGF3YWl0IG1vY2tBcGlDbGllbnQubG9nb3V0KClcclxuICAgICAgdGhpcy5yZW1vdmVUb2tlbigpXHJcbiAgICAgIHJldHVyblxyXG4gICAgfVxyXG5cclxuICAgIHRyeSB7XHJcbiAgICAgIGF3YWl0IHRoaXMucmVxdWVzdCh7XHJcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXHJcbiAgICAgICAgdXJsOiAnL2F1dGgvbG9nb3V0JyxcclxuICAgICAgfSlcclxuICAgIH0gZmluYWxseSB7XHJcbiAgICAgIHRoaXMucmVtb3ZlVG9rZW4oKVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgYXN5bmMgZ2V0Q3VycmVudFVzZXIoKTogUHJvbWlzZTxVc2VyPiB7XHJcbiAgICBpZiAodGhpcy51c2VNb2NrQXBpKSB7XHJcbiAgICAgIHJldHVybiBhd2FpdCBtb2NrQXBpQ2xpZW50LmdldEN1cnJlbnRVc2VyKClcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMucmVxdWVzdDxVc2VyPih7XHJcbiAgICAgIG1ldGhvZDogJ0dFVCcsXHJcbiAgICAgIHVybDogJy9hdXRoL21lJyxcclxuICAgIH0pXHJcblxyXG4gICAgaWYgKHJlc3BvbnNlLnN1Y2Nlc3MgJiYgcmVzcG9uc2UuZGF0YSkge1xyXG4gICAgICByZXR1cm4gcmVzcG9uc2UuZGF0YVxyXG4gICAgfVxyXG5cclxuICAgIHRocm93IG5ldyBFcnJvcihyZXNwb25zZS5tZXNzYWdlIHx8ICdGYWlsZWQgdG8gZ2V0IHVzZXIgZGF0YScpXHJcbiAgfVxyXG5cclxuICAvLyBVc2VyIG1ldGhvZHNcclxuICBhc3luYyB1cGRhdGVQcm9maWxlKGRhdGE6IFBhcnRpYWw8VXNlcj4pOiBQcm9taXNlPFVzZXI+IHtcclxuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5yZXF1ZXN0PFVzZXI+KHtcclxuICAgICAgbWV0aG9kOiAnUFVUJyxcclxuICAgICAgdXJsOiAnL3VzZXJzL21lJyxcclxuICAgICAgZGF0YSxcclxuICAgIH0pXHJcblxyXG4gICAgaWYgKHJlc3BvbnNlLnN1Y2Nlc3MgJiYgcmVzcG9uc2UuZGF0YSkge1xyXG4gICAgICByZXR1cm4gcmVzcG9uc2UuZGF0YVxyXG4gICAgfVxyXG5cclxuICAgIHRocm93IG5ldyBFcnJvcihyZXNwb25zZS5tZXNzYWdlIHx8ICdGYWlsZWQgdG8gdXBkYXRlIHByb2ZpbGUnKVxyXG4gIH1cclxuXHJcbiAgYXN5bmMgZ2V0VXNlclN0YXRzKCk6IFByb21pc2U8QW5hbHl0aWNzRGF0YT4ge1xyXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLnJlcXVlc3Q8QW5hbHl0aWNzRGF0YT4oe1xyXG4gICAgICBtZXRob2Q6ICdHRVQnLFxyXG4gICAgICB1cmw6ICcvdXNlcnMvbWUvc3RhdHMnLFxyXG4gICAgfSlcclxuXHJcbiAgICBpZiAocmVzcG9uc2Uuc3VjY2VzcyAmJiByZXNwb25zZS5kYXRhKSB7XHJcbiAgICAgIHJldHVybiByZXNwb25zZS5kYXRhXHJcbiAgICB9XHJcblxyXG4gICAgdGhyb3cgbmV3IEVycm9yKHJlc3BvbnNlLm1lc3NhZ2UgfHwgJ0ZhaWxlZCB0byBnZXQgdXNlciBzdGF0cycpXHJcbiAgfVxyXG5cclxuICAvLyBJbnRlcnZpZXcgbWV0aG9kc1xyXG4gIGFzeW5jIGNyZWF0ZUludGVydmlld1Nlc3Npb24oY29uZmlnOiBJbnRlcnZpZXdDb25maWcpOiBQcm9taXNlPEludGVydmlld1Nlc3Npb24+IHtcclxuICAgIGlmICh0aGlzLnVzZU1vY2tBcGkpIHtcclxuICAgICAgcmV0dXJuIGF3YWl0IG1vY2tBcGlDbGllbnQuY3JlYXRlSW50ZXJ2aWV3U2Vzc2lvbihjb25maWcpXHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLnJlcXVlc3Q8SW50ZXJ2aWV3U2Vzc2lvbj4oe1xyXG4gICAgICBtZXRob2Q6ICdQT1NUJyxcclxuICAgICAgdXJsOiAnL2ludGVydmlld3Mvc2Vzc2lvbnMnLFxyXG4gICAgICBkYXRhOiBjb25maWcsXHJcbiAgICB9KVxyXG5cclxuICAgIGlmIChyZXNwb25zZS5zdWNjZXNzICYmIHJlc3BvbnNlLmRhdGEpIHtcclxuICAgICAgcmV0dXJuIHJlc3BvbnNlLmRhdGFcclxuICAgIH1cclxuXHJcbiAgICB0aHJvdyBuZXcgRXJyb3IocmVzcG9uc2UubWVzc2FnZSB8fCAnRmFpbGVkIHRvIGNyZWF0ZSBpbnRlcnZpZXcgc2Vzc2lvbicpXHJcbiAgfVxyXG5cclxuICBhc3luYyBnZXRJbnRlcnZpZXdTZXNzaW9ucyhwYXJhbXM/OiB7XHJcbiAgICBzdGF0dXM/OiBzdHJpbmdcclxuICAgIGxpbWl0PzogbnVtYmVyXHJcbiAgICBvZmZzZXQ/OiBudW1iZXJcclxuICB9KTogUHJvbWlzZTxJbnRlcnZpZXdTZXNzaW9uW10+IHtcclxuICAgIGlmICh0aGlzLnVzZU1vY2tBcGkpIHtcclxuICAgICAgcmV0dXJuIGF3YWl0IG1vY2tBcGlDbGllbnQuZ2V0SW50ZXJ2aWV3U2Vzc2lvbnMoKVxyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5yZXF1ZXN0PEludGVydmlld1Nlc3Npb25bXT4oe1xyXG4gICAgICBtZXRob2Q6ICdHRVQnLFxyXG4gICAgICB1cmw6ICcvaW50ZXJ2aWV3cy9zZXNzaW9ucycsXHJcbiAgICAgIHBhcmFtcyxcclxuICAgIH0pXHJcblxyXG4gICAgaWYgKHJlc3BvbnNlLnN1Y2Nlc3MgJiYgcmVzcG9uc2UuZGF0YSkge1xyXG4gICAgICByZXR1cm4gcmVzcG9uc2UuZGF0YVxyXG4gICAgfVxyXG5cclxuICAgIHRocm93IG5ldyBFcnJvcihyZXNwb25zZS5tZXNzYWdlIHx8ICdGYWlsZWQgdG8gZ2V0IGludGVydmlldyBzZXNzaW9ucycpXHJcbiAgfVxyXG5cclxuICBhc3luYyBnZXRJbnRlcnZpZXdTZXNzaW9uKHNlc3Npb25JZDogc3RyaW5nKTogUHJvbWlzZTxJbnRlcnZpZXdTZXNzaW9uPiB7XHJcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMucmVxdWVzdDxJbnRlcnZpZXdTZXNzaW9uPih7XHJcbiAgICAgIG1ldGhvZDogJ0dFVCcsXHJcbiAgICAgIHVybDogYC9pbnRlcnZpZXdzL3Nlc3Npb25zLyR7c2Vzc2lvbklkfWAsXHJcbiAgICB9KVxyXG5cclxuICAgIGlmIChyZXNwb25zZS5zdWNjZXNzICYmIHJlc3BvbnNlLmRhdGEpIHtcclxuICAgICAgcmV0dXJuIHJlc3BvbnNlLmRhdGFcclxuICAgIH1cclxuXHJcbiAgICB0aHJvdyBuZXcgRXJyb3IocmVzcG9uc2UubWVzc2FnZSB8fCAnRmFpbGVkIHRvIGdldCBpbnRlcnZpZXcgc2Vzc2lvbicpXHJcbiAgfVxyXG5cclxuICBhc3luYyBzdGFydEludGVydmlld1Nlc3Npb24oc2Vzc2lvbklkOiBzdHJpbmcpOiBQcm9taXNlPFF1ZXN0aW9uPiB7XHJcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMucmVxdWVzdDxRdWVzdGlvbj4oe1xyXG4gICAgICBtZXRob2Q6ICdQVVQnLFxyXG4gICAgICB1cmw6IGAvaW50ZXJ2aWV3cy9zZXNzaW9ucy8ke3Nlc3Npb25JZH0vc3RhcnRgLFxyXG4gICAgfSlcclxuXHJcbiAgICBpZiAocmVzcG9uc2Uuc3VjY2VzcyAmJiByZXNwb25zZS5kYXRhKSB7XHJcbiAgICAgIHJldHVybiByZXNwb25zZS5kYXRhXHJcbiAgICB9XHJcblxyXG4gICAgdGhyb3cgbmV3IEVycm9yKHJlc3BvbnNlLm1lc3NhZ2UgfHwgJ0ZhaWxlZCB0byBzdGFydCBpbnRlcnZpZXcgc2Vzc2lvbicpXHJcbiAgfVxyXG5cclxuICBhc3luYyBzdWJtaXRBbnN3ZXIoc2Vzc2lvbklkOiBzdHJpbmcsIGRhdGE6IHtcclxuICAgIHF1ZXN0aW9uSWQ6IHN0cmluZ1xyXG4gICAgdGV4dFJlc3BvbnNlPzogc3RyaW5nXHJcbiAgICBhdWRpb0Jsb2I/OiBCbG9iXHJcbiAgICB2aWRlb0Jsb2I/OiBCbG9iXHJcbiAgICBkdXJhdGlvbjogbnVtYmVyXHJcbiAgfSk6IFByb21pc2U8eyBmZWVkYmFjazogRmVlZGJhY2s7IG5leHRRdWVzdGlvbj86IFF1ZXN0aW9uIH0+IHtcclxuICAgIGNvbnN0IGZvcm1EYXRhID0gbmV3IEZvcm1EYXRhKClcclxuICAgIGZvcm1EYXRhLmFwcGVuZCgncXVlc3Rpb25JZCcsIGRhdGEucXVlc3Rpb25JZClcclxuICAgIGZvcm1EYXRhLmFwcGVuZCgnZHVyYXRpb24nLCBkYXRhLmR1cmF0aW9uLnRvU3RyaW5nKCkpXHJcblxyXG4gICAgaWYgKGRhdGEudGV4dFJlc3BvbnNlKSB7XHJcbiAgICAgIGZvcm1EYXRhLmFwcGVuZCgndGV4dFJlc3BvbnNlJywgZGF0YS50ZXh0UmVzcG9uc2UpXHJcbiAgICB9XHJcblxyXG4gICAgaWYgKGRhdGEuYXVkaW9CbG9iKSB7XHJcbiAgICAgIGZvcm1EYXRhLmFwcGVuZCgnYXVkaW8nLCBkYXRhLmF1ZGlvQmxvYiwgJ2Fuc3dlci53ZWJtJylcclxuICAgIH1cclxuXHJcbiAgICBpZiAoZGF0YS52aWRlb0Jsb2IpIHtcclxuICAgICAgZm9ybURhdGEuYXBwZW5kKCd2aWRlbycsIGRhdGEudmlkZW9CbG9iLCAnYW5zd2VyLndlYm0nKVxyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5yZXF1ZXN0PHsgZmVlZGJhY2s6IEZlZWRiYWNrOyBuZXh0UXVlc3Rpb24/OiBRdWVzdGlvbiB9Pih7XHJcbiAgICAgIG1ldGhvZDogJ1BPU1QnLFxyXG4gICAgICB1cmw6IGAvaW50ZXJ2aWV3cy9zZXNzaW9ucy8ke3Nlc3Npb25JZH0vYW5zd2Vyc2AsXHJcbiAgICAgIGRhdGE6IGZvcm1EYXRhLFxyXG4gICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdtdWx0aXBhcnQvZm9ybS1kYXRhJyxcclxuICAgICAgfSxcclxuICAgIH0pXHJcblxyXG4gICAgaWYgKHJlc3BvbnNlLnN1Y2Nlc3MgJiYgcmVzcG9uc2UuZGF0YSkge1xyXG4gICAgICByZXR1cm4gcmVzcG9uc2UuZGF0YVxyXG4gICAgfVxyXG5cclxuICAgIHRocm93IG5ldyBFcnJvcihyZXNwb25zZS5tZXNzYWdlIHx8ICdGYWlsZWQgdG8gc3VibWl0IGFuc3dlcicpXHJcbiAgfVxyXG5cclxuICBhc3luYyBjb21wbGV0ZUludGVydmlld1Nlc3Npb24oc2Vzc2lvbklkOiBzdHJpbmcpOiBQcm9taXNlPFBlcmZvcm1hbmNlTWV0cmljcz4ge1xyXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLnJlcXVlc3Q8UGVyZm9ybWFuY2VNZXRyaWNzPih7XHJcbiAgICAgIG1ldGhvZDogJ1BVVCcsXHJcbiAgICAgIHVybDogYC9pbnRlcnZpZXdzL3Nlc3Npb25zLyR7c2Vzc2lvbklkfS9jb21wbGV0ZWAsXHJcbiAgICB9KVxyXG5cclxuICAgIGlmIChyZXNwb25zZS5zdWNjZXNzICYmIHJlc3BvbnNlLmRhdGEpIHtcclxuICAgICAgcmV0dXJuIHJlc3BvbnNlLmRhdGFcclxuICAgIH1cclxuXHJcbiAgICB0aHJvdyBuZXcgRXJyb3IocmVzcG9uc2UubWVzc2FnZSB8fCAnRmFpbGVkIHRvIGNvbXBsZXRlIGludGVydmlldyBzZXNzaW9uJylcclxuICB9XHJcblxyXG4gIGFzeW5jIGdldFNlc3Npb25SZXN1bHRzKHNlc3Npb25JZDogc3RyaW5nKTogUHJvbWlzZTx7XHJcbiAgICBzZXNzaW9uOiBJbnRlcnZpZXdTZXNzaW9uXHJcbiAgICBtZXRyaWNzOiBQZXJmb3JtYW5jZU1ldHJpY3NcclxuICAgIGZlZWRiYWNrOiBGZWVkYmFja1tdXHJcbiAgfT4ge1xyXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLnJlcXVlc3Q8e1xyXG4gICAgICBzZXNzaW9uOiBJbnRlcnZpZXdTZXNzaW9uXHJcbiAgICAgIG1ldHJpY3M6IFBlcmZvcm1hbmNlTWV0cmljc1xyXG4gICAgICBmZWVkYmFjazogRmVlZGJhY2tbXVxyXG4gICAgfT4oe1xyXG4gICAgICBtZXRob2Q6ICdHRVQnLFxyXG4gICAgICB1cmw6IGAvaW50ZXJ2aWV3cy9zZXNzaW9ucy8ke3Nlc3Npb25JZH0vcmVzdWx0c2AsXHJcbiAgICB9KVxyXG5cclxuICAgIGlmIChyZXNwb25zZS5zdWNjZXNzICYmIHJlc3BvbnNlLmRhdGEpIHtcclxuICAgICAgcmV0dXJuIHJlc3BvbnNlLmRhdGFcclxuICAgIH1cclxuXHJcbiAgICB0aHJvdyBuZXcgRXJyb3IocmVzcG9uc2UubWVzc2FnZSB8fCAnRmFpbGVkIHRvIGdldCBzZXNzaW9uIHJlc3VsdHMnKVxyXG4gIH1cclxuXHJcbiAgLy8gUmVzdW1lIG1ldGhvZHNcclxuICBhc3luYyB1cGxvYWRSZXN1bWUoZmlsZTogRmlsZSk6IFByb21pc2U8UmVzdW1lPiB7XHJcbiAgICBjb25zdCBmb3JtRGF0YSA9IG5ldyBGb3JtRGF0YSgpXHJcbiAgICBmb3JtRGF0YS5hcHBlbmQoJ3Jlc3VtZScsIGZpbGUpXHJcblxyXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLnJlcXVlc3Q8UmVzdW1lPih7XHJcbiAgICAgIG1ldGhvZDogJ1BPU1QnLFxyXG4gICAgICB1cmw6ICcvcmVzdW1lcy91cGxvYWQnLFxyXG4gICAgICBkYXRhOiBmb3JtRGF0YSxcclxuICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgICdDb250ZW50LVR5cGUnOiAnbXVsdGlwYXJ0L2Zvcm0tZGF0YScsXHJcbiAgICAgIH0sXHJcbiAgICB9KVxyXG5cclxuICAgIGlmIChyZXNwb25zZS5zdWNjZXNzICYmIHJlc3BvbnNlLmRhdGEpIHtcclxuICAgICAgcmV0dXJuIHJlc3BvbnNlLmRhdGFcclxuICAgIH1cclxuXHJcbiAgICB0aHJvdyBuZXcgRXJyb3IocmVzcG9uc2UubWVzc2FnZSB8fCAnRmFpbGVkIHRvIHVwbG9hZCByZXN1bWUnKVxyXG4gIH1cclxuXHJcbiAgYXN5bmMgZ2V0UmVzdW1lcygpOiBQcm9taXNlPFJlc3VtZVtdPiB7XHJcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMucmVxdWVzdDxSZXN1bWVbXT4oe1xyXG4gICAgICBtZXRob2Q6ICdHRVQnLFxyXG4gICAgICB1cmw6ICcvcmVzdW1lcycsXHJcbiAgICB9KVxyXG5cclxuICAgIGlmIChyZXNwb25zZS5zdWNjZXNzICYmIHJlc3BvbnNlLmRhdGEpIHtcclxuICAgICAgcmV0dXJuIHJlc3BvbnNlLmRhdGFcclxuICAgIH1cclxuXHJcbiAgICB0aHJvdyBuZXcgRXJyb3IocmVzcG9uc2UubWVzc2FnZSB8fCAnRmFpbGVkIHRvIGdldCByZXN1bWVzJylcclxuICB9XHJcblxyXG4gIGFzeW5jIGFuYWx5emVSZXN1bWUocmVzdW1lSWQ6IHN0cmluZyk6IFByb21pc2U8e1xyXG4gICAgYXRzU2NvcmU6IG51bWJlclxyXG4gICAga2V5d29yZHM6IHN0cmluZ1tdXHJcbiAgICBzdWdnZXN0aW9uczogc3RyaW5nW11cclxuICB9PiB7XHJcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMucmVxdWVzdDx7XHJcbiAgICAgIGF0c1Njb3JlOiBudW1iZXJcclxuICAgICAga2V5d29yZHM6IHN0cmluZ1tdXHJcbiAgICAgIHN1Z2dlc3Rpb25zOiBzdHJpbmdbXVxyXG4gICAgfT4oe1xyXG4gICAgICBtZXRob2Q6ICdQT1NUJyxcclxuICAgICAgdXJsOiBgL3Jlc3VtZXMvJHtyZXN1bWVJZH0vYW5hbHl6ZWAsXHJcbiAgICB9KVxyXG5cclxuICAgIGlmIChyZXNwb25zZS5zdWNjZXNzICYmIHJlc3BvbnNlLmRhdGEpIHtcclxuICAgICAgcmV0dXJuIHJlc3BvbnNlLmRhdGFcclxuICAgIH1cclxuXHJcbiAgICB0aHJvdyBuZXcgRXJyb3IocmVzcG9uc2UubWVzc2FnZSB8fCAnRmFpbGVkIHRvIGFuYWx5emUgcmVzdW1lJylcclxuICB9XHJcblxyXG4gIC8vIEV4cGVydCBtZXRob2RzXHJcbiAgYXN5bmMgZ2V0RXhwZXJ0cyhwYXJhbXM/OiB7XHJcbiAgICBleHBlcnRpc2U/OiBzdHJpbmdbXVxyXG4gICAgbWluUmF0aW5nPzogbnVtYmVyXHJcbiAgICBtYXhSYXRlPzogbnVtYmVyXHJcbiAgICBhdmFpbGFiaWxpdHk/OiBzdHJpbmdcclxuICB9KTogUHJvbWlzZTxFeHBlcnRQcm9maWxlW10+IHtcclxuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5yZXF1ZXN0PEV4cGVydFByb2ZpbGVbXT4oe1xyXG4gICAgICBtZXRob2Q6ICdHRVQnLFxyXG4gICAgICB1cmw6ICcvZXhwZXJ0cycsXHJcbiAgICAgIHBhcmFtcyxcclxuICAgIH0pXHJcblxyXG4gICAgaWYgKHJlc3BvbnNlLnN1Y2Nlc3MgJiYgcmVzcG9uc2UuZGF0YSkge1xyXG4gICAgICByZXR1cm4gcmVzcG9uc2UuZGF0YVxyXG4gICAgfVxyXG5cclxuICAgIHRocm93IG5ldyBFcnJvcihyZXNwb25zZS5tZXNzYWdlIHx8ICdGYWlsZWQgdG8gZ2V0IGV4cGVydHMnKVxyXG4gIH1cclxuXHJcbiAgYXN5bmMgYm9va0V4cGVydFNlc3Npb24oZXhwZXJ0SWQ6IHN0cmluZywgZGF0YToge1xyXG4gICAgc2Vzc2lvbklkOiBzdHJpbmdcclxuICAgIHNjaGVkdWxlZEF0OiBzdHJpbmdcclxuICAgIGR1cmF0aW9uOiBudW1iZXJcclxuICAgIG5vdGVzPzogc3RyaW5nXHJcbiAgfSk6IFByb21pc2U8YW55PiB7XHJcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMucmVxdWVzdCh7XHJcbiAgICAgIG1ldGhvZDogJ1BPU1QnLFxyXG4gICAgICB1cmw6IGAvZXhwZXJ0cy8ke2V4cGVydElkfS9ib29rYCxcclxuICAgICAgZGF0YSxcclxuICAgIH0pXHJcblxyXG4gICAgaWYgKHJlc3BvbnNlLnN1Y2Nlc3MpIHtcclxuICAgICAgcmV0dXJuIHJlc3BvbnNlLmRhdGFcclxuICAgIH1cclxuXHJcbiAgICB0aHJvdyBuZXcgRXJyb3IocmVzcG9uc2UubWVzc2FnZSB8fCAnRmFpbGVkIHRvIGJvb2sgZXhwZXJ0IHNlc3Npb24nKVxyXG4gIH1cclxuXHJcbiAgLy8gQW5hbHl0aWNzIG1ldGhvZHNcclxuICBhc3luYyBnZXRBbmFseXRpY3MocGFyYW1zPzoge1xyXG4gICAgc3RhcnREYXRlPzogc3RyaW5nXHJcbiAgICBlbmREYXRlPzogc3RyaW5nXHJcbiAgICBjYXRlZ29yeT86IHN0cmluZ1xyXG4gIH0pOiBQcm9taXNlPEFuYWx5dGljc0RhdGE+IHtcclxuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5yZXF1ZXN0PEFuYWx5dGljc0RhdGE+KHtcclxuICAgICAgbWV0aG9kOiAnR0VUJyxcclxuICAgICAgdXJsOiAnL2FuYWx5dGljcy91c2VyJyxcclxuICAgICAgcGFyYW1zLFxyXG4gICAgfSlcclxuXHJcbiAgICBpZiAocmVzcG9uc2Uuc3VjY2VzcyAmJiByZXNwb25zZS5kYXRhKSB7XHJcbiAgICAgIHJldHVybiByZXNwb25zZS5kYXRhXHJcbiAgICB9XHJcblxyXG4gICAgdGhyb3cgbmV3IEVycm9yKHJlc3BvbnNlLm1lc3NhZ2UgfHwgJ0ZhaWxlZCB0byBnZXQgYW5hbHl0aWNzJylcclxuICB9XHJcblxyXG4gIC8vIEFJIG1ldGhvZHNcclxuICBhc3luYyBnZW5lcmF0ZVF1ZXN0aW9ucyhkYXRhOiB7XHJcbiAgICBqb2JUaXRsZTogc3RyaW5nXHJcbiAgICBjb21wYW55Pzogc3RyaW5nXHJcbiAgICBqb2JEZXNjcmlwdGlvbj86IHN0cmluZ1xyXG4gICAgcXVlc3Rpb25UeXBlczogc3RyaW5nW11cclxuICAgIGRpZmZpY3VsdHk6IHN0cmluZ1xyXG4gICAgY291bnQ6IG51bWJlclxyXG4gIH0pOiBQcm9taXNlPFF1ZXN0aW9uW10+IHtcclxuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5yZXF1ZXN0PFF1ZXN0aW9uW10+KHtcclxuICAgICAgbWV0aG9kOiAnUE9TVCcsXHJcbiAgICAgIHVybDogJy9haS9xdWVzdGlvbnMnLFxyXG4gICAgICBkYXRhLFxyXG4gICAgfSlcclxuXHJcbiAgICBpZiAocmVzcG9uc2Uuc3VjY2VzcyAmJiByZXNwb25zZS5kYXRhKSB7XHJcbiAgICAgIHJldHVybiByZXNwb25zZS5kYXRhXHJcbiAgICB9XHJcblxyXG4gICAgdGhyb3cgbmV3IEVycm9yKHJlc3BvbnNlLm1lc3NhZ2UgfHwgJ0ZhaWxlZCB0byBnZW5lcmF0ZSBxdWVzdGlvbnMnKVxyXG4gIH1cclxuXHJcbiAgYXN5bmMgYW5hbHl6ZUFuc3dlcihkYXRhOiB7XHJcbiAgICBxdWVzdGlvbklkOiBzdHJpbmdcclxuICAgIGFuc3dlcjogc3RyaW5nXHJcbiAgICBhdWRpb1VybD86IHN0cmluZ1xyXG4gIH0pOiBQcm9taXNlPEZlZWRiYWNrPiB7XHJcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMucmVxdWVzdDxGZWVkYmFjaz4oe1xyXG4gICAgICBtZXRob2Q6ICdQT1NUJyxcclxuICAgICAgdXJsOiAnL2FpL2FuYWx5emUtYW5zd2VyJyxcclxuICAgICAgZGF0YSxcclxuICAgIH0pXHJcblxyXG4gICAgaWYgKHJlc3BvbnNlLnN1Y2Nlc3MgJiYgcmVzcG9uc2UuZGF0YSkge1xyXG4gICAgICByZXR1cm4gcmVzcG9uc2UuZGF0YVxyXG4gICAgfVxyXG5cclxuICAgIHRocm93IG5ldyBFcnJvcihyZXNwb25zZS5tZXNzYWdlIHx8ICdGYWlsZWQgdG8gYW5hbHl6ZSBhbnN3ZXInKVxyXG4gIH1cclxuXHJcbiAgYXN5bmMgYW5hbHl6ZUVtb3Rpb24oZGF0YToge1xyXG4gICAgYXVkaW9Vcmw/OiBzdHJpbmdcclxuICAgIHZpZGVvVXJsPzogc3RyaW5nXHJcbiAgfSk6IFByb21pc2U8YW55PiB7XHJcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMucmVxdWVzdCh7XHJcbiAgICAgIG1ldGhvZDogJ1BPU1QnLFxyXG4gICAgICB1cmw6ICcvYWkvYW5hbHl6ZS1lbW90aW9uJyxcclxuICAgICAgZGF0YSxcclxuICAgIH0pXHJcblxyXG4gICAgaWYgKHJlc3BvbnNlLnN1Y2Nlc3MgJiYgcmVzcG9uc2UuZGF0YSkge1xyXG4gICAgICByZXR1cm4gcmVzcG9uc2UuZGF0YVxyXG4gICAgfVxyXG5cclxuICAgIHRocm93IG5ldyBFcnJvcihyZXNwb25zZS5tZXNzYWdlIHx8ICdGYWlsZWQgdG8gYW5hbHl6ZSBlbW90aW9uJylcclxuICB9XHJcbn1cclxuXHJcbi8vIENyZWF0ZSBhbmQgZXhwb3J0IGEgc2luZ2xldG9uIGluc3RhbmNlXHJcbmV4cG9ydCBjb25zdCBhcGlDbGllbnQgPSBuZXcgQXBpQ2xpZW50KClcclxuZXhwb3J0IGRlZmF1bHQgYXBpQ2xpZW50Il0sIm5hbWVzIjpbImF4aW9zIiwidG9hc3QiLCJnZXRFcnJvck1lc3NhZ2UiLCJpc1Rva2VuRXhwaXJlZCIsIm1vY2tBcGlDbGllbnQiLCJBcGlDbGllbnQiLCJjb25zdHJ1Y3RvciIsImJhc2VVUkwiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfQVBJX1VSTCIsInVzZU1vY2tBcGkiLCJORVhUX1BVQkxJQ19VU0VfTU9DS19BUEkiLCJjb25zb2xlIiwibG9nIiwiY2xpZW50IiwiY3JlYXRlIiwidGltZW91dCIsImhlYWRlcnMiLCJzZXR1cEludGVyY2VwdG9ycyIsImludGVyY2VwdG9ycyIsInJlcXVlc3QiLCJ1c2UiLCJjb25maWciLCJ0b2tlbiIsImdldFRva2VuIiwiQXV0aG9yaXphdGlvbiIsImVycm9yIiwiUHJvbWlzZSIsInJlamVjdCIsInJlc3BvbnNlIiwic3RhdHVzIiwiaGFuZGxlVW5hdXRob3JpemVkIiwiY29kZSIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJzZXRUb2tlbiIsInNldEl0ZW0iLCJyZW1vdmVUb2tlbiIsInJlbW92ZUl0ZW0iLCJ3aW5kb3ciLCJsb2NhdGlvbiIsImhyZWYiLCJkYXRhIiwibWVzc2FnZSIsIkVycm9yIiwibG9naW4iLCJjcmVkZW50aWFscyIsInJlc3VsdCIsIm1ldGhvZCIsInVybCIsInN1Y2Nlc3MiLCJyZWdpc3RlciIsInVzZXJEYXRhIiwibG9nb3V0IiwiZ2V0Q3VycmVudFVzZXIiLCJ1cGRhdGVQcm9maWxlIiwiZ2V0VXNlclN0YXRzIiwiY3JlYXRlSW50ZXJ2aWV3U2Vzc2lvbiIsImdldEludGVydmlld1Nlc3Npb25zIiwicGFyYW1zIiwiZ2V0SW50ZXJ2aWV3U2Vzc2lvbiIsInNlc3Npb25JZCIsInN0YXJ0SW50ZXJ2aWV3U2Vzc2lvbiIsInN1Ym1pdEFuc3dlciIsImZvcm1EYXRhIiwiRm9ybURhdGEiLCJhcHBlbmQiLCJxdWVzdGlvbklkIiwiZHVyYXRpb24iLCJ0b1N0cmluZyIsInRleHRSZXNwb25zZSIsImF1ZGlvQmxvYiIsInZpZGVvQmxvYiIsImNvbXBsZXRlSW50ZXJ2aWV3U2Vzc2lvbiIsImdldFNlc3Npb25SZXN1bHRzIiwidXBsb2FkUmVzdW1lIiwiZmlsZSIsImdldFJlc3VtZXMiLCJhbmFseXplUmVzdW1lIiwicmVzdW1lSWQiLCJnZXRFeHBlcnRzIiwiYm9va0V4cGVydFNlc3Npb24iLCJleHBlcnRJZCIsImdldEFuYWx5dGljcyIsImdlbmVyYXRlUXVlc3Rpb25zIiwiYW5hbHl6ZUFuc3dlciIsImFuYWx5emVFbW90aW9uIiwiYXBpQ2xpZW50Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/mockApi.ts":
/*!****************************!*\
  !*** ./src/lib/mockApi.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MockApiClient: () => (/* binding */ MockApiClient),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   mockApiClient: () => (/* binding */ mockApiClient)\n/* harmony export */ });\n// Mock data\nconst mockUser = {\n    id: \"user-123\",\n    email: \"<EMAIL>\",\n    firstName: \"John\",\n    lastName: \"Doe\",\n    avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face\",\n    role: \"user\",\n    createdAt: new Date(\"2024-01-01\"),\n    updatedAt: new Date(),\n    preferences: {\n        notifications: true,\n        theme: \"light\",\n        language: \"en\"\n    }\n};\nconst mockSessions = [\n    {\n        id: \"session-1\",\n        userId: \"user-123\",\n        jobTitle: \"Senior Software Engineer\",\n        company: \"Google\",\n        industry: \"Technology\",\n        status: \"completed\",\n        createdAt: new Date(\"2024-01-15\"),\n        updatedAt: new Date(\"2024-01-15\"),\n        config: {\n            duration: 45,\n            questionTypes: [\n                \"technical\",\n                \"behavioral\"\n            ],\n            difficulty: \"medium\",\n            recordingEnabled: true\n        },\n        questions: [],\n        answers: [],\n        performanceMetrics: {\n            overallScore: 85,\n            categoryScores: {\n                technical: 82,\n                behavioral: 88,\n                communication: 85,\n                problemSolving: 83\n            },\n            strengths: [\n                \"Clear communication\",\n                \"Strong technical knowledge\"\n            ],\n            improvements: [\n                \"More specific examples\",\n                \"Better structure\"\n            ],\n            recommendations: [\n                \"Practice system design\",\n                \"Prepare more STAR examples\"\n            ]\n        }\n    },\n    {\n        id: \"session-2\",\n        userId: \"user-123\",\n        jobTitle: \"Product Manager\",\n        company: \"Meta\",\n        industry: \"Technology\",\n        status: \"completed\",\n        createdAt: new Date(\"2024-01-12\"),\n        updatedAt: new Date(\"2024-01-12\"),\n        config: {\n            duration: 60,\n            questionTypes: [\n                \"behavioral\",\n                \"case-study\"\n            ],\n            difficulty: \"hard\",\n            recordingEnabled: true\n        },\n        questions: [],\n        answers: [],\n        performanceMetrics: {\n            overallScore: 78,\n            categoryScores: {\n                strategic: 80,\n                analytical: 75,\n                communication: 82,\n                leadership: 76\n            },\n            strengths: [\n                \"Strategic thinking\",\n                \"Good communication\"\n            ],\n            improvements: [\n                \"Data analysis depth\",\n                \"Leadership examples\"\n            ],\n            recommendations: [\n                \"Practice case studies\",\n                \"Prepare leadership stories\"\n            ]\n        }\n    },\n    {\n        id: \"session-3\",\n        userId: \"user-123\",\n        jobTitle: \"Data Scientist\",\n        company: \"Netflix\",\n        industry: \"Technology\",\n        status: \"in-progress\",\n        createdAt: new Date(\"2024-01-20\"),\n        updatedAt: new Date(\"2024-01-20\"),\n        config: {\n            duration: 30,\n            questionTypes: [\n                \"technical\",\n                \"behavioral\"\n            ],\n            difficulty: \"medium\",\n            recordingEnabled: false\n        },\n        questions: [],\n        answers: []\n    }\n];\nconst mockQuestions = [\n    {\n        id: \"q1\",\n        text: \"Tell me about a challenging project you worked on recently.\",\n        type: \"behavioral\",\n        category: \"experience\",\n        difficulty: \"medium\",\n        timeLimit: 180,\n        followUpQuestions: [\n            \"What was the biggest challenge?\",\n            \"How did you overcome it?\",\n            \"What would you do differently?\"\n        ]\n    },\n    {\n        id: \"q2\",\n        text: \"How would you design a URL shortener like bit.ly?\",\n        type: \"technical\",\n        category: \"system-design\",\n        difficulty: \"hard\",\n        timeLimit: 300,\n        followUpQuestions: [\n            \"How would you handle scale?\",\n            \"What about analytics?\",\n            \"How would you prevent abuse?\"\n        ]\n    }\n];\n// Mock API delay\nconst delay = (ms)=>new Promise((resolve)=>setTimeout(resolve, ms));\nclass MockApiClient {\n    // Authentication methods\n    async login(credentials) {\n        await delay(1000);\n        // Validate email format\n        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n        if (!emailRegex.test(credentials.email)) {\n            throw new Error(\"Invalid email format\");\n        }\n        // Validate password (minimum 6 characters for demo)\n        if (!credentials.password || credentials.password.length < 6) {\n            throw new Error(\"Password must be at least 6 characters\");\n        }\n        // For demo purposes, accept any valid email/password combination\n        // In production, this would validate against a real database\n        this.token = \"mock-jwt-token\";\n        // Create user object based on email\n        const emailParts = credentials.email.split(\"@\")[0].split(\".\");\n        const firstName = emailParts[0] ? emailParts[0].charAt(0).toUpperCase() + emailParts[0].slice(1) : \"User\";\n        const lastName = emailParts[1] ? emailParts[1].charAt(0).toUpperCase() + emailParts[1].slice(1) : \"Demo\";\n        const user = {\n            ...mockUser,\n            email: credentials.email,\n            firstName,\n            lastName\n        };\n        return {\n            user,\n            token: this.token,\n            refreshToken: \"mock-refresh-token\"\n        };\n    }\n    async register(userData) {\n        await delay(1000);\n        // Validate email format\n        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n        if (!emailRegex.test(userData.email)) {\n            throw new Error(\"Invalid email format\");\n        }\n        // Validate password\n        if (!userData.password || userData.password.length < 6) {\n            throw new Error(\"Password must be at least 6 characters\");\n        }\n        // Validate required fields\n        if (!userData.firstName || !userData.lastName) {\n            throw new Error(\"First name and last name are required\");\n        }\n        const newUser = {\n            ...mockUser,\n            id: `user-${Date.now()}`,\n            email: userData.email,\n            firstName: userData.firstName,\n            lastName: userData.lastName,\n            createdAt: new Date(),\n            updatedAt: new Date()\n        };\n        this.token = \"mock-jwt-token\";\n        return {\n            user: newUser,\n            token: this.token,\n            refreshToken: \"mock-refresh-token\"\n        };\n    }\n    async logout() {\n        await delay(500);\n        this.token = null;\n    }\n    async getCurrentUser() {\n        await delay(500);\n        if (!this.token) throw new Error(\"Not authenticated\");\n        return mockUser;\n    }\n    async updateProfile(data) {\n        await delay(1000);\n        return {\n            ...mockUser,\n            ...data,\n            updatedAt: new Date()\n        };\n    }\n    // Interview methods\n    async createInterviewSession(config) {\n        await delay(1000);\n        const newSession = {\n            id: `session-${Date.now()}`,\n            userId: mockUser.id,\n            jobTitle: config.jobTitle || \"Software Engineer\",\n            company: config.company || \"Tech Company\",\n            industry: config.industry || \"Technology\",\n            status: \"created\",\n            createdAt: new Date(),\n            updatedAt: new Date(),\n            config,\n            questions: mockQuestions,\n            answers: []\n        };\n        mockSessions.unshift(newSession);\n        return newSession;\n    }\n    async getInterviewSessions() {\n        await delay(800);\n        return mockSessions;\n    }\n    async getInterviewSession(sessionId) {\n        await delay(500);\n        const session = mockSessions.find((s)=>s.id === sessionId);\n        if (!session) throw new Error(\"Session not found\");\n        return session;\n    }\n    async startInterviewSession(sessionId) {\n        await delay(1000);\n        const session = mockSessions.find((s)=>s.id === sessionId);\n        if (!session) throw new Error(\"Session not found\");\n        session.status = \"in-progress\";\n        session.startedAt = new Date();\n        return mockQuestions[0];\n    }\n    async submitAnswer(sessionId, data) {\n        await delay(2000) // Simulate AI processing time\n        ;\n        const feedback = {\n            id: `feedback-${Date.now()}`,\n            questionId: data.questionId,\n            score: Math.floor(Math.random() * 30) + 70,\n            strengths: [\n                \"Clear communication\",\n                \"Good structure\",\n                \"Relevant examples\"\n            ],\n            improvements: [\n                \"More specific details\",\n                \"Better time management\",\n                \"Stronger conclusion\"\n            ],\n            suggestions: [\n                \"Use the STAR method\",\n                \"Practice with a timer\",\n                \"Prepare more examples\"\n            ],\n            createdAt: new Date()\n        };\n        const nextQuestion = mockQuestions[1] // Return next question or undefined if last\n        ;\n        return {\n            feedback,\n            nextQuestion\n        };\n    }\n    async completeInterviewSession(sessionId) {\n        await delay(1500);\n        const session = mockSessions.find((s)=>s.id === sessionId);\n        if (!session) throw new Error(\"Session not found\");\n        session.status = \"completed\";\n        session.completedAt = new Date();\n        const metrics = {\n            overallScore: Math.floor(Math.random() * 25) + 75,\n            categoryScores: {\n                technical: Math.floor(Math.random() * 30) + 70,\n                behavioral: Math.floor(Math.random() * 30) + 70,\n                communication: Math.floor(Math.random() * 30) + 70,\n                problemSolving: Math.floor(Math.random() * 30) + 70\n            },\n            strengths: [\n                \"Strong technical knowledge\",\n                \"Clear communication\",\n                \"Good problem-solving approach\"\n            ],\n            improvements: [\n                \"More detailed examples\",\n                \"Better time management\",\n                \"Stronger closing statements\"\n            ],\n            recommendations: [\n                \"Practice more behavioral questions\",\n                \"Work on system design skills\",\n                \"Prepare industry-specific examples\"\n            ]\n        };\n        session.performanceMetrics = metrics;\n        return metrics;\n    }\n    async getSessionResults(sessionId) {\n        await delay(1000);\n        const session = mockSessions.find((s)=>s.id === sessionId);\n        if (!session) throw new Error(\"Session not found\");\n        const mockFeedback = [\n            {\n                id: \"feedback-1\",\n                questionId: \"q1\",\n                score: 85,\n                strengths: [\n                    \"Clear structure\",\n                    \"Good examples\"\n                ],\n                improvements: [\n                    \"More specific metrics\"\n                ],\n                suggestions: [\n                    \"Use STAR method\"\n                ],\n                createdAt: new Date()\n            }\n        ];\n        return {\n            session,\n            metrics: session.performanceMetrics,\n            feedback: mockFeedback\n        };\n    }\n    // Resume methods\n    async uploadResume(file) {\n        await delay(2000);\n        return {\n            id: `resume-${Date.now()}`,\n            userId: mockUser.id,\n            filename: file.name,\n            originalName: file.name,\n            size: file.size,\n            mimeType: file.type,\n            url: URL.createObjectURL(file),\n            extractedText: \"Mock extracted text from resume...\",\n            analysis: {\n                atsScore: 85,\n                keywords: [\n                    \"JavaScript\",\n                    \"React\",\n                    \"Node.js\",\n                    \"Python\"\n                ],\n                suggestions: [\n                    \"Add more quantifiable achievements\",\n                    \"Include relevant certifications\"\n                ]\n            },\n            createdAt: new Date(),\n            updatedAt: new Date()\n        };\n    }\n    async getResumes() {\n        await delay(500);\n        return [];\n    }\n    async analyzeResume(resumeId) {\n        await delay(3000);\n        return {\n            atsScore: Math.floor(Math.random() * 30) + 70,\n            keywords: [\n                \"JavaScript\",\n                \"React\",\n                \"Node.js\",\n                \"Python\",\n                \"AWS\"\n            ],\n            suggestions: [\n                \"Add more quantifiable achievements\",\n                \"Include relevant certifications\",\n                \"Optimize for ATS keywords\",\n                \"Improve formatting consistency\"\n            ]\n        };\n    }\n    // Expert methods\n    async getExperts() {\n        await delay(1000);\n        return [];\n    }\n    async bookExpertSession(expertId, data) {\n        await delay(1000);\n        return {\n            success: true,\n            bookingId: `booking-${Date.now()}`\n        };\n    }\n    // Analytics methods\n    async getAnalytics() {\n        await delay(1000);\n        // Return mock analytics data\n        return {\n            totalSessions: mockSessions.length,\n            averageScore: 82,\n            improvementRate: 15,\n            timeSpent: 750,\n            categoryBreakdown: {\n                technical: 80,\n                behavioral: 85,\n                communication: 83,\n                problemSolving: 78\n            },\n            recentActivity: [\n                {\n                    date: \"2024-01-20\",\n                    sessions: 2,\n                    score: 85\n                },\n                {\n                    date: \"2024-01-19\",\n                    sessions: 1,\n                    score: 78\n                },\n                {\n                    date: \"2024-01-18\",\n                    sessions: 3,\n                    score: 82\n                }\n            ],\n            trends: {\n                scoreImprovement: 12,\n                consistencyRating: 85,\n                strongestArea: \"Communication\",\n                weakestArea: \"Technical\"\n            }\n        };\n    }\n    // AI methods\n    async generateQuestions(data) {\n        await delay(2000);\n        return mockQuestions;\n    }\n    async analyzeAnswer(data) {\n        await delay(1500);\n        return {\n            id: `feedback-${Date.now()}`,\n            questionId: data.questionId,\n            score: Math.floor(Math.random() * 30) + 70,\n            strengths: [\n                \"Clear communication\",\n                \"Good structure\"\n            ],\n            improvements: [\n                \"More specific examples\"\n            ],\n            suggestions: [\n                \"Use STAR method\",\n                \"Practice timing\"\n            ],\n            createdAt: new Date()\n        };\n    }\n    async analyzeEmotion(data) {\n        await delay(1000);\n        return {\n            confidence: 0.85,\n            emotions: {\n                confident: 0.7,\n                nervous: 0.2,\n                excited: 0.1\n            },\n            recommendations: [\n                \"Maintain eye contact\",\n                \"Speak more slowly\"\n            ]\n        };\n    }\n    constructor(){\n        this.token = null;\n    }\n}\n// Create and export singleton\nconst mockApiClient = new MockApiClient();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (mockApiClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/mockApi.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   capitalizeFirst: () => (/* binding */ capitalizeFirst),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatBytes: () => (/* binding */ formatBytes),\n/* harmony export */   formatDuration: () => (/* binding */ formatDuration),\n/* harmony export */   formatRelativeTime: () => (/* binding */ formatRelativeTime),\n/* harmony export */   formatScore: () => (/* binding */ formatScore),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   getErrorMessage: () => (/* binding */ getErrorMessage),\n/* harmony export */   getInitials: () => (/* binding */ getInitials),\n/* harmony export */   getScoreBadgeVariant: () => (/* binding */ getScoreBadgeVariant),\n/* harmony export */   getScoreColor: () => (/* binding */ getScoreColor),\n/* harmony export */   isTokenExpired: () => (/* binding */ isTokenExpired),\n/* harmony export */   parseJwt: () => (/* binding */ parseJwt),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   throttle: () => (/* binding */ throttle),\n/* harmony export */   truncateText: () => (/* binding */ truncateText),\n/* harmony export */   validateEmail: () => (/* binding */ validateEmail)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatBytes(bytes, decimals = 2) {\n    if (bytes === 0) return \"0 Bytes\";\n    const k = 1024;\n    const dm = decimals < 0 ? 0 : decimals;\n    const sizes = [\n        \"Bytes\",\n        \"KB\",\n        \"MB\",\n        \"GB\",\n        \"TB\",\n        \"PB\",\n        \"EB\",\n        \"ZB\",\n        \"YB\"\n    ];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + \" \" + sizes[i];\n}\nfunction formatDuration(seconds) {\n    const hours = Math.floor(seconds / 3600);\n    const minutes = Math.floor(seconds % 3600 / 60);\n    const remainingSeconds = seconds % 60;\n    if (hours > 0) {\n        return `${hours}:${minutes.toString().padStart(2, \"0\")}:${remainingSeconds.toString().padStart(2, \"0\")}`;\n    }\n    return `${minutes}:${remainingSeconds.toString().padStart(2, \"0\")}`;\n}\nfunction formatRelativeTime(date) {\n    const now = new Date();\n    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n    if (diffInSeconds < 60) {\n        return \"just now\";\n    }\n    const diffInMinutes = Math.floor(diffInSeconds / 60);\n    if (diffInMinutes < 60) {\n        return `${diffInMinutes} minute${diffInMinutes > 1 ? \"s\" : \"\"} ago`;\n    }\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) {\n        return `${diffInHours} hour${diffInHours > 1 ? \"s\" : \"\"} ago`;\n    }\n    const diffInDays = Math.floor(diffInHours / 24);\n    if (diffInDays < 7) {\n        return `${diffInDays} day${diffInDays > 1 ? \"s\" : \"\"} ago`;\n    }\n    const diffInWeeks = Math.floor(diffInDays / 7);\n    if (diffInWeeks < 4) {\n        return `${diffInWeeks} week${diffInWeeks > 1 ? \"s\" : \"\"} ago`;\n    }\n    const diffInMonths = Math.floor(diffInDays / 30);\n    if (diffInMonths < 12) {\n        return `${diffInMonths} month${diffInMonths > 1 ? \"s\" : \"\"} ago`;\n    }\n    const diffInYears = Math.floor(diffInDays / 365);\n    return `${diffInYears} year${diffInYears > 1 ? \"s\" : \"\"} ago`;\n}\nfunction generateId() {\n    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n}\nfunction debounce(func, wait) {\n    let timeout = null;\n    return (...args)=>{\n        if (timeout) {\n            clearTimeout(timeout);\n        }\n        timeout = setTimeout(()=>{\n            func(...args);\n        }, wait);\n    };\n}\nfunction throttle(func, limit) {\n    let inThrottle;\n    return (...args)=>{\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\nfunction sleep(ms) {\n    return new Promise((resolve)=>setTimeout(resolve, ms));\n}\nfunction getInitials(name) {\n    return name.split(\" \").map((word)=>word.charAt(0)).join(\"\").toUpperCase().slice(0, 2);\n}\nfunction validateEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\nfunction capitalizeFirst(str) {\n    return str.charAt(0).toUpperCase() + str.slice(1);\n}\nfunction truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.slice(0, maxLength) + \"...\";\n}\nfunction parseJwt(token) {\n    try {\n        const base64Url = token.split(\".\")[1];\n        const base64 = base64Url.replace(/-/g, \"+\").replace(/_/g, \"/\");\n        const jsonPayload = decodeURIComponent(atob(base64).split(\"\").map((c)=>\"%\" + (\"00\" + c.charCodeAt(0).toString(16)).slice(-2)).join(\"\"));\n        return JSON.parse(jsonPayload);\n    } catch (error) {\n        return null;\n    }\n}\nfunction isTokenExpired(token) {\n    const payload = parseJwt(token);\n    if (!payload || !payload.exp) return true;\n    const currentTime = Date.now() / 1000;\n    return payload.exp < currentTime;\n}\nfunction getErrorMessage(error) {\n    if (error instanceof Error) return error.message;\n    if (typeof error === \"string\") return error;\n    return \"An unknown error occurred\";\n}\nfunction formatScore(score) {\n    return `${Math.round(score)}%`;\n}\nfunction getScoreColor(score) {\n    if (score >= 80) return \"text-green-600\";\n    if (score >= 60) return \"text-yellow-600\";\n    return \"text-red-600\";\n}\nfunction getScoreBadgeVariant(score) {\n    if (score >= 80) return \"default\";\n    if (score >= 60) return \"secondary\";\n    return \"destructive\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./src/stores/auth.ts":
/*!****************************!*\
  !*** ./src/stores/auth.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuthStore: () => (/* binding */ useAuthStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! sonner */ \"(ssr)/./node_modules/sonner/dist/index.mjs\");\n\n\n\n\nconst useAuthStore = (0,zustand__WEBPACK_IMPORTED_MODULE_2__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_3__.persist)((set, get)=>({\n        // Initial state\n        user: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: null,\n        // Actions\n        login: async (credentials)=>{\n            try {\n                set({\n                    isLoading: true,\n                    error: null\n                });\n                const authResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.apiClient.login(credentials);\n                set({\n                    user: authResponse.user,\n                    isAuthenticated: true,\n                    isLoading: false,\n                    error: null\n                });\n                sonner__WEBPACK_IMPORTED_MODULE_1__.toast.success(\"Successfully logged in!\");\n            } catch (error) {\n                set({\n                    user: null,\n                    isAuthenticated: false,\n                    isLoading: false,\n                    error: error.message\n                });\n                sonner__WEBPACK_IMPORTED_MODULE_1__.toast.error(error.message || \"Login failed\");\n                throw error;\n            }\n        },\n        register: async (userData)=>{\n            try {\n                set({\n                    isLoading: true,\n                    error: null\n                });\n                const authResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.apiClient.register(userData);\n                set({\n                    user: authResponse.user,\n                    isAuthenticated: true,\n                    isLoading: false,\n                    error: null\n                });\n                sonner__WEBPACK_IMPORTED_MODULE_1__.toast.success(\"Account created successfully!\");\n            } catch (error) {\n                set({\n                    user: null,\n                    isAuthenticated: false,\n                    isLoading: false,\n                    error: error.message\n                });\n                sonner__WEBPACK_IMPORTED_MODULE_1__.toast.error(error.message || \"Registration failed\");\n                throw error;\n            }\n        },\n        logout: async ()=>{\n            try {\n                set({\n                    isLoading: true\n                });\n                await _lib_api__WEBPACK_IMPORTED_MODULE_0__.apiClient.logout();\n                set({\n                    user: null,\n                    isAuthenticated: false,\n                    isLoading: false,\n                    error: null\n                });\n                sonner__WEBPACK_IMPORTED_MODULE_1__.toast.success(\"Successfully logged out\");\n            } catch (error) {\n                // Even if logout fails on server, clear local state\n                set({\n                    user: null,\n                    isAuthenticated: false,\n                    isLoading: false,\n                    error: null\n                });\n                sonner__WEBPACK_IMPORTED_MODULE_1__.toast.error(\"Logout failed, but you have been signed out locally\");\n            }\n        },\n        getCurrentUser: async ()=>{\n            try {\n                set({\n                    isLoading: true,\n                    error: null\n                });\n                const user = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.apiClient.getCurrentUser();\n                set({\n                    user,\n                    isAuthenticated: true,\n                    isLoading: false,\n                    error: null\n                });\n            } catch (error) {\n                set({\n                    user: null,\n                    isAuthenticated: false,\n                    isLoading: false,\n                    error: error.message\n                });\n                throw error;\n            }\n        },\n        updateProfile: async (data)=>{\n            try {\n                set({\n                    isLoading: true,\n                    error: null\n                });\n                const updatedUser = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.apiClient.updateProfile(data);\n                set({\n                    user: updatedUser,\n                    isLoading: false,\n                    error: null\n                });\n                sonner__WEBPACK_IMPORTED_MODULE_1__.toast.success(\"Profile updated successfully!\");\n            } catch (error) {\n                set({\n                    isLoading: false,\n                    error: error.message\n                });\n                sonner__WEBPACK_IMPORTED_MODULE_1__.toast.error(error.message || \"Failed to update profile\");\n                throw error;\n            }\n        },\n        clearError: ()=>{\n            set({\n                error: null\n            });\n        },\n        setLoading: (loading)=>{\n            set({\n                isLoading: loading\n            });\n        }\n    }), {\n    name: \"auth-storage\",\n    partialize: (state)=>({\n            user: state.user,\n            isAuthenticated: state.isAuthenticated\n        })\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/stores/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"f84d94280eb3\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQHdlYi9hcHAvLi9zcmMvYXBwL2dsb2JhbHMuY3NzP2UzOWEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJmODRkOTQyODBlYjNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/auth/login/page.tsx":
/*!*************************************!*\
  !*** ./src/app/auth/login/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\apps\InterviewSpark\apps\web\src\app\auth\login\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers */ \"(rsc)/./src/components/providers.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"AI-InterviewSpark - Advanced Mock Interview Platform\",\n    description: \"Master your interviews with AI-powered mock sessions, real-time emotional analysis, and personalized feedback.\",\n    keywords: [\n        \"mock interview\",\n        \"interview preparation\",\n        \"AI interview\",\n        \"emotional analysis\",\n        \"interview coaching\",\n        \"job preparation\",\n        \"career development\"\n    ],\n    authors: [\n        {\n            name: \"AI-InterviewSpark Team\"\n        }\n    ],\n    creator: \"AI-InterviewSpark\",\n    publisher: \"AI-InterviewSpark\",\n    formatDetection: {\n        email: false,\n        address: false,\n        telephone: false\n    },\n    metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || \"http://localhost:3000\"),\n    openGraph: {\n        type: \"website\",\n        locale: \"en_US\",\n        url: \"/\",\n        title: \"AI-InterviewSpark - Advanced Mock Interview Platform\",\n        description: \"Master your interviews with AI-powered mock sessions, real-time emotional analysis, and personalized feedback.\",\n        siteName: \"AI-InterviewSpark\",\n        images: [\n            {\n                url: \"/og-image.png\",\n                width: 1200,\n                height: 630,\n                alt: \"AI-InterviewSpark - Advanced Mock Interview Platform\"\n            }\n        ]\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"AI-InterviewSpark - Advanced Mock Interview Platform\",\n        description: \"Master your interviews with AI-powered mock sessions, real-time emotional analysis, and personalized feedback.\",\n        images: [\n            \"/og-image.png\"\n        ]\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    },\n    verification: {\n        google: \"your-google-verification-code\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen bg-background font-sans antialiased\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 75,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 74,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/providers.tsx":
/*!**************************************!*\
  !*** ./src/components/providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\apps\InterviewSpark\apps\web\src\components\providers.tsx#Providers`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@tanstack","vendor-chunks/sonner","vendor-chunks/next-themes","vendor-chunks/@swc","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/@radix-ui","vendor-chunks/class-variance-authority","vendor-chunks/clsx","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/zustand","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/form-data","vendor-chunks/get-intrinsic","vendor-chunks/asynckit","vendor-chunks/use-sync-external-store","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/zod","vendor-chunks/@hookform","vendor-chunks/react-hook-form"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Flogin%2Fpage&page=%2Fauth%2Flogin%2Fpage&appPaths=%2Fauth%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fauth%2Flogin%2Fpage.tsx&appDir=C%3A%5Capps%5CInterviewSpark%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Capps%5CInterviewSpark%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();