"use client";import R,{createContext as k,useContext as h,useMemo as T,useState as D}from"react";import{useEvent as v}from'../../hooks/use-event.js';import{useId as _}from'../../hooks/use-id.js';import{useIsoMorphicEffect as A}from'../../hooks/use-iso-morphic-effect.js';import{useSyncRefs as B}from'../../hooks/use-sync-refs.js';import{useDisabled as F}from'../../internal/disabled.js';import{useProvidedId as S}from'../../internal/id.js';import{forwardRefWithAs as M,useRender as H}from'../../utils/render.js';let c=k(null);c.displayName="LabelContext";function P(){let r=h(c);if(r===null){let l=new Error("You used a <Label /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(l,P),l}return r}function I(r){var a,e,o;let l=(e=(a=h(c))==null?void 0:a.value)!=null?e:void 0;return((o=r==null?void 0:r.length)!=null?o:0)>0?[l,...r].filter(Boolean).join(" "):l}function K({inherit:r=!1}={}){let l=I(),[a,e]=D([]),o=r?[l,...a].filter(Boolean):a;return[o.length>0?o.join(" "):void 0,T(()=>function(t){let s=v(i=>(e(p=>[...p,i]),()=>e(p=>{let u=p.slice(),d=u.indexOf(i);return d!==-1&&u.splice(d,1),u}))),m=T(()=>({register:s,slot:t.slot,name:t.name,props:t.props,value:t.value}),[s,t.slot,t.name,t.props,t.value]);return R.createElement(c.Provider,{value:m},t.children)},[e])]}let N="label";function G(r,l){var y;let a=_(),e=P(),o=S(),g=F(),{id:t=`headlessui-label-${a}`,htmlFor:s=o!=null?o:(y=e.props)==null?void 0:y.htmlFor,passive:m=!1,...i}=r,p=B(l);A(()=>e.register(t),[t,e.register]);let u=v(L=>{let b=L.currentTarget;if(b instanceof HTMLLabelElement&&L.preventDefault(),e.props&&"onClick"in e.props&&typeof e.props.onClick=="function"&&e.props.onClick(L),b instanceof HTMLLabelElement){let n=document.getElementById(b.htmlFor);if(n){let E=n.getAttribute("disabled");if(E==="true"||E==="")return;let x=n.getAttribute("aria-disabled");if(x==="true"||x==="")return;(n instanceof HTMLInputElement&&(n.type==="radio"||n.type==="checkbox")||n.role==="radio"||n.role==="checkbox"||n.role==="switch")&&n.click(),n.focus({preventScroll:!0})}}}),d=g||!1,C=T(()=>({...e.slot,disabled:d}),[e.slot,d]),f={ref:p,...e.props,id:t,htmlFor:s,onClick:u};return m&&("onClick"in f&&(delete f.htmlFor,delete f.onClick),"onClick"in i&&delete i.onClick),H()({ourProps:f,theirProps:i,slot:C,defaultTag:s?N:"div",name:e.name||"Label"})}let U=M(G),Q=Object.assign(U,{});export{Q as Label,P as useLabelContext,I as useLabelledBy,K as useLabels};
