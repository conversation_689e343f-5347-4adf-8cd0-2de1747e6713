"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("tslib");require("../../icon-elements/Badge/Badge.cjs"),require("../../icon-elements/BadgeDelta/BadgeDelta.cjs");var r=require("../../icon-elements/Icon/Icon.cjs"),t=require("../../../lib/tremorTwMerge.cjs");function o(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var n=o(require("react"));exports.NavButton=o=>{var{onClick:a,icon:d}=o,c=e.__rest(o,["onClick","icon"]);const u=d;return n.default.createElement("button",Object.assign({type:"button",className:t.tremorTwMerge("flex items-center justify-center p-1 h-7 w-7 outline-none focus:ring-2 transition duration-100 border border-tremor-border dark:border-dark-tremor-border hover:bg-tremor-background-muted dark:hover:bg-dark-tremor-background-muted rounded-tremor-small focus:border-tremor-brand-subtle select-none dark:focus:border-dark-tremor-brand-subtle focus:ring-tremor-brand-muted dark:focus:ring-dark-tremor-brand-muted text-tremor-content-subtle dark:text-dark-tremor-content-subtle hover:text-tremor-content dark:hover:text-dark-tremor-content")},c),n.default.createElement(r.default,{onClick:a,icon:u,variant:"simple",color:"slate",size:"sm"}))};
