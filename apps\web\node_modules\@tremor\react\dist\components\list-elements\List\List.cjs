"use strict";var e=require("tslib"),r=require("../../../lib/tremorTwMerge.cjs"),t=require("../../../lib/utils.cjs");function a(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var i=a(require("react"));const s=t.makeClassName("List"),d=i.default.forwardRef(((t,a)=>{const{children:d,className:l}=t,o=e.__rest(t,["children","className"]);return i.default.createElement("ul",Object.assign({ref:a,className:r.tremorTwMerge(s("root"),"w-full divide-y","divide-tremor-border text-tremor-content","dark:divide-dark-tremor-border dark:text-dark-tremor-content",l)},o),d)}));d.displayName="List",module.exports=d;
