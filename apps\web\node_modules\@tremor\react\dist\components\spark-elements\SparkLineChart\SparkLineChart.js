'use client';
import{__rest as e}from"tslib";import t from"react";import{ResponsiveContainer as a,<PERSON><PERSON><PERSON> as o,YAxis as r,XAxis as n,Line as i}from"recharts";import{BaseColors as m}from"../../../lib/constants.js";import{themeColorRange as l,colorPalette as s}from"../../../lib/theme.js";import{tremorTwMerge as c}from"../../../lib/tremorTwMerge.js";import{getColorClassNames as u}from"../../../lib/utils.js";import{constructCategoryColors as d,getYAxisDomain as p}from"../../chart-elements/common/utils.js";import f from"../../chart-elements/common/NoData.js";const h=t.forwardRef(((h,N)=>{const{data:x=[],categories:y=[],index:g,colors:j=l,animationDuration:k=900,showAnimation:b=!1,curveType:v="linear",connectNulls:D=!1,noDataText:E,autoMinValue:w=!1,minValue:T,maxValue:V,className:A}=h,L=e(h,["data","categories","index","colors","animationDuration","showAnimation","curveType","connectNulls","noDataText","autoMinValue","minValue","maxValue","className"]),M=d(y,j),C=p(w,T,V);return t.createElement("div",Object.assign({ref:N,className:c("w-28 h-12",A)},L),t.createElement(a,{className:"h-full w-full"},(null==x?void 0:x.length)?t.createElement(o,{data:x,margin:{top:1,left:1,right:1,bottom:1}},t.createElement(r,{hide:!0,domain:C}),t.createElement(n,{hide:!0,dataKey:g}),y.map((e=>{var a;return t.createElement(i,{className:c(u(null!==(a=M.get(e))&&void 0!==a?a:m.Gray,s.text).strokeColor),strokeOpacity:1,dot:!1,key:e,name:e,type:v,dataKey:e,stroke:"",strokeWidth:2,strokeLinejoin:"round",strokeLinecap:"round",isAnimationActive:b,animationDuration:k,connectNulls:D})}))):t.createElement(f,{noDataText:E})))}));h.displayName="SparkLineChart";export{h as default};
