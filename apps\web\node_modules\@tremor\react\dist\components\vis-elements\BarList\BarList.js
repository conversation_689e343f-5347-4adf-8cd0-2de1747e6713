'use client';
import{__rest as e}from"tslib";import t from"react";import{colorPalette as r}from"../../../lib/theme.js";import{tremorTwMerge as a}from"../../../lib/tremorTwMerge.js";import{getColorClassNames as o,makeClassName as n,defaultValueFormatter as l}from"../../../lib/utils.js";const s=n("BarList");function m(n,m){const{data:c=[],color:i,valueFormatter:u=l,showAnimation:d=!1,onValueChange:b,sortOrder:p="descending",className:h}=n,v=e(n,["data","color","valueFormatter","showAnimation","onValueChange","sortOrder","className"]),f=b?"button":"div",g=t.useMemo((()=>"none"===p?c:[...c].sort(((e,t)=>"ascending"===p?e.value-t.value:t.value-e.value))),[c,p]),k=t.useMemo((()=>{const e=Math.max(...g.map((e=>e.value)),0);return g.map((t=>0===t.value?0:Math.max(t.value/e*100,2)))}),[g]);return t.createElement("div",Object.assign({ref:m,className:a(s("root"),"flex justify-between space-x-6",h),"aria-sort":p},v),t.createElement("div",{className:a(s("bars"),"relative w-full space-y-1.5")},g.map(((e,n)=>{var l,m,c;const u=e.icon;return t.createElement(f,{key:null!==(l=e.key)&&void 0!==l?l:n,onClick:()=>{null==b||b(e)},className:a(s("bar"),"group w-full flex items-center rounded-tremor-small",b?["cursor-pointer","hover:bg-tremor-background-muted dark:hover:bg-dark-tremor-background-subtle/40"]:"")},t.createElement("div",{className:a("flex items-center rounded transition-all bg-opacity-40","h-8",e.color||i?[o(null!==(m=e.color)&&void 0!==m?m:i,r.background).bgColor,b?"group-hover:bg-opacity-30":""]:"bg-tremor-brand-subtle dark:bg-dark-tremor-brand-subtle/60",!b||e.color||i?"":"group-hover:bg-tremor-brand-subtle/30 group-hover:dark:bg-dark-tremor-brand-subtle/70",n===g.length-1?"mb-0":"",d?"duration-500":""),style:{width:`${k[n]}%`,transition:d?"all 1s":""}},t.createElement("div",{className:a("absolute left-2 pr-4 flex max-w-full")},u?t.createElement(u,{className:a(s("barIcon"),"flex-none h-5 w-5 mr-2","text-tremor-content","dark:text-dark-tremor-content")}):null,e.href?t.createElement("a",{href:e.href,target:null!==(c=e.target)&&void 0!==c?c:"_blank",rel:"noreferrer",className:a(s("barLink"),"whitespace-nowrap hover:underline truncate text-tremor-default",b?"cursor-pointer":"","text-tremor-content-emphasis","dark:text-dark-tremor-content-emphasis"),onClick:e=>e.stopPropagation()},e.name):t.createElement("p",{className:a(s("barText"),"whitespace-nowrap truncate text-tremor-default","text-tremor-content-emphasis","dark:text-dark-tremor-content-emphasis")},e.name))))}))),t.createElement("div",{className:s("labels")},g.map(((e,r)=>{var o;return t.createElement("div",{key:null!==(o=e.key)&&void 0!==o?o:r,className:a(s("labelWrapper"),"flex justify-end items-center","h-8",r===g.length-1?"mb-0":"mb-1.5")},t.createElement("p",{className:a(s("labelText"),"whitespace-nowrap leading-none truncate text-tremor-default","text-tremor-content-emphasis","dark:text-dark-tremor-content-emphasis")},u(e.value)))}))))}m.displayName="BarList";const c=t.forwardRef(m);export{c as default};
