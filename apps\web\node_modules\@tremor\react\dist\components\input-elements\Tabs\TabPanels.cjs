'use client';
"use strict";var e=require("tslib"),t=require("@headlessui/react");require("../../../contexts/BaseColorContext.cjs");var r=require("../../../contexts/IndexContext.cjs");require("../../../contexts/RootStylesContext.cjs");var a=require("../../../contexts/SelectedValueContext.cjs"),s=require("../../../lib/tremorTwMerge.cjs"),l=require("../../../lib/utils.cjs");function c(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var u=c(require("react"));const n=l.makeClassName("TabPanels"),o=u.default.forwardRef(((l,c)=>{const{children:o,className:i}=l,d=e.__rest(l,["children","className"]);return u.default.createElement(t.Tab.Panels,Object.assign({as:"div",ref:c,className:s.tremorTwMerge(n("root"),"w-full",i)},d),(({selectedIndex:e})=>u.default.createElement(a.Provider,{value:{selectedValue:e}},u.default.Children.map(o,((e,t)=>u.default.createElement(r.Provider,{value:t},e))))))}));o.displayName="TabPanels",module.exports=o;
