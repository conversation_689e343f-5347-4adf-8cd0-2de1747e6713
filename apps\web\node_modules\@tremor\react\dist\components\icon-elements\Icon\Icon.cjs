'use client';
"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("tslib"),r=require("react"),t=require("../../util-elements/Tooltip/Tooltip.cjs"),o=require("../../../lib/constants.cjs"),s=require("../../../lib/tremorTwMerge.cjs"),i=require("../../../lib/utils.cjs"),a=require("./styles.cjs");function l(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var n=l(r);const c=i.makeClassName("Icon"),p={Simple:"simple",Light:"light",Shadow:"shadow",Solid:"solid",Outlined:"outlined"},d=n.default.forwardRef(((r,l)=>{const{icon:d,variant:u=p.Simple,tooltip:f,size:m=o.Sizes.SM,color:g,className:h}=r,b=e.__rest(r,["icon","variant","tooltip","size","color","className"]),j=d,w=a.getIconColors(u,g),{tooltipProps:S,getReferenceProps:q}=t.useTooltip();return n.default.createElement("span",Object.assign({ref:i.mergeRefs([l,S.refs.setReference]),className:s.tremorTwMerge(c("root"),"inline-flex shrink-0 items-center justify-center",w.bgColor,w.textColor,w.borderColor,w.ringColor,a.shape[u].rounded,a.shape[u].border,a.shape[u].shadow,a.shape[u].ring,a.wrapperProportions[m].paddingX,a.wrapperProportions[m].paddingY,h)},q,b),n.default.createElement(t.default,Object.assign({text:f},S)),n.default.createElement(j,{className:s.tremorTwMerge(c("icon"),"shrink-0",a.iconSizes[m].height,a.iconSizes[m].width)}))}));d.displayName="Icon",exports.IconVariants=p,exports.default=d;
