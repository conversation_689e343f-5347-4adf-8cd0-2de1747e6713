'use client';
import{__rest as e}from"tslib";import t,{useState as r,useCallback as n,useRef as a}from"react";import o from"../../assets/ExclamationFilledIcon.js";import l from"../../assets/EyeIcon.js";import s from"../../assets/EyeOffIcon.js";import{getSelectButtonColors as m,hasValue as d}from"./selectUtils.js";import{tremorTwMerge as u}from"../../lib/tremorTwMerge.js";import{mergeRefs as i}from"../../lib/utils.js";const c=t.forwardRef(((c,p)=>{const{value:b,defaultValue:f,type:x,placeholder:h="Type...",icon:k,error:g=!1,errorMessage:w,disabled:E=!1,stepper:v,makeInputClassName:N,className:y,onChange:C,onValueChange:j,autoFocus:I,pattern:V}=c,F=e(c,["value","defaultValue","type","placeholder","icon","error","errorMessage","disabled","stepper","makeInputClassName","className","onChange","onValueChange","autoFocus","pattern"]),[L,M]=r(I||!1),[B,O]=r(!1),T=n((()=>O(!B)),[B,O]),H=k,P=a(null),R=d(b||f);return t.useEffect((()=>{const e=()=>M(!0),t=()=>M(!1),r=P.current;return r&&(r.addEventListener("focus",e),r.addEventListener("blur",t),I&&r.focus()),()=>{r&&(r.removeEventListener("focus",e),r.removeEventListener("blur",t))}}),[I]),t.createElement(t.Fragment,null,t.createElement("div",{className:u(N("root"),"relative w-full flex items-center min-w-[10rem] outline-none rounded-tremor-default transition duration-100 border","shadow-tremor-input","dark:shadow-dark-tremor-input",m(R,E,g),L&&u("ring-2","border-tremor-brand-subtle ring-tremor-brand-muted","dark:border-dark-tremor-brand-subtle dark:ring-dark-tremor-brand-muted"),y)},H?t.createElement(H,{className:u(N("icon"),"shrink-0 h-5 w-5 mx-2.5 absolute left-0 flex items-center","text-tremor-content-subtle","dark:text-dark-tremor-content-subtle")}):null,t.createElement("input",Object.assign({ref:i([P,p]),defaultValue:f,value:b,type:B?"text":x,className:u(N("input"),"w-full bg-transparent focus:outline-none focus:ring-0 border-none text-tremor-default rounded-tremor-default transition duration-100 py-2","text-tremor-content-emphasis","dark:text-dark-tremor-content-emphasis","[appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none","password"===x?g?"pr-16":"pr-12":g?"pr-8":"pr-3",H?"pl-10":"pl-3",E?"placeholder:text-tremor-content-subtle dark:placeholder:text-dark-tremor-content-subtle":"placeholder:text-tremor-content dark:placeholder:text-dark-tremor-content"),placeholder:h,disabled:E,"data-testid":"base-input",onChange:e=>{null==C||C(e),null==j||j(e.target.value)},pattern:V},F)),"password"!==x||E?null:t.createElement("button",{className:u(N("toggleButton"),"absolute inset-y-0 right-0 flex items-center px-2.5 rounded-lg"),type:"button",onClick:()=>T(),"aria-label":B?"Hide password":"Show Password"},B?t.createElement(s,{className:u("flex-none h-5 w-5 transition","text-tremor-content-subtle hover:text-tremor-content","dark:text-dark-tremor-content-subtle hover:dark:text-dark-tremor-content"),"aria-hidden":!0}):t.createElement(l,{className:u("flex-none h-5 w-5 transition","text-tremor-content-subtle hover:text-tremor-content","dark:text-dark-tremor-content-subtle hover:dark:text-dark-tremor-content"),"aria-hidden":!0})),g?t.createElement(o,{className:u(N("errorIcon"),"text-red-500 shrink-0 h-5 w-5 absolute right-0 flex items-center","password"===x?"mr-10":"number"===x?v?"mr-20":"mr-3":"mx-2.5")}):null,null!=v?v:null),g&&w?t.createElement("p",{className:u(N("errorMessage"),"text-sm text-red-500 mt-1")},w):null)}));c.displayName="BaseInput";export{c as default};
