import{__rest as e}from"tslib";import r from"react";import{tremorTwMerge as t}from"../../../lib/tremorTwMerge.js";import{makeClassName as o}from"../../../lib/utils.js";const a=o("TableBody"),i=r.forwardRef(((o,i)=>{const{children:d,className:l}=o,m=e(o,["children","className"]);return r.createElement(r.Fragment,null,r.createElement("tbody",Object.assign({ref:i,className:t(a("root"),"align-top divide-y","divide-tremor-border","dark:divide-dark-tremor-border",l)},m),d))}));i.displayName="TableBody";export{i as default};
