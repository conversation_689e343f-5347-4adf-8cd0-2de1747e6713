'use client';
import{__rest as e}from"tslib";import t,{useContext as r}from"react";import{Disclosure as o}from"@headlessui/react";import a from"../../../assets/ArrowUpHeadIcon.js";import{OpenContext as s}from"./Accordion.js";import{tremorTwMerge as m}from"../../../lib/tremorTwMerge.js";import{makeClassName as n}from"../../../lib/utils.js";const i=n("AccordionHeader"),l=t.forwardRef(((n,l)=>{const{children:c,className:d}=n,f=e(n,["children","className"]),{isOpen:p}=r(s);return t.createElement(o.Button,Object.assign({ref:l,className:m(i("root"),"w-full flex items-center justify-between px-4 py-3","text-tremor-content-emphasis","dark:text-dark-tremor-content-emphasis",d)},f),t.createElement("div",{className:m(i("children"),"flex flex-1 text-inherit mr-4")},c),t.createElement("div",null,t.createElement(a,{className:m(i("arrowIcon"),"h-5 w-5 -mr-1","text-tremor-content-subtle","dark:text-dark-tremor-content-subtle",p?"transition-all":"transition-all -rotate-180")})))}));l.displayName="AccordionHeader";export{l as default};
