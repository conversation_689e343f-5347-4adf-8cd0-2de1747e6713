"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),r=require("../../../lib/constants.cjs"),t=require("../../../lib/theme.cjs"),a=require("../../../lib/tremorTwMerge.cjs"),o=require("../../../lib/utils.cjs");function d(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var l=d(e);const m=({children:e})=>l.default.createElement("div",{className:a.tremorTwMerge("rounded-tremor-default text-tremor-default border","bg-tremor-background shadow-tremor-dropdown border-tremor-border","dark:bg-dark-tremor-background dark:shadow-dark-tremor-dropdown dark:border-dark-tremor-border")},e),s=({value:e,name:r})=>l.default.createElement("div",{className:"flex items-center justify-between space-x-8"},l.default.createElement("div",{className:"flex items-center space-x-2"},l.default.createElement("p",{className:a.tremorTwMerge("text-right whitespace-nowrap","text-tremor-content","dark:text-dark-tremor-content")},r)),l.default.createElement("p",{className:a.tremorTwMerge("font-medium tabular-nums text-right whitespace-nowrap","text-tremor-content-emphasis","dark:text-dark-tremor-content-emphasis")},e));exports.ChartTooltipFrame=m,exports.ChartTooltipRow=s,exports.default=({label:e,active:d,payload:n,valueFormatter:c,axis:u,category:i,categoryColors:p})=>{var b,f;return d&&n?l.default.createElement(m,null,l.default.createElement("div",{className:a.tremorTwMerge("flex items-center space-x-2 border-b px-4 py-2","border-tremor-border","dark:border-dark-tremor-border")},l.default.createElement("span",{className:a.tremorTwMerge("shrink-0 rounded-tremor-full border-2 h-3 w-3","border-tremor-background shadow-tremor-card","dark:border-dark-tremor-background dark:shadow-dark-tremor-card",o.getColorClassNames(i&&null!==(f=p.get(null===(b=null==n?void 0:n[0])||void 0===b?void 0:b.payload[i]))&&void 0!==f?f:r.BaseColors.Blue,t.colorPalette.background).bgColor)}),l.default.createElement("p",{className:a.tremorTwMerge("font-medium","text-tremor-content-emphasis","dark:text-dark-tremor-content-emphasis")},e)),l.default.createElement("div",{className:a.tremorTwMerge("px-4 py-2 space-y-1")},n.map((({value:e,name:r},t)=>{var a,d;const m=null!==(a=Object.keys(u).find((e=>u[e]===r)))&&void 0!==a?a:"",n=null!==(d=c[m])&&void 0!==d?d:o.defaultValueFormatter;return l.default.createElement(s,{key:`id-${t}`,value:c&&n?n(e):`${e}`,name:r})})))):null};
