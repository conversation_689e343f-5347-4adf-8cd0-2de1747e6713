"use strict";var e=require("tslib"),r=require("react"),t=require("../../../lib/tremorTwMerge.cjs"),a=require("../../../lib/utils.cjs");function l(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var d=l(r);const i=a.makeClassName("TableBody"),s=d.default.forwardRef(((r,a)=>{const{children:l,className:s}=r,o=e.__rest(r,["children","className"]);return d.default.createElement(d.default.Fragment,null,d.default.createElement("tbody",Object.assign({ref:a,className:t.tremorTwMerge(i("root"),"align-top divide-y","divide-tremor-border","dark:divide-dark-tremor-border",s)},o),l))}));s.displayName="TableBody",module.exports=s;
