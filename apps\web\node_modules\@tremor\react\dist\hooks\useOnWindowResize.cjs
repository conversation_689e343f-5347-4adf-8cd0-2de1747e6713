"use strict";function e(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var n=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,n.get?n:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}var r=e(require("react"));module.exports=e=>{r.useEffect((()=>{const r=()=>{e()};return r(),window.addEventListener("resize",r),()=>window.removeEventListener("resize",r)}),[e])};
