"use client";import{useFocusRing as W}from"@react-aria/focus";import{useHover as N}from"@react-aria/interactions";import i,{useCallback as V,useMemo as w,useState as J}from"react";import{useActivePress as $}from'../../hooks/use-active-press.js';import{useControllable as j}from'../../hooks/use-controllable.js';import{useDefaultValue as q}from'../../hooks/use-default-value.js';import{useDisposables as z}from'../../hooks/use-disposables.js';import{useEvent as l}from'../../hooks/use-event.js';import{useId as Q}from'../../hooks/use-id.js';import{useDisabled as Y}from'../../internal/disabled.js';import{FormFields as Z}from'../../internal/form-fields.js';import{useProvidedId as ee}from'../../internal/id.js';import{isDisabledReactIssue7711 as oe}from'../../utils/bugs.js';import{attemptSubmit as te}from'../../utils/form.js';import{forwardRefWithAs as re,mergeProps as ae,useRender as ne}from'../../utils/render.js';import{useDescribedBy as le}from'../description/description.js';import{Keys as y}from'../keyboard.js';import{useLabelledBy as se}from'../label/label.js';let ie="span";function de(T,h){let C=Q(),k=ee(),x=Y(),{id:g=k||`headlessui-checkbox-${C}`,disabled:e=x||!1,autoFocus:s=!1,checked:E,defaultChecked:v,onChange:P,name:d,value:D,form:R,indeterminate:n=!1,...A}=T,r=q(v),[a,t]=j(E,P,r!=null?r:!1),F=se(),K=le(),_=z(),[p,c]=J(!1),u=l(()=>{c(!0),t==null||t(!a),_.nextFrame(()=>{c(!1)})}),H=l(o=>{if(oe(o.currentTarget))return o.preventDefault();o.preventDefault(),u()}),B=l(o=>{o.key===y.Space?(o.preventDefault(),u()):o.key===y.Enter&&te(o.currentTarget)}),L=l(o=>o.preventDefault()),{isFocusVisible:m,focusProps:I}=W({autoFocus:s}),{isHovered:f,hoverProps:M}=N({isDisabled:e}),{pressed:b,pressProps:U}=$({disabled:e}),O=ae({ref:h,id:g,role:"checkbox","aria-checked":n?"mixed":a?"true":"false","aria-labelledby":F,"aria-describedby":K,"aria-disabled":e?!0:void 0,indeterminate:n?"true":void 0,tabIndex:e?void 0:0,onKeyUp:e?void 0:B,onKeyPress:e?void 0:L,onClick:e?void 0:H},I,M,U),X=w(()=>({checked:a,disabled:e,hover:f,focus:m,active:b,indeterminate:n,changing:p,autofocus:s}),[a,n,e,f,m,b,p,s]),G=V(()=>{if(r!==void 0)return t==null?void 0:t(r)},[t,r]),S=ne();return i.createElement(i.Fragment,null,d!=null&&i.createElement(Z,{disabled:e,data:{[d]:D||"on"},overrides:{type:"checkbox",checked:a},form:R,onReset:G}),S({ourProps:O,theirProps:A,slot:X,defaultTag:ie,name:"Checkbox"}))}let Ae=re(de);export{Ae as Checkbox};
