'use client';
import{__rest as t}from"tslib";import e,{useTooltip as o}from"../../util-elements/Tooltip/Tooltip.js";import i,{useEffect as r}from"react";import{useTransition as n}from"react-transition-state";import{HorizontalPositions as a,Sizes as s}from"../../../lib/constants.js";import{tremorTwMerge as l}from"../../../lib/tremorTwMerge.js";import{mergeRefs as m,makeClassName as c}from"../../../lib/utils.js";import d from"../../../assets/LoadingSpinner.js";import{iconSizes as p,getButtonColors as u,getButtonProportions as f}from"./styles.js";const g=c("Button"),h=({loading:t,iconSize:e,iconPosition:o,Icon:r,needMargin:n,transitionStatus:s})=>{const m=n?o===a.Left?l("-ml-1","mr-1.5"):l("-mr-1","ml-1.5"):"",c=l("w-0 h-0"),p={default:c,entering:c,entered:e,exiting:e,exited:c};return t?i.createElement(d,{className:l(g("icon"),"animate-spin shrink-0",m,p.default,p[s]),style:{transition:"width 150ms"}}):i.createElement(r,{className:l(g("icon"),"shrink-0",e,m)})},b=i.forwardRef(((c,d)=>{const{icon:b,iconPosition:x=a.Left,size:w=s.SM,color:j,variant:S="primary",disabled:v,loading:C=!1,loadingText:E,children:N,tooltip:P,className:y}=c,z=t(c,["icon","iconPosition","size","color","variant","disabled","loading","loadingText","children","tooltip","className"]),T=b,k=C||v,B=void 0!==T||C,M=C&&E,R=!(!N&&!M),I=l(p[w].height,p[w].width),L="light"!==S?l("rounded-tremor-default border","shadow-tremor-input","dark:shadow-dark-tremor-input"):"",O=u(S,j),X=f(S)[w],{tooltipProps:Y,getReferenceProps:q}=o(300),[A,D]=n({timeout:50});return r((()=>{D(C)}),[C]),i.createElement("button",Object.assign({ref:m([d,Y.refs.setReference]),className:l(g("root"),"shrink-0 inline-flex justify-center items-center group font-medium outline-none",L,X.paddingX,X.paddingY,X.fontSize,O.textColor,O.bgColor,O.borderColor,O.hoverBorderColor,k?"opacity-50 cursor-not-allowed":l(u(S,j).hoverTextColor,u(S,j).hoverBgColor,u(S,j).hoverBorderColor),y),disabled:k},q,z),i.createElement(e,Object.assign({text:P},Y)),B&&x!==a.Right?i.createElement(h,{loading:C,iconSize:I,iconPosition:x,Icon:T,transitionStatus:A.status,needMargin:R}):null,M||N?i.createElement("span",{className:l(g("text"),"text-tremor-default whitespace-nowrap")},M?E:N):null,B&&x===a.Right?i.createElement(h,{loading:C,iconSize:I,iconPosition:x,Icon:T,transitionStatus:A.status,needMargin:R}):null)}));b.displayName="Button";export{h as ButtonIconOrSpinner,b as default};
