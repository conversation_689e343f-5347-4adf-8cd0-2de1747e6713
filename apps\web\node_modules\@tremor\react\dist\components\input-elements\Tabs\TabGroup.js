'use client';
import{__rest as e}from"tslib";import{Tab as r}from"@headlessui/react";import{tremorTwMerge as a}from"../../../lib/tremorTwMerge.js";import{makeClassName as o}from"../../../lib/utils.js";import t from"react";const n=o("TabGroup"),s=t.forwardRef(((o,s)=>{const{defaultIndex:d,index:l,onIndexChange:i,children:m,className:f}=o,c=e(o,["defaultIndex","index","onIndexChange","children","className"]);return t.createElement(r.Group,Object.assign({as:"div",ref:s,defaultIndex:d,selectedIndex:l,onChange:i,className:a(n("root"),"w-full",f)},c),m)}));s.displayName="TabGroup";export{s as default};
