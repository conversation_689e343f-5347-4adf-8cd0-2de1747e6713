'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { aiInterviewService } from '@/services/aiInterviewService'
import {
  ArrowLeft,
  ArrowRight,
  Brain,
  Briefcase,
  Clock,
  Target,
  Video,
  Mic,
  FileText,
  Settings,
  Zap,
  Users,
  Building,
  Code,
  TrendingUp,
  Heart,
  Palette
} from 'lucide-react'

interface InterviewSetup {
  title: string
  jobTitle: string
  company: string
  industry: string
  difficulty: 'easy' | 'medium' | 'hard'
  duration: number
  questionTypes: string[]
  interviewType: 'video' | 'audio' | 'text'
  jobDescription: string
  customQuestions: string[]
}

export default function NewInterviewPage() {
  const router = useRouter()
  const [currentStep, setCurrentStep] = useState(1)
  const [isCreating, setIsCreating] = useState(false)
  
  const [setup, setSetup] = useState<InterviewSetup>({
    title: '',
    jobTitle: '',
    company: '',
    industry: '',
    difficulty: 'medium',
    duration: 30,
    questionTypes: ['behavioral', 'technical'],
    interviewType: 'video',
    jobDescription: '',
    customQuestions: []
  })

  const industries = [
    { id: 'technology', name: 'Technology', icon: Code, color: 'blue' },
    { id: 'finance', name: 'Finance', icon: TrendingUp, color: 'green' },
    { id: 'healthcare', name: 'Healthcare', icon: Heart, color: 'red' },
    { id: 'consulting', name: 'Consulting', icon: Users, color: 'purple' },
    { id: 'design', name: 'Design', icon: Palette, color: 'pink' },
    { id: 'other', name: 'Other', icon: Building, color: 'gray' }
  ]

  const questionTypes = [
    { id: 'behavioral', name: 'Behavioral', description: 'Past experiences and situations' },
    { id: 'technical', name: 'Technical', description: 'Role-specific technical knowledge' },
    { id: 'situational', name: 'Situational', description: 'Hypothetical scenarios' },
    { id: 'company-specific', name: 'Company-Specific', description: 'Company culture and values' }
  ]

  const interviewTypes = [
    {
      id: 'video' as const,
      name: 'Video Interview',
      description: 'Full video recording with AI analysis of verbal and non-verbal communication',
      icon: Video,
      features: ['Video recording', 'Body language analysis', 'Eye contact tracking', 'Professional presence']
    },
    {
      id: 'audio' as const,
      name: 'Audio Interview',
      description: 'Audio-only recording focusing on verbal communication and content',
      icon: Mic,
      features: ['Audio recording', 'Speech analysis', 'Pace and clarity', 'Content quality']
    },
    {
      id: 'text' as const,
      name: 'Text Interview',
      description: 'Written responses with AI analysis of structure and content',
      icon: FileText,
      features: ['Written responses', 'Structure analysis', 'Grammar check', 'Content depth']
    }
  ]

  const handleNext = () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1)
    }
  }

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleCreateInterview = async () => {
    setIsCreating(true)
    try {
      // Generate questions based on setup
      const questions = await aiInterviewService.generateQuestions({
        jobTitle: setup.jobTitle,
        industry: setup.industry,
        difficulty: setup.difficulty,
        count: Math.floor(setup.duration / 5), // Roughly 5 minutes per question
        types: setup.questionTypes
      })

      // Store interview setup in sessionStorage
      sessionStorage.setItem('interviewSetup', JSON.stringify({
        ...setup,
        questions
      }))

      // Navigate to practice page
      router.push('/dashboard/interviews/practice')
    } catch (error) {
      console.error('Error creating interview:', error)
    } finally {
      setIsCreating(false)
    }
  }

  const updateSetup = (updates: Partial<InterviewSetup>) => {
    setSetup(prev => ({ ...prev, ...updates }))
  }

  const toggleQuestionType = (type: string) => {
    const currentTypes = setup.questionTypes
    if (currentTypes.includes(type)) {
      updateSetup({ questionTypes: currentTypes.filter(t => t !== type) })
    } else {
      updateSetup({ questionTypes: [...currentTypes, type] })
    }
  }

  const getStepTitle = (step: number) => {
    switch (step) {
      case 1: return 'Basic Information'
      case 2: return 'Interview Type & Settings'
      case 3: return 'Question Configuration'
      case 4: return 'Review & Create'
      default: return 'Setup'
    }
  }

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.back()}
            className="flex items-center space-x-2"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Back</span>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center space-x-2">
              <Brain className="h-8 w-8 text-blue-600" />
              <span>Create New Interview</span>
            </h1>
            <p className="text-gray-600 mt-1">
              Set up a personalized AI-powered interview session
            </p>
          </div>
        </div>
      </div>

      {/* Progress Steps */}
      <div className="flex items-center justify-between">
        {[1, 2, 3, 4].map((step) => (
          <div key={step} className="flex items-center">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
              step <= currentStep 
                ? 'bg-blue-600 text-white' 
                : 'bg-gray-200 text-gray-600'
            }`}>
              {step}
            </div>
            {step < 4 && (
              <div className={`w-16 h-1 mx-2 ${
                step < currentStep ? 'bg-blue-600' : 'bg-gray-200'
              }`} />
            )}
          </div>
        ))}
      </div>

      {/* Step Content */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <span>Step {currentStep}: {getStepTitle(currentStep)}</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Step 1: Basic Information */}
          {currentStep === 1 && (
            <div className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="title">Interview Title</Label>
                <Input
                  id="title"
                  value={setup.title}
                  onChange={(e) => updateSetup({ title: e.target.value })}
                  placeholder="e.g., Software Engineer Interview Practice"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="jobTitle">Job Title</Label>
                  <Input
                    id="jobTitle"
                    value={setup.jobTitle}
                    onChange={(e) => updateSetup({ jobTitle: e.target.value })}
                    placeholder="e.g., Senior Software Engineer"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="company">Company (Optional)</Label>
                  <Input
                    id="company"
                    value={setup.company}
                    onChange={(e) => updateSetup({ company: e.target.value })}
                    placeholder="e.g., Google, Microsoft"
                  />
                </div>
              </div>

              <div className="space-y-3">
                <Label>Industry</Label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {industries.map((industry) => {
                    const Icon = industry.icon
                    return (
                      <div
                        key={industry.id}
                        className={`p-4 border rounded-lg cursor-pointer transition-all ${
                          setup.industry === industry.id
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => updateSetup({ industry: industry.id })}
                      >
                        <div className="flex items-center space-x-3">
                          <Icon className={`h-5 w-5 text-${industry.color}-600`} />
                          <span className="font-medium">{industry.name}</span>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </div>
            </div>
          )}

          {/* Step 2: Interview Type & Settings */}
          {currentStep === 2 && (
            <div className="space-y-6">
              <div className="space-y-3">
                <Label>Interview Type</Label>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {interviewTypes.map((type) => {
                    const Icon = type.icon
                    return (
                      <div
                        key={type.id}
                        className={`p-4 border rounded-lg cursor-pointer transition-all ${
                          setup.interviewType === type.id
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => updateSetup({ interviewType: type.id })}
                      >
                        <div className="space-y-3">
                          <div className="flex items-center space-x-2">
                            <Icon className="h-5 w-5 text-blue-600" />
                            <span className="font-medium">{type.name}</span>
                          </div>
                          <p className="text-sm text-gray-600">{type.description}</p>
                          <div className="space-y-1">
                            {type.features.map((feature, index) => (
                              <div key={index} className="flex items-center space-x-2 text-xs text-gray-500">
                                <div className="w-1 h-1 bg-gray-400 rounded-full" />
                                <span>{feature}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="duration">Duration (minutes)</Label>
                  <select
                    id="duration"
                    value={setup.duration}
                    onChange={(e) => updateSetup({ duration: Number(e.target.value) })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value={15}>15 minutes</option>
                    <option value={30}>30 minutes</option>
                    <option value={45}>45 minutes</option>
                    <option value={60}>60 minutes</option>
                  </select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="difficulty">Difficulty Level</Label>
                  <select
                    id="difficulty"
                    value={setup.difficulty}
                    onChange={(e) => updateSetup({ difficulty: e.target.value as 'easy' | 'medium' | 'hard' })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="easy">Easy</option>
                    <option value="medium">Medium</option>
                    <option value="hard">Hard</option>
                  </select>
                </div>
              </div>
            </div>
          )}

          {/* Step 3: Question Configuration */}
          {currentStep === 3 && (
            <div className="space-y-6">
              <div className="space-y-3">
                <Label>Question Types</Label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {questionTypes.map((type) => (
                    <div
                      key={type.id}
                      className={`p-4 border rounded-lg cursor-pointer transition-all ${
                        setup.questionTypes.includes(type.id)
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => toggleQuestionType(type.id)}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium">{type.name}</h4>
                          <p className="text-sm text-gray-600">{type.description}</p>
                        </div>
                        {setup.questionTypes.includes(type.id) && (
                          <Badge variant="default">Selected</Badge>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="jobDescription">Job Description (Optional)</Label>
                <Textarea
                  id="jobDescription"
                  value={setup.jobDescription}
                  onChange={(e) => updateSetup({ jobDescription: e.target.value })}
                  placeholder="Paste the job description here for more targeted questions..."
                  rows={4}
                />
                <p className="text-sm text-gray-500">
                  Adding a job description helps our AI generate more relevant questions
                </p>
              </div>
            </div>
          )}

          {/* Step 4: Review & Create */}
          {currentStep === 4 && (
            <div className="space-y-6">
              <div className="bg-blue-50 p-6 rounded-lg">
                <h3 className="text-lg font-medium text-blue-900 mb-4">Interview Summary</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-blue-800">Title:</span>
                    <span className="ml-2">{setup.title || 'Untitled Interview'}</span>
                  </div>
                  <div>
                    <span className="font-medium text-blue-800">Job Title:</span>
                    <span className="ml-2">{setup.jobTitle}</span>
                  </div>
                  <div>
                    <span className="font-medium text-blue-800">Industry:</span>
                    <span className="ml-2 capitalize">{setup.industry}</span>
                  </div>
                  <div>
                    <span className="font-medium text-blue-800">Duration:</span>
                    <span className="ml-2">{setup.duration} minutes</span>
                  </div>
                  <div>
                    <span className="font-medium text-blue-800">Type:</span>
                    <span className="ml-2 capitalize">{setup.interviewType}</span>
                  </div>
                  <div>
                    <span className="font-medium text-blue-800">Difficulty:</span>
                    <span className="ml-2 capitalize">{setup.difficulty}</span>
                  </div>
                </div>
                <div className="mt-4">
                  <span className="font-medium text-blue-800">Question Types:</span>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {setup.questionTypes.map((type) => (
                      <Badge key={type} variant="secondary" className="capitalize">
                        {type.replace('-', ' ')}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-4 p-4 bg-green-50 rounded-lg">
                <Zap className="h-8 w-8 text-green-600" />
                <div>
                  <h4 className="font-medium text-green-900">AI-Powered Features</h4>
                  <p className="text-sm text-green-700">
                    Your interview will include real-time AI analysis, personalized feedback, and performance scoring
                  </p>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          onClick={handlePrevious}
          disabled={currentStep === 1}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>

        {currentStep < 4 ? (
          <Button
            onClick={handleNext}
            disabled={
              (currentStep === 1 && (!setup.jobTitle || !setup.industry)) ||
              (currentStep === 3 && setup.questionTypes.length === 0)
            }
          >
            Next
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        ) : (
          <Button
            onClick={handleCreateInterview}
            disabled={isCreating || !setup.jobTitle || !setup.industry}
            className="flex items-center space-x-2"
          >
            {isCreating ? (
              <>
                <Settings className="mr-2 h-4 w-4 animate-spin" />
                <span>Creating Interview...</span>
              </>
            ) : (
              <>
                <Brain className="mr-2 h-4 w-4" />
                <span>Start Interview</span>
              </>
            )}
          </Button>
        )}
      </div>
    </div>
  )
}
