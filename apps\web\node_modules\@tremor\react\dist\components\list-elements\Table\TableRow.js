import{__rest as e}from"tslib";import r from"react";import{tremorTwMerge as t}from"../../../lib/tremorTwMerge.js";import{makeClassName as a}from"../../../lib/utils.js";const l=a("TableRow"),m=r.forwardRef(((a,m)=>{const{children:o,className:s}=a,i=e(a,["children","className"]);return r.createElement(r.Fragment,null,r.createElement("tr",Object.assign({ref:m,className:t(l("row"),s)},i),o))}));m.displayName="TableRow";export{m as default};
