"use strict";var e=require("tslib"),r=require("react"),t=require("../../util-elements/Tooltip/Tooltip.cjs"),a=require("../../../lib/theme.cjs"),l=require("../../../lib/tremorTwMerge.cjs"),o=require("../../../lib/utils.cjs");function s(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var n=s(r);const i=o.makeClassName("ProgressBar"),m=n.default.forwardRef(((r,s)=>{const{value:m,label:u,color:c,tooltip:d,showAnimation:f=!1,className:g}=r,b=e.__rest(r,["value","label","color","tooltip","showAnimation","className"]),{tooltipProps:p,getReferenceProps:w}=t.useTooltip();return n.default.createElement(n.default.Fragment,null,n.default.createElement(t.default,Object.assign({text:d},p)),n.default.createElement("div",Object.assign({ref:s,className:l.tremorTwMerge(i("root"),"flex items-center w-full",g)},b),n.default.createElement("div",Object.assign({ref:p.refs.setReference,className:l.tremorTwMerge(i("progressBarWrapper"),"relative flex items-center w-full rounded-tremor-full bg-opacity-20 h-2",c?o.getColorClassNames(c,a.colorPalette.background).bgColor:"bg-tremor-brand-muted/50 dark:bg-dark-tremor-brand-muted")},w),n.default.createElement("div",{className:l.tremorTwMerge(i("progressBar"),"flex-col h-full rounded-tremor-full",c?o.getColorClassNames(c,a.colorPalette.background).bgColor:"bg-tremor-brand dark:bg-dark-tremor-brand",f?"transition-all duration-300 ease-in-out":""),style:{width:`${m}%`}})),u?n.default.createElement("div",{className:l.tremorTwMerge(i("labelWrapper"),"w-16 truncate text-right ml-2","text-tremor-content-emphasis","dark:text-dark-tremor-content-emphasis")},n.default.createElement("p",{className:l.tremorTwMerge(i("label"),"shrink-0 whitespace-nowrap truncate text-tremor-default")},u)):null))}));m.displayName="ProgressBar",module.exports=m;
