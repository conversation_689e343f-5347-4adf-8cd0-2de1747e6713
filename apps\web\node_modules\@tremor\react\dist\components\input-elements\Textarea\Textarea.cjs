'use client';
"use strict";var e=require("tslib"),r=require("../selectUtils.cjs"),t=require("../../../hooks/useInternalState.cjs"),a=require("react"),l=require("../../../lib/tremorTwMerge.cjs"),o=require("../../../lib/utils.cjs");function s(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var u=s(a);const n=o.makeClassName("Textarea"),d=u.default.forwardRef(((s,d)=>{const{value:c,defaultValue:i="",placeholder:m="Type...",error:f=!1,errorMessage:g,disabled:h=!1,className:b,onChange:p,onValueChange:x,autoHeight:k=!1}=s,v=e.__rest(s,["value","defaultValue","placeholder","error","errorMessage","disabled","className","onChange","onValueChange","autoHeight"]),[w,C]=t(i,c),T=a.useRef(null),j=r.hasValue(w);return a.useEffect((()=>{const e=T.current;if(k&&e){e.style.height="60px";const r=e.scrollHeight;e.style.height=r+"px"}}),[k,T,w]),u.default.createElement(u.default.Fragment,null,u.default.createElement("textarea",Object.assign({ref:o.mergeRefs([T,d]),value:w,placeholder:m,disabled:h,className:l.tremorTwMerge(n("Textarea"),"w-full flex items-center outline-none rounded-tremor-default px-3 py-2 text-tremor-default focus:ring-2 transition duration-100 border","shadow-tremor-input focus:border-tremor-brand-subtle focus:ring-tremor-brand-muted","dark:shadow-dark-tremor-input focus:dark:border-dark-tremor-brand-subtle focus:dark:ring-dark-tremor-brand-muted",r.getSelectButtonColors(j,h,f),h?"placeholder:text-tremor-content-subtle dark:placeholder:text-dark-tremor-content-subtle":"placeholder:text-tremor-content dark:placeholder:text-dark-tremor-content",b),"data-testid":"text-area",onChange:e=>{null==p||p(e),C(e.target.value),null==x||x(e.target.value)}},v)),f&&g?u.default.createElement("p",{className:l.tremorTwMerge(n("errorMessage"),"text-sm text-red-500 mt-1")},g):null)}));d.displayName="Textarea",module.exports=d;
