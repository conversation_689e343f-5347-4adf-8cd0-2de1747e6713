'use client';
import{__rest as e}from"tslib";import t,{useRef as r}from"react";import a from"../../../assets/PlusIcon.js";import n from"../../../assets/MinusIcon.js";import{tremorTwMerge as o}from"../../../lib/tremorTwMerge.js";import{mergeRefs as l,makeClassName as s}from"../../../lib/utils.js";import u from"../BaseInput.js";const p="flex mx-auto text-tremor-content-subtle dark:text-dark-tremor-content-subtle",c="cursor-pointer hover:text-tremor-content dark:hover:text-dark-tremor-content",i=t.forwardRef(((i,d)=>{const{onSubmit:m,enableStepper:b=!0,disabled:v,onValueChange:f,onChange:k}=i,w=e(i,["onSubmit","enableStepper","disabled","onValueChange","onChange"]),y=r(null),[h,x]=t.useState(!1),g=t.useCallback((()=>{x(!0)}),[]),C=t.useCallback((()=>{x(!1)}),[]),[D,E]=t.useState(!1),N=t.useCallback((()=>{E(!0)}),[]),I=t.useCallback((()=>{E(!1)}),[]);return t.createElement(u,Object.assign({type:"number",ref:l([y,d]),disabled:v,makeInputClassName:s("NumberInput"),onKeyDown:e=>{var t;if("Enter"===e.key&&!e.ctrlKey&&!e.altKey&&!e.shiftKey){const e=null===(t=y.current)||void 0===t?void 0:t.value;null==m||m(parseFloat(null!=e?e:""))}"ArrowDown"===e.key&&g(),"ArrowUp"===e.key&&N()},onKeyUp:e=>{"ArrowDown"===e.key&&C(),"ArrowUp"===e.key&&I()},onChange:e=>{v||(null==f||f(parseFloat(e.target.value)),null==k||k(e))},stepper:b?t.createElement("div",{className:o("flex justify-center align-middle")},t.createElement("div",{tabIndex:-1,onClick:e=>e.preventDefault(),onMouseDown:e=>e.preventDefault(),onTouchStart:e=>{e.cancelable&&e.preventDefault()},onMouseUp:()=>{var e,t;v||(null===(e=y.current)||void 0===e||e.stepDown(),null===(t=y.current)||void 0===t||t.dispatchEvent(new Event("input",{bubbles:!0})))},className:o(!v&&c,p,"group py-[10px] px-2.5 border-l border-tremor-border dark:border-dark-tremor-border")},t.createElement(n,{"data-testid":"step-down",className:(h?"scale-95":"")+" h-4 w-4 duration-75 transition group-active:scale-95"})),t.createElement("div",{tabIndex:-1,onClick:e=>e.preventDefault(),onMouseDown:e=>e.preventDefault(),onTouchStart:e=>{e.cancelable&&e.preventDefault()},onMouseUp:()=>{var e,t;v||(null===(e=y.current)||void 0===e||e.stepUp(),null===(t=y.current)||void 0===t||t.dispatchEvent(new Event("input",{bubbles:!0})))},className:o(!v&&c,p,"group py-[10px] px-2.5 border-l border-tremor-border dark:border-dark-tremor-border")},t.createElement(a,{"data-testid":"step-up",className:(D?"scale-95":"")+" h-4 w-4 duration-75 transition group-active:scale-95"}))):null},w))}));i.displayName="NumberInput";export{i as default};
