'use client';
import{__rest as e}from"tslib";import r from"react";import t,{useTooltip as a}from"../../util-elements/Tooltip/Tooltip.js";import{DeltaTypes as s}from"../../../lib/constants.js";import{tremorTwMerge as l}from"../../../lib/tremorTwMerge.js";import{makeClassName as o,mapInputsToDeltaType as i}from"../../../lib/utils.js";import{colors as n}from"./styles.js";const m=o("DeltaBar"),c=r.forwardRef(((o,c)=>{const{value:d,isIncreasePositive:f=!0,showAnimation:u=!1,className:b,tooltip:p}=o,g=e(o,["value","isIncreasePositive","showAnimation","className","tooltip"]),v=i((e=>e>=0?s.Increase:s.Decrease)(d),f),{tooltipProps:h,getReferenceProps:j}=a();return r.createElement(r.Fragment,null,r.createElement(t,Object.assign({text:p},h)),r.createElement("div",Object.assign({ref:c,className:l(m("root"),"relative flex items-center w-full rounded-tremor-full h-2","bg-tremor-background-subtle","dark:bg-dark-tremor-background-subtle",b)},g),r.createElement("div",{className:"flex justify-end h-full w-1/2"},d<0?r.createElement("div",Object.assign({ref:h.refs.setReference,className:l(m("negativeDeltaBar"),"rounded-l-tremor-full",n[v].bgColor),style:{width:`${Math.abs(d)}%`,transition:u?"all duration-300":""}},j)):null),r.createElement("div",{className:l(m("separator"),"ring-2 z-10 rounded-tremor-full h-4 w-1","ring-tremor-brand-inverted bg-tremor-background-emphasis","dark:ring-dark-tremor-brand-inverted dark:bg-dark-tremor-background-emphasis")}),r.createElement("div",{className:l(m("positiveDeltaBarWrapper"),"flex justify-start h-full w-1/2")},d>=0?r.createElement("div",Object.assign({ref:h.refs.setReference,className:l(m("positiveDeltaBar"),"rounded-r-tremor-full",n[v].bgColor),style:{width:`${Math.abs(d)}%`,transition:u?"all 1s":""}},j)):null)))}));c.displayName="DeltaBar";export{c as default};
