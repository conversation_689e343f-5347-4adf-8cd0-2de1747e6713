'use client'

import { useEffect } from 'react'
import { useAuthStore } from '@/stores/auth'

interface AuthProviderProps {
  children: React.ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const { isAuthenticated, user, initialize } = useAuthStore()

  useEffect(() => {
    // Only initialize if we have a token but no user data
    const token = localStorage.getItem('auth_token')
    if (token && !user && !isAuthenticated) {
      console.log('AuthProvider - Initializing auth state')
      initialize().catch((error) => {
        console.error('AuthProvider - Auth initialization failed:', error)
      })
    }
  }, [user, isAuthenticated, initialize])

  return <>{children}</>
}
