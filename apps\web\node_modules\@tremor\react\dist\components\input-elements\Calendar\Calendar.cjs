'use client';
"use strict";var e=require("tslib"),t=require("react"),r=require("react-day-picker"),a=require("../../../assets/ArrowLeftHeadIcon.cjs"),o=require("../../../assets/ArrowRightHeadIcon.cjs"),n=require("../../../assets/DoubleArrowLeftHeadIcon.cjs"),s=require("../../../assets/DoubleArrowRightHeadIcon.cjs"),d=require("date-fns"),l=require("../../text-elements/Text/Text.cjs"),c=require("./NavButton.cjs");function u(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var i=u(t);function m(t){var{mode:u,defaultMonth:m,selected:b,onSelect:f,locale:k,disabled:x,enableYearNavigation:h,classNames:v,weekStartsOn:p=0}=t,g=e.__rest(t,["mode","defaultMonth","selected","onSelect","locale","disabled","enableYearNavigation","classNames","weekStartsOn"]);return i.default.createElement(r.DayPicker,Object.assign({showOutsideDays:!0,mode:u,defaultMonth:m,selected:b,onSelect:f,locale:k,disabled:x,weekStartsOn:p,classNames:Object.assign({months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",month:"space-y-4",caption:"flex justify-center pt-2 relative items-center",caption_label:"text-tremor-default text-tremor-content-emphasis dark:text-dark-tremor-content-emphasis font-medium",nav:"space-x-1 flex items-center",nav_button:"flex items-center justify-center p-1 h-7 w-7 outline-none focus:ring-2 transition duration-100 border border-tremor-border dark:border-dark-tremor-border hover:bg-tremor-background-muted dark:hover:bg-dark-tremor-background-muted rounded-tremor-small focus:border-tremor-brand-subtle dark:focus:border-dark-tremor-brand-subtle focus:ring-tremor-brand-muted dark:focus:ring-dark-tremor-brand-muted text-tremor-content-subtle dark:text-dark-tremor-content-subtle hover:text-tremor-content dark:hover:text-dark-tremor-content",nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-y-1",head_row:"flex",head_cell:"w-9 font-normal text-center text-tremor-content-subtle dark:text-dark-tremor-content-subtle",row:"flex w-full mt-0.5",cell:"text-center p-0 relative focus-within:relative text-tremor-default text-tremor-content-emphasis dark:text-dark-tremor-content-emphasis",day:"h-9 w-9 p-0 hover:bg-tremor-background-subtle dark:hover:bg-dark-tremor-background-subtle outline-tremor-brand dark:outline-dark-tremor-brand rounded-tremor-default",day_today:"font-bold",day_selected:"aria-selected:bg-tremor-background-emphasis aria-selected:text-tremor-content-inverted dark:aria-selected:bg-dark-tremor-background-emphasis dark:aria-selected:text-dark-tremor-content-inverted ",day_disabled:"text-tremor-content-subtle dark:text-dark-tremor-content-subtle disabled:hover:bg-transparent",day_outside:"text-tremor-content-subtle dark:text-dark-tremor-content-subtle"},v),components:{IconLeft:t=>{var r=e.__rest(t,[]);return i.default.createElement(a,Object.assign({className:"h-4 w-4"},r))},IconRight:t=>{var r=e.__rest(t,[]);return i.default.createElement(o,Object.assign({className:"h-4 w-4"},r))},Caption:t=>{var u=e.__rest(t,[]);const{goToMonth:m,nextMonth:b,previousMonth:f,currentMonth:x}=r.useNavigation();return i.default.createElement("div",{className:"flex justify-between items-center"},i.default.createElement("div",{className:"flex items-center space-x-1"},h&&i.default.createElement(c.NavButton,{onClick:()=>x&&m(d.addYears(x,-1)),icon:n}),i.default.createElement(c.NavButton,{onClick:()=>f&&m(f),icon:a})),i.default.createElement(l,{className:"text-tremor-default tabular-nums capitalize text-tremor-content-emphasis dark:text-dark-tremor-content-emphasis font-medium"},d.format(u.displayMonth,"LLLL yyy",{locale:k})),i.default.createElement("div",{className:"flex items-center space-x-1"},i.default.createElement(c.NavButton,{onClick:()=>b&&m(b),icon:o}),h&&i.default.createElement(c.NavButton,{onClick:()=>x&&m(d.addYears(x,1)),icon:s})))}}},g))}m.displayName="DateRangePicker",module.exports=m;
