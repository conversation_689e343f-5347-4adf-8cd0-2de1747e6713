"use strict";var e=require("tslib"),t=require("react"),r=require("../../../lib/tremorTwMerge.cjs"),a=require("../../../lib/utils.cjs");function l(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var s=l(t);const c=a.makeClassName("TableHead"),n=s.default.forwardRef(((t,a)=>{const{children:l,className:n}=t,u=e.__rest(t,["children","className"]);return s.default.createElement(s.default.Fragment,null,s.default.createElement("thead",Object.assign({ref:a,className:r.tremorTwMerge(c("root"),"text-left","text-tremor-content","dark:text-dark-tremor-content",n)},u),l))}));n.displayName="TableHead",module.exports=n;
