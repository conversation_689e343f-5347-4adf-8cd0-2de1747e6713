import{__rest as e}from"tslib";import r from"react";import t,{useTooltip as a}from"../../util-elements/Tooltip/Tooltip.js";import{colorPalette as o}from"../../../lib/theme.js";import{tremorTwMerge as l}from"../../../lib/tremorTwMerge.js";import{getColorClassNames as s,makeClassName as m}from"../../../lib/utils.js";const n=m("ProgressBar"),i=r.forwardRef(((m,i)=>{const{value:c,label:d,color:p,tooltip:b,showAnimation:u=!1,className:f}=m,g=e(m,["value","label","color","tooltip","showAnimation","className"]),{tooltipProps:h,getReferenceProps:w}=a();return r.createElement(r.Fragment,null,r.createElement(t,Object.assign({text:b},h)),r.createElement("div",Object.assign({ref:i,className:l(n("root"),"flex items-center w-full",f)},g),r.createElement("div",Object.assign({ref:h.refs.setReference,className:l(n("progressBarWrapper"),"relative flex items-center w-full rounded-tremor-full bg-opacity-20 h-2",p?s(p,o.background).bgColor:"bg-tremor-brand-muted/50 dark:bg-dark-tremor-brand-muted")},w),r.createElement("div",{className:l(n("progressBar"),"flex-col h-full rounded-tremor-full",p?s(p,o.background).bgColor:"bg-tremor-brand dark:bg-dark-tremor-brand",u?"transition-all duration-300 ease-in-out":""),style:{width:`${c}%`}})),d?r.createElement("div",{className:l(n("labelWrapper"),"w-16 truncate text-right ml-2","text-tremor-content-emphasis","dark:text-dark-tremor-content-emphasis")},r.createElement("p",{className:l(n("label"),"shrink-0 whitespace-nowrap truncate text-tremor-default")},d)):null))}));i.displayName="ProgressBar";export{i as default};
