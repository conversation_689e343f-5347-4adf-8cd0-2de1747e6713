'use client';
import{__rest as e}from"tslib";import t from"react";import r,{useTooltip as i}from"../../util-elements/Tooltip/Tooltip.js";import{DeltaTypes as s,Sizes as o}from"../../../lib/constants.js";import{tremorTwMerge as a}from"../../../lib/tremorTwMerge.js";import{mergeRefs as l,makeClassName as n,mapInputsToDeltaType as m}from"../../../lib/utils.js";import{colors as c,iconSizes as p,deltaIcons as d,badgeProportionsWithText as f,badgeProportionsIconOnly as g}from"./styles.js";const b=n("BadgeDelta"),u=t.forwardRef(((n,u)=>{const{deltaType:y=s.Increase,isIncreasePositive:h=!0,size:j=o.SM,tooltip:w,children:x,className:N}=n,T=e(n,["deltaType","isIncreasePositive","size","tooltip","children","className"]),k=d[y],E=m(y,h),P=x?f:g,{tooltipProps:z,getReferenceProps:C}=i();return t.createElement("span",Object.assign({ref:l([u,z.refs.setReference]),className:a(b("root"),"w-max shrink-0 inline-flex justify-center items-center cursor-default rounded-tremor-small ring-1 ring-inset",c[E].bgColor,c[E].textColor,c[E].ringColor,P[j].paddingX,P[j].paddingY,P[j].fontSize,"bg-opacity-10 ring-opacity-20","dark:bg-opacity-5 dark:ring-opacity-60",N)},C,T),t.createElement(r,Object.assign({text:w},z)),t.createElement(k,{className:a(b("icon"),"shrink-0",x?a("-ml-1 mr-1.5"):p[j].height,p[j].width)}),x?t.createElement("span",{className:a(b("text"),"whitespace-nowrap")},x):null)}));u.displayName="BadgeDelta";export{u as default};
