'use client';
import{__rest as e}from"tslib";import t,{useState as o,Fragment as a}from"react";import{ResponsiveContainer as r,<PERSON>Chart as l,CartesianGrid as n,XAxis as i,Label as s,YAxis as c,Tooltip as d,Legend as m,Area as p,Dot as u,Line as k}from"recharts";import y from"../common/ChartLegend.js";import v from"../common/ChartTooltip.js";import f from"../common/NoData.js";import{constructCategoryColors as h,hasOnlyOneValueForThisKey as g,getYAxisDomain as x}from"../common/utils.js";import{BaseColors as b}from"../../../lib/constants.js";import{themeColorRange as E,colorPalette as L}from"../../../lib/theme.js";import{tremorTwMerge as w}from"../../../lib/tremorTwMerge.js";import{getColorClassNames as C,defaultValueFormatter as A}from"../../../lib/utils.js";const N=t.forwardRef(((N,T)=>{const{data:j=[],categories:G=[],index:K,stack:D=!1,colors:O=E,valueFormatter:W=A,startEndOnly:V=!1,showXAxis:S=!0,showYAxis:F=!0,yAxisWidth:X=56,intervalType:M="equidistantPreserveStart",showAnimation:P=!1,animationDuration:Y=900,showTooltip:q=!0,showLegend:z=!0,showGridLines:B=!0,showGradient:H=!0,autoMinValue:I=!1,curveType:R="linear",minValue:$,maxValue:J,connectNulls:Q=!1,allowDecimals:U=!0,noDataText:Z,className:_,onValueChange:ee,enableLegendSlider:te=!1,customTooltip:oe,rotateLabelX:ae,padding:re=(!S&&!F||V&&!F?{left:0,right:0}:{left:20,right:20}),tickGap:le=5,xAxisLabel:ne,yAxisLabel:ie}=N,se=e(N,["data","categories","index","stack","colors","valueFormatter","startEndOnly","showXAxis","showYAxis","yAxisWidth","intervalType","showAnimation","animationDuration","showTooltip","showLegend","showGridLines","showGradient","autoMinValue","curveType","minValue","maxValue","connectNulls","allowDecimals","noDataText","className","onValueChange","enableLegendSlider","customTooltip","rotateLabelX","padding","tickGap","xAxisLabel","yAxisLabel"]),ce=oe,[de,me]=o(60),[pe,ue]=o(void 0),[ke,ye]=o(void 0),ve=h(G,O),fe=x(I,$,J),he=!!ee;function ge(e){he&&(e===ke&&!pe||g(j,e)&&pe&&pe.dataKey===e?(ye(void 0),null==ee||ee(null)):(ye(e),null==ee||ee({eventType:"category",categoryClicked:e})),ue(void 0))}return t.createElement("div",Object.assign({ref:T,className:w("w-full h-80",_)},se),t.createElement(r,{className:"h-full w-full"},(null==j?void 0:j.length)?t.createElement(l,{data:j,onClick:he&&(ke||pe)?()=>{ue(void 0),ye(void 0),null==ee||ee(null)}:void 0,margin:{bottom:ne?30:void 0,left:ie?20:void 0,right:ie?5:void 0,top:5}},B?t.createElement(n,{className:w("stroke-1","stroke-tremor-border","dark:stroke-dark-tremor-border"),horizontal:!0,vertical:!1}):null,t.createElement(i,{padding:re,hide:!S,dataKey:K,tick:{transform:"translate(0, 6)"},ticks:V?[j[0][K],j[j.length-1][K]]:void 0,fill:"",stroke:"",className:w("text-tremor-label","fill-tremor-content","dark:fill-dark-tremor-content"),interval:V?"preserveStartEnd":M,tickLine:!1,axisLine:!1,minTickGap:le,angle:null==ae?void 0:ae.angle,dy:null==ae?void 0:ae.verticalShift,height:null==ae?void 0:ae.xAxisHeight},ne&&t.createElement(s,{position:"insideBottom",offset:-20,className:"fill-tremor-content-emphasis text-tremor-default font-medium dark:fill-dark-tremor-content-emphasis"},ne)),t.createElement(c,{width:X,hide:!F,axisLine:!1,tickLine:!1,type:"number",domain:fe,tick:{transform:"translate(-3, 0)"},fill:"",stroke:"",className:w("text-tremor-label","fill-tremor-content","dark:fill-dark-tremor-content"),tickFormatter:W,allowDecimals:U},ie&&t.createElement(s,{position:"insideLeft",style:{textAnchor:"middle"},angle:-90,offset:-15,className:"fill-tremor-content-emphasis text-tremor-default font-medium dark:fill-dark-tremor-content-emphasis"},ie)),t.createElement(d,{wrapperStyle:{outline:"none"},isAnimationActive:!1,cursor:{stroke:"#d1d5db",strokeWidth:1},content:q?({active:e,payload:o,label:a})=>ce?t.createElement(ce,{payload:null==o?void 0:o.map((e=>{var t;return Object.assign(Object.assign({},e),{color:null!==(t=ve.get(e.dataKey))&&void 0!==t?t:b.Gray})})),active:e,label:a}):t.createElement(v,{active:e,payload:o,label:a,valueFormatter:W,categoryColors:ve}):t.createElement(t.Fragment,null),position:{y:0}}),z?t.createElement(m,{verticalAlign:"top",height:de,content:({payload:e})=>y({payload:e},ve,me,ke,he?e=>ge(e):void 0,te)}):null,G.map((e=>{var o,a,r;const l=(null!==(o=ve.get(e))&&void 0!==o?o:b.Gray).replace("#","");return t.createElement("defs",{key:e},H?t.createElement("linearGradient",{className:C(null!==(a=ve.get(e))&&void 0!==a?a:b.Gray,L.text).textColor,id:l,x1:"0",y1:"0",x2:"0",y2:"1"},t.createElement("stop",{offset:"5%",stopColor:"currentColor",stopOpacity:pe||ke&&ke!==e?.15:.4}),t.createElement("stop",{offset:"95%",stopColor:"currentColor",stopOpacity:0})):t.createElement("linearGradient",{className:C(null!==(r=ve.get(e))&&void 0!==r?r:b.Gray,L.text).textColor,id:l,x1:"0",y1:"0",x2:"0",y2:"1"},t.createElement("stop",{stopColor:"currentColor",stopOpacity:pe||ke&&ke!==e?.1:.3})))})),G.map((e=>{var o,r;const l=(null!==(o=ve.get(e))&&void 0!==o?o:b.Gray).replace("#","");return t.createElement(p,{className:C(null!==(r=ve.get(e))&&void 0!==r?r:b.Gray,L.text).strokeColor,strokeOpacity:pe||ke&&ke!==e?.3:1,activeDot:e=>{var o;const{cx:a,cy:r,stroke:l,strokeLinecap:n,strokeLinejoin:i,strokeWidth:s,dataKey:c}=e;return t.createElement(u,{className:w("stroke-tremor-background dark:stroke-dark-tremor-background",ee?"cursor-pointer":"",C(null!==(o=ve.get(c))&&void 0!==o?o:b.Gray,L.text).fillColor),cx:a,cy:r,r:5,fill:"",stroke:l,strokeLinecap:n,strokeLinejoin:i,strokeWidth:s,onClick:(t,o)=>function(e,t){t.stopPropagation(),he&&(e.index===(null==pe?void 0:pe.index)&&e.dataKey===(null==pe?void 0:pe.dataKey)||g(j,e.dataKey)&&ke&&ke===e.dataKey?(ye(void 0),ue(void 0),null==ee||ee(null)):(ye(e.dataKey),ue({index:e.index,dataKey:e.dataKey}),null==ee||ee(Object.assign({eventType:"dot",categoryClicked:e.dataKey},e.payload))))}(e,o)})},dot:o=>{var r;const{stroke:l,strokeLinecap:n,strokeLinejoin:i,strokeWidth:s,cx:c,cy:d,dataKey:m,index:p}=o;return g(j,e)&&!(pe||ke&&ke!==e)||(null==pe?void 0:pe.index)===p&&(null==pe?void 0:pe.dataKey)===e?t.createElement(u,{key:p,cx:c,cy:d,r:5,stroke:l,fill:"",strokeLinecap:n,strokeLinejoin:i,strokeWidth:s,className:w("stroke-tremor-background dark:stroke-dark-tremor-background",ee?"cursor-pointer":"",C(null!==(r=ve.get(m))&&void 0!==r?r:b.Gray,L.text).fillColor)}):t.createElement(a,{key:p})},key:e,name:e,type:R,dataKey:e,stroke:"",fill:`url(#${l})`,strokeWidth:2,strokeLinejoin:"round",strokeLinecap:"round",isAnimationActive:P,animationDuration:Y,stackId:D?"a":void 0,connectNulls:Q})})),ee?G.map((e=>t.createElement(k,{className:w("cursor-pointer"),strokeOpacity:0,key:e,name:e,type:R,dataKey:e,stroke:"transparent",fill:"transparent",legendType:"none",tooltipType:"none",strokeWidth:12,connectNulls:Q,onClick:(e,t)=>{t.stopPropagation();const{name:o}=e;ge(o)}}))):null):t.createElement(f,{noDataText:Z})))}));N.displayName="AreaChart";export{N as default};
