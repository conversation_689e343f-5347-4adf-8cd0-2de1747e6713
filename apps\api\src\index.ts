// --- START api/src/index.ts --- //
// Main entry point for AI-InterviewSpark API server
// Sets up Express server with middleware, routes, and WebSocket support

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import { createServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import { config } from './config';
import { initializeDatabase, closeDatabaseConnection } from './database/connection';
import { authenticate } from './middleware/auth';
import { errorHandler } from './middleware/errorHandler';
import { logger } from './utils/logger';

// Import routes
import authRoutes from './routes/auth';
import userRoutes from './routes/users';
import interviewRoutes from './routes/interviews';
import aiRoutes from './routes/ai';
import resumeRoutes from './routes/resumes';
import expertRoutes from './routes/experts';
import analyticsRoutes from './routes/analytics';

// Create Express app
const app = express();
const server = createServer(app);

// Initialize Socket.IO for real-time features
const io = new SocketIOServer(server, {
  cors: {
    origin: config.security.corsOrigin,
    methods: ['GET', 'POST'],
    credentials: true,
  },
});

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));

// CORS configuration
app.use(cors({
  origin: config.security.corsOrigin,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
}));

// Compression middleware
app.use(compression());

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Rate limiting
const limiter = rateLimit({
  windowMs: config.security.rateLimit.windowMs,
  max: config.security.rateLimit.maxRequests,
  message: {
    success: false,
    error: 'Too many requests from this IP, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

app.use('/api/', limiter);

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'AI-InterviewSpark API is running',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
  });
});

// API routes
app.use('/api/auth', authRoutes);
app.use('/api/users', authenticate, userRoutes);
app.use('/api/interviews', authenticate, interviewRoutes);
app.use('/api/ai', authenticate, aiRoutes);
app.use('/api/resumes', authenticate, resumeRoutes);
app.use('/api/experts', authenticate, expertRoutes);
app.use('/api/analytics', authenticate, analyticsRoutes);

// WebSocket connection handling
io.on('connection', (socket) => {
  logger.info(`Client connected: ${socket.id}`);

  // Join interview session room
  socket.on('join-session', (sessionId: string) => {
    socket.join(`session-${sessionId}`);
    logger.info(`Client ${socket.id} joined session ${sessionId}`);
  });

  // Leave interview session room
  socket.on('leave-session', (sessionId: string) => {
    socket.leave(`session-${sessionId}`);
    logger.info(`Client ${socket.id} left session ${sessionId}`);
  });

  // Handle real-time emotion updates
  socket.on('emotion-update', (data: { sessionId: string; emotionData: any }) => {
    socket.to(`session-${data.sessionId}`).emit('emotion-update', data.emotionData);
  });

  // Handle real-time feedback updates
  socket.on('feedback-update', (data: { sessionId: string; feedback: any }) => {
    socket.to(`session-${data.sessionId}`).emit('feedback-update', data.feedback);
  });

  // Handle session status updates
  socket.on('session-status', (data: { sessionId: string; status: string }) => {
    socket.to(`session-${data.sessionId}`).emit('session-status', data.status);
  });

  socket.on('disconnect', () => {
    logger.info(`Client disconnected: ${socket.id}`);
  });
});

// Error handling middleware (must be last)
app.use(errorHandler);

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Route not found',
    path: req.originalUrl,
  });
});

// Graceful shutdown handling
const gracefulShutdown = async (signal: string) => {
  logger.info(`Received ${signal}. Starting graceful shutdown...`);
  
  try {
    // Close database connections
    await closeDatabaseConnection();
    
    // Close HTTP server
    server.close(() => {
      logger.info('HTTP server closed');
      process.exit(0);
    });

    // Force exit after 10 seconds
    setTimeout(() => {
      logger.error('Forced shutdown after timeout');
      process.exit(1);
    }, 10000);
  } catch (error) {
    logger.error('Error during graceful shutdown:', error);
    process.exit(1);
  }
};

// Handle shutdown signals
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Start server
const startServer = async () => {
  try {
    // Initialize database connection
    try {
      await initializeDatabase();
    } catch (error) {
      console.warn('⚠️  Database connection failed, starting server without database...');
      console.warn('   This is expected in development without a PostgreSQL instance');
    }
    
    // Start HTTP server
    server.listen(config.server.port, config.server.host, () => {
      logger.info(`🚀 AI-InterviewSpark API server running on http://${config.server.host}:${config.server.port}`);
      logger.info(`📊 Environment: ${config.server.nodeEnv}`);
      logger.info(`🔐 Authentication: ${config.auth.clerk.secretKey ? 'Clerk' : 'JWT'}`);
      logger.info(`🤖 AI Services: OpenAI=${config.ai.openai.enabled}, Gemini=${config.ai.gemini.enabled}`);
      logger.info(`🎭 Emotional Analysis: Motivel=${config.ai.motivel.enabled}, Moodme=${config.ai.moodme.enabled}`);
    });
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
};

// Export for testing
export { app, server, io };

// Start server if this file is run directly
if (require.main === module) {
  startServer();
} 