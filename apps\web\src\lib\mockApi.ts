import {
  User,
  InterviewSession,
  InterviewConfig,
  Question,
  Answer,
  Feedback,
  PerformanceMetrics,
  Resume,
  ExpertProfile,
  AnalyticsData,
  AuthResponse,
  LoginRequest,
  RegisterRequest
} from '@/types'

// Mock data
const mockUser: User = {
  id: 'user-123',
  email: '<EMAIL>',
  firstName: 'John',
  lastName: 'Doe',
  avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
  role: 'user',
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date(),
  preferences: {
    notifications: true,
    theme: 'light',
    language: 'en'
  }
}

const mockSessions: InterviewSession[] = [
  {
    id: 'session-1',
    userId: 'user-123',
    jobTitle: 'Senior Software Engineer',
    company: 'Google',
    industry: 'Technology',
    status: 'completed',
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-15'),
    config: {
      duration: 45,
      questionTypes: ['technical', 'behavioral'],
      difficulty: 'medium',
      recordingEnabled: true
    },
    questions: [],
    answers: [],
    performanceMetrics: {
      overallScore: 85,
      categoryScores: {
        technical: 82,
        behavioral: 88,
        communication: 85,
        problemSolving: 83
      },
      strengths: ['Clear communication', 'Strong technical knowledge'],
      improvements: ['More specific examples', 'Better structure'],
      recommendations: ['Practice system design', 'Prepare more STAR examples']
    }
  },
  {
    id: 'session-2',
    userId: 'user-123',
    jobTitle: 'Product Manager',
    company: 'Meta',
    industry: 'Technology',
    status: 'completed',
    createdAt: new Date('2024-01-12'),
    updatedAt: new Date('2024-01-12'),
    config: {
      duration: 60,
      questionTypes: ['behavioral', 'case-study'],
      difficulty: 'hard',
      recordingEnabled: true
    },
    questions: [],
    answers: [],
    performanceMetrics: {
      overallScore: 78,
      categoryScores: {
        strategic: 80,
        analytical: 75,
        communication: 82,
        leadership: 76
      },
      strengths: ['Strategic thinking', 'Good communication'],
      improvements: ['Data analysis depth', 'Leadership examples'],
      recommendations: ['Practice case studies', 'Prepare leadership stories']
    }
  },
  {
    id: 'session-3',
    userId: 'user-123',
    jobTitle: 'Data Scientist',
    company: 'Netflix',
    industry: 'Technology',
    status: 'in-progress',
    createdAt: new Date('2024-01-20'),
    updatedAt: new Date('2024-01-20'),
    config: {
      duration: 30,
      questionTypes: ['technical', 'behavioral'],
      difficulty: 'medium',
      recordingEnabled: false
    },
    questions: [],
    answers: []
  }
]

const mockQuestions: Question[] = [
  {
    id: 'q1',
    text: 'Tell me about a challenging project you worked on recently.',
    type: 'behavioral',
    category: 'experience',
    difficulty: 'medium',
    timeLimit: 180,
    followUpQuestions: [
      'What was the biggest challenge?',
      'How did you overcome it?',
      'What would you do differently?'
    ]
  },
  {
    id: 'q2',
    text: 'How would you design a URL shortener like bit.ly?',
    type: 'technical',
    category: 'system-design',
    difficulty: 'hard',
    timeLimit: 300,
    followUpQuestions: [
      'How would you handle scale?',
      'What about analytics?',
      'How would you prevent abuse?'
    ]
  }
]

// Mock API delay
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

export class MockApiClient {
  private token: string | null = null

  // Authentication methods
  async login(credentials: LoginRequest): Promise<AuthResponse> {
    await delay(1000)

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(credentials.email)) {
      throw new Error('Invalid email format')
    }

    // Validate password (minimum 6 characters for demo)
    if (!credentials.password || credentials.password.length < 6) {
      throw new Error('Password must be at least 6 characters')
    }

    // For demo purposes, accept any valid email/password combination
    // In production, this would validate against a real database
    this.token = 'mock-jwt-token'

    // Create user object based on email
    const emailParts = credentials.email.split('@')[0].split('.')
    const firstName = emailParts[0] ? emailParts[0].charAt(0).toUpperCase() + emailParts[0].slice(1) : 'User'
    const lastName = emailParts[1] ? emailParts[1].charAt(0).toUpperCase() + emailParts[1].slice(1) : 'Demo'

    const user = {
      ...mockUser,
      email: credentials.email,
      firstName,
      lastName
    }

    return {
      user,
      token: this.token,
      refreshToken: 'mock-refresh-token'
    }
  }

  async register(userData: RegisterRequest): Promise<AuthResponse> {
    await delay(1000)

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(userData.email)) {
      throw new Error('Invalid email format')
    }

    // Validate password
    if (!userData.password || userData.password.length < 6) {
      throw new Error('Password must be at least 6 characters')
    }

    // Validate required fields
    if (!userData.firstName || !userData.lastName) {
      throw new Error('First name and last name are required')
    }

    const newUser: User = {
      ...mockUser,
      id: `user-${Date.now()}`,
      email: userData.email,
      firstName: userData.firstName,
      lastName: userData.lastName,
      createdAt: new Date(),
      updatedAt: new Date()
    }

    this.token = 'mock-jwt-token'
    return {
      user: newUser,
      token: this.token,
      refreshToken: 'mock-refresh-token'
    }
  }

  async logout(): Promise<void> {
    await delay(500)
    this.token = null
  }

  async getCurrentUser(): Promise<User> {
    await delay(500)
    if (!this.token) throw new Error('Not authenticated')
    return mockUser
  }

  async updateProfile(data: Partial<User>): Promise<User> {
    await delay(1000)
    return { ...mockUser, ...data, updatedAt: new Date() }
  }

  // Interview methods
  async createInterviewSession(config: InterviewConfig): Promise<InterviewSession> {
    await delay(1000)
    
    const newSession: InterviewSession = {
      id: `session-${Date.now()}`,
      userId: mockUser.id,
      jobTitle: config.jobTitle || 'Software Engineer',
      company: config.company || 'Tech Company',
      industry: config.industry || 'Technology',
      status: 'created',
      createdAt: new Date(),
      updatedAt: new Date(),
      config,
      questions: mockQuestions,
      answers: []
    }
    
    mockSessions.unshift(newSession)
    return newSession
  }

  async getInterviewSessions(): Promise<InterviewSession[]> {
    await delay(800)
    return mockSessions
  }

  async getInterviewSession(sessionId: string): Promise<InterviewSession> {
    await delay(500)
    const session = mockSessions.find(s => s.id === sessionId)
    if (!session) throw new Error('Session not found')
    return session
  }

  async startInterviewSession(sessionId: string): Promise<Question> {
    await delay(1000)
    const session = mockSessions.find(s => s.id === sessionId)
    if (!session) throw new Error('Session not found')
    
    session.status = 'in-progress'
    session.startedAt = new Date()
    
    return mockQuestions[0]
  }

  async submitAnswer(sessionId: string, data: {
    questionId: string
    textResponse?: string
    audioBlob?: Blob
    videoBlob?: Blob
    duration: number
  }): Promise<{ feedback: Feedback; nextQuestion?: Question }> {
    await delay(2000) // Simulate AI processing time
    
    const feedback: Feedback = {
      id: `feedback-${Date.now()}`,
      questionId: data.questionId,
      score: Math.floor(Math.random() * 30) + 70, // 70-100
      strengths: [
        'Clear communication',
        'Good structure',
        'Relevant examples'
      ],
      improvements: [
        'More specific details',
        'Better time management',
        'Stronger conclusion'
      ],
      suggestions: [
        'Use the STAR method',
        'Practice with a timer',
        'Prepare more examples'
      ],
      createdAt: new Date()
    }
    
    const nextQuestion = mockQuestions[1] // Return next question or undefined if last
    
    return { feedback, nextQuestion }
  }

  async completeInterviewSession(sessionId: string): Promise<PerformanceMetrics> {
    await delay(1500)
    
    const session = mockSessions.find(s => s.id === sessionId)
    if (!session) throw new Error('Session not found')
    
    session.status = 'completed'
    session.completedAt = new Date()
    
    const metrics: PerformanceMetrics = {
      overallScore: Math.floor(Math.random() * 25) + 75, // 75-100
      categoryScores: {
        technical: Math.floor(Math.random() * 30) + 70,
        behavioral: Math.floor(Math.random() * 30) + 70,
        communication: Math.floor(Math.random() * 30) + 70,
        problemSolving: Math.floor(Math.random() * 30) + 70
      },
      strengths: [
        'Strong technical knowledge',
        'Clear communication',
        'Good problem-solving approach'
      ],
      improvements: [
        'More detailed examples',
        'Better time management',
        'Stronger closing statements'
      ],
      recommendations: [
        'Practice more behavioral questions',
        'Work on system design skills',
        'Prepare industry-specific examples'
      ]
    }
    
    session.performanceMetrics = metrics
    return metrics
  }

  async getSessionResults(sessionId: string): Promise<{
    session: InterviewSession
    metrics: PerformanceMetrics
    feedback: Feedback[]
  }> {
    await delay(1000)
    
    const session = mockSessions.find(s => s.id === sessionId)
    if (!session) throw new Error('Session not found')
    
    const mockFeedback: Feedback[] = [
      {
        id: 'feedback-1',
        questionId: 'q1',
        score: 85,
        strengths: ['Clear structure', 'Good examples'],
        improvements: ['More specific metrics'],
        suggestions: ['Use STAR method'],
        createdAt: new Date()
      }
    ]
    
    return {
      session,
      metrics: session.performanceMetrics!,
      feedback: mockFeedback
    }
  }

  // Resume methods
  async uploadResume(file: File): Promise<Resume> {
    await delay(2000)
    
    return {
      id: `resume-${Date.now()}`,
      userId: mockUser.id,
      filename: file.name,
      originalName: file.name,
      size: file.size,
      mimeType: file.type,
      url: URL.createObjectURL(file),
      extractedText: 'Mock extracted text from resume...',
      analysis: {
        atsScore: 85,
        keywords: ['JavaScript', 'React', 'Node.js', 'Python'],
        suggestions: ['Add more quantifiable achievements', 'Include relevant certifications']
      },
      createdAt: new Date(),
      updatedAt: new Date()
    }
  }

  async getResumes(): Promise<Resume[]> {
    await delay(500)
    return []
  }

  async analyzeResume(resumeId: string): Promise<{
    atsScore: number
    keywords: string[]
    suggestions: string[]
  }> {
    await delay(3000)
    
    return {
      atsScore: Math.floor(Math.random() * 30) + 70,
      keywords: ['JavaScript', 'React', 'Node.js', 'Python', 'AWS'],
      suggestions: [
        'Add more quantifiable achievements',
        'Include relevant certifications',
        'Optimize for ATS keywords',
        'Improve formatting consistency'
      ]
    }
  }

  // Expert methods
  async getExperts(): Promise<ExpertProfile[]> {
    await delay(1000)
    return []
  }

  async bookExpertSession(expertId: string, data: any): Promise<any> {
    await delay(1000)
    return { success: true, bookingId: `booking-${Date.now()}` }
  }

  // Analytics methods
  async getAnalytics(): Promise<AnalyticsData> {
    await delay(1000)
    
    // Return mock analytics data
    return {
      totalSessions: mockSessions.length,
      averageScore: 82,
      improvementRate: 15,
      timeSpent: 750, // minutes
      categoryBreakdown: {
        technical: 80,
        behavioral: 85,
        communication: 83,
        problemSolving: 78
      },
      recentActivity: [
        { date: '2024-01-20', sessions: 2, score: 85 },
        { date: '2024-01-19', sessions: 1, score: 78 },
        { date: '2024-01-18', sessions: 3, score: 82 }
      ],
      trends: {
        scoreImprovement: 12,
        consistencyRating: 85,
        strongestArea: 'Communication',
        weakestArea: 'Technical'
      }
    }
  }

  // AI methods
  async generateQuestions(data: any): Promise<Question[]> {
    await delay(2000)
    return mockQuestions
  }

  async analyzeAnswer(data: any): Promise<Feedback> {
    await delay(1500)
    
    return {
      id: `feedback-${Date.now()}`,
      questionId: data.questionId,
      score: Math.floor(Math.random() * 30) + 70,
      strengths: ['Clear communication', 'Good structure'],
      improvements: ['More specific examples'],
      suggestions: ['Use STAR method', 'Practice timing'],
      createdAt: new Date()
    }
  }

  async analyzeEmotion(data: any): Promise<any> {
    await delay(1000)
    
    return {
      confidence: 0.85,
      emotions: {
        confident: 0.7,
        nervous: 0.2,
        excited: 0.1
      },
      recommendations: ['Maintain eye contact', 'Speak more slowly']
    }
  }
}

// Create and export singleton
export const mockApiClient = new MockApiClient()
export default mockApiClient
