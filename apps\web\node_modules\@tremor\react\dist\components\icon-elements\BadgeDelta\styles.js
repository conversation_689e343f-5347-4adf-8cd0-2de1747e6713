import{DeltaTypes as o,BaseColors as r}from"../../../lib/constants.js";import{colorPalette as e}from"../../../lib/theme.js";import"../../../lib/tremorTwMerge.js";import{getColorClassNames as t}from"../../../lib/utils.js";import"tslib";import"react";import i from"../../../assets/ArrowDownIcon.js";import n from"../../../assets/ArrowDownRightIcon.js";import d from"../../../assets/ArrowRightIcon.js";import g from"../../../assets/ArrowUpIcon.js";import a from"../../../assets/ArrowUpRightIcon.js";const p={xs:{paddingX:"px-2",paddingY:"py-0.5",fontSize:"text-xs"},sm:{paddingX:"px-2.5",paddingY:"py-1",fontSize:"text-sm"},md:{paddingX:"px-3",paddingY:"py-1.5",fontSize:"text-md"},lg:{paddingX:"px-3.5",paddingY:"py-1.5",fontSize:"text-lg"},xl:{paddingX:"px-3.5",paddingY:"py-1.5",fontSize:"text-xl"}},l={xs:{paddingX:"px-2",paddingY:"py-0.5",fontSize:"text-xs"},sm:{paddingX:"px-2.5",paddingY:"py-0.5",fontSize:"text-sm"},md:{paddingX:"px-3",paddingY:"py-0.5",fontSize:"text-md"},lg:{paddingX:"px-3.5",paddingY:"py-0.5",fontSize:"text-lg"},xl:{paddingX:"px-4",paddingY:"py-1",fontSize:"text-xl"}},s={xs:{height:"h-4",width:"w-4"},sm:{height:"h-4",width:"w-4"},md:{height:"h-4",width:"w-4"},lg:{height:"h-5",width:"w-5"},xl:{height:"h-6",width:"w-6"}},x={[o.Increase]:{bgColor:t(r.Emerald,e.background).bgColor,textColor:t(r.Emerald,e.iconText).textColor,ringColor:t(r.Emerald,e.iconRing).ringColor},[o.ModerateIncrease]:{bgColor:t(r.Emerald,e.background).bgColor,textColor:t(r.Emerald,e.iconText).textColor,ringColor:t(r.Emerald,e.iconRing).ringColor},[o.Decrease]:{bgColor:t(r.Red,e.background).bgColor,textColor:t(r.Red,e.iconText).textColor,ringColor:t(r.Red,e.iconRing).ringColor},[o.ModerateDecrease]:{bgColor:t(r.Red,e.background).bgColor,textColor:t(r.Red,e.iconText).textColor,ringColor:t(r.Red,e.iconRing).ringColor},[o.Unchanged]:{bgColor:t(r.Orange,e.background).bgColor,textColor:t(r.Orange,e.iconText).textColor,ringColor:t(r.Orange,e.iconRing).ringColor}},m={[o.Increase]:g,[o.ModerateIncrease]:a,[o.Decrease]:i,[o.ModerateDecrease]:n,[o.Unchanged]:d};export{p as badgeProportionsIconOnly,l as badgeProportionsWithText,x as colors,m as deltaIcons,s as iconSizes};
