import{__rest as o}from"tslib";import{tremorTwMerge as n}from"../../../lib/tremorTwMerge.js";import{makeClassName as r}from"../../../lib/utils.js";import m from"react";import{colSpan as e,colSpanSm as l,colSpanMd as t,colSpanLg as a}from"./styles.js";const s=r("Col"),i=m.forwardRef(((r,i)=>{const{numColSpan:p=1,numColSpanSm:c,numColSpanMd:u,numColSpanLg:S,children:C,className:d}=r,f=o(r,["numColSpan","numColSpanSm","numColSpanMd","numColSpanLg","children","className"]),b=(o,n)=>o&&Object.keys(n).includes(String(o))?n[o]:"";return m.createElement("div",Object.assign({ref:i,className:n(s("root"),(()=>{const o=b(p,e),r=b(c,l),m=b(u,t),s=b(S,a);return n(o,r,m,s)})(),d)},f),C)}));i.displayName="Col";export{i as default};
