import{__rest as r}from"tslib";import{tremorTwMerge as e}from"../../../lib/tremorTwMerge.js";import{makeClassName as t}from"../../../lib/utils.js";import o from"react";const i=t("List"),a=o.forwardRef(((t,a)=>{const{children:m,className:s}=t,d=r(t,["children","className"]);return o.createElement("ul",Object.assign({ref:a,className:e(i("root"),"w-full divide-y","divide-tremor-border text-tremor-content","dark:divide-dark-tremor-border dark:text-dark-tremor-content",s)},d),m)}));a.displayName="List";export{a as default};
