'use client';
"use strict";var e=require("tslib"),t=require("react"),r=require("../../../lib/tremorTwMerge.cjs"),a=require("../../../lib/utils.cjs"),o=require("@headlessui/react");function c(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var s=c(t);const n=a.makeClassName("SearchSelectItem"),d=s.default.forwardRef(((t,a)=>{const{value:c,icon:d,className:l,children:u}=t,m=e.__rest(t,["value","icon","className","children"]),i=d;return s.default.createElement(o.ComboboxOption,Object.assign({className:r.tremorTwMerge(n("root"),"flex justify-start items-center cursor-default text-tremor-default p-2.5","data-[focus]:bg-tremor-background-muted  data-[focus]:text-tremor-content-strong data-[selected]:text-tremor-content-strong data-[selected]:bg-tremor-background-muted text-tremor-content-emphasis","dark:data-[focus]:bg-dark-tremor-background-muted  dark:data-[focus]:text-dark-tremor-content-strong dark:data-[selected]:text-dark-tremor-content-strong dark:data-[selected]:bg-dark-tremor-background-muted dark:text-dark-tremor-content-emphasis",l),ref:a,key:c,value:c},m),i&&s.default.createElement(i,{className:r.tremorTwMerge(n("icon"),"flex-none h-5 w-5 mr-3","text-tremor-content-subtle","dark:text-dark-tremor-content-subtle")}),s.default.createElement("span",{className:"whitespace-nowrap truncate"},null!=u?u:c))}));d.displayName="SearchSelectItem",module.exports=d;
