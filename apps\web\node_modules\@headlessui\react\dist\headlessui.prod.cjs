"use strict";var ja=Object.create;var Dn=Object.defineProperty;var Ka=Object.getOwnPropertyDescriptor;var za=Object.getOwnPropertyNames;var Xa=Object.getPrototypeOf,Ya=Object.prototype.hasOwnProperty;var Ja=(e,n,t)=>n in e?Dn(e,n,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[n]=t;var qa=(e,n)=>{for(var t in n)Dn(e,t,{get:n[t],enumerable:!0})},wi=(e,n,t,o)=>{if(n&&typeof n=="object"||typeof n=="function")for(let r of za(n))!Ya.call(e,r)&&r!==t&&Dn(e,r,{get:()=>n[r],enumerable:!(o=Ka(n,r))||o.enumerable});return e};var oe=(e,n,t)=>(t=e!=null?ja(Xa(e)):{},wi(n||!e||!e.__esModule?Dn(t,"default",{value:e,enumerable:!0}):t,e)),Qa=e=>wi(Dn({},"__esModule",{value:!0}),e);var so=(e,n,t)=>(Ja(e,typeof n!="symbol"?n+"":n,t),t);var Vp={};qa(Vp,{Button:()=>Ir,Checkbox:()=>Au,CloseButton:()=>Ou,Combobox:()=>uf,ComboboxButton:()=>Dl,ComboboxInput:()=>Il,ComboboxLabel:()=>Ml,ComboboxOption:()=>wl,ComboboxOptions:()=>Fl,DataInteractive:()=>df,Description:()=>xt,Dialog:()=>Bf,DialogBackdrop:()=>$f,DialogDescription:()=>Nf,DialogPanel:()=>Xl,DialogTitle:()=>Yl,Disclosure:()=>Qf,DisclosureButton:()=>ea,DisclosurePanel:()=>ta,Field:()=>td,Fieldset:()=>od,FocusTrap:()=>ai,FocusTrapFeatures:()=>Qo,Input:()=>sd,Label:()=>Xe,Legend:()=>ad,Listbox:()=>Pd,ListboxButton:()=>fa,ListboxLabel:()=>da,ListboxOption:()=>ma,ListboxOptions:()=>pa,ListboxSelectedOption:()=>Ta,Menu:()=>Gd,MenuButton:()=>ba,MenuHeading:()=>Ea,MenuItem:()=>ya,MenuItems:()=>ga,MenuSection:()=>va,MenuSeparator:()=>ha,Popover:()=>rp,PopoverBackdrop:()=>Ca,PopoverButton:()=>Ra,PopoverGroup:()=>La,PopoverOverlay:()=>Sa,PopoverPanel:()=>Aa,Portal:()=>qe,Radio:()=>Da,RadioGroup:()=>mp,RadioGroupDescription:()=>Ma,RadioGroupLabel:()=>Ia,RadioGroupOption:()=>Oa,Select:()=>gp,Switch:()=>Pp,SwitchDescription:()=>Ha,SwitchGroup:()=>wa,SwitchLabel:()=>_a,Tab:()=>Np,TabGroup:()=>Ba,TabList:()=>Ua,TabPanel:()=>Va,TabPanels:()=>Ga,Textarea:()=>Gp,Transition:()=>ci,TransitionChild:()=>to,useClose:()=>Ro});module.exports=Qa(Vp);var _i=oe(require("react"),1),In=typeof document!="undefined"?_i.default.useLayoutEffect:()=>{};var lo=require("react");function pr(e){let n=(0,lo.useRef)(null);return In(()=>{n.current=e},[e]),(0,lo.useCallback)((...t)=>{let o=n.current;return o==null?void 0:o(...t)},[])}var Mt=e=>{var n;return(n=e==null?void 0:e.ownerDocument)!==null&&n!==void 0?n:document},vt=e=>e&&"window"in e&&e.window===e?e:Mt(e).defaultView||window;function Za(e){var n;return typeof window=="undefined"||window.navigator==null?!1:((n=window.navigator.userAgentData)===null||n===void 0?void 0:n.brands.some(t=>e.test(t.brand)))||e.test(window.navigator.userAgent)}function eu(e){var n;return typeof window!="undefined"&&window.navigator!=null?e.test(((n=window.navigator.userAgentData)===null||n===void 0?void 0:n.platform)||window.navigator.platform):!1}function mr(){return eu(/^Mac/i)}function Tr(){return Za(/Android/i)}function br(e){return e.mozInputSource===0&&e.isTrusted?!0:Tr()&&e.pointerType?e.type==="click"&&e.buttons===1:e.detail===0&&!e.pointerType}var ao=require("react");var gr=class{isDefaultPrevented(){return this.nativeEvent.defaultPrevented}preventDefault(){this.defaultPrevented=!0,this.nativeEvent.preventDefault()}stopPropagation(){this.nativeEvent.stopPropagation(),this.isPropagationStopped=()=>!0}isPropagationStopped(){return!1}persist(){}constructor(n,t){this.nativeEvent=t,this.target=t.target,this.currentTarget=t.currentTarget,this.relatedTarget=t.relatedTarget,this.bubbles=t.bubbles,this.cancelable=t.cancelable,this.defaultPrevented=t.defaultPrevented,this.eventPhase=t.eventPhase,this.isTrusted=t.isTrusted,this.timeStamp=t.timeStamp,this.type=n}};function uo(e){let n=(0,ao.useRef)({isFocused:!1,observer:null});In(()=>{let o=n.current;return()=>{o.observer&&(o.observer.disconnect(),o.observer=null)}},[]);let t=pr(o=>{e==null||e(o)});return(0,ao.useCallback)(o=>{if(o.target instanceof HTMLButtonElement||o.target instanceof HTMLInputElement||o.target instanceof HTMLTextAreaElement||o.target instanceof HTMLSelectElement){n.current.isFocused=!0;let r=o.target,i=s=>{n.current.isFocused=!1,r.disabled&&t(new gr("blur",s)),n.current.observer&&(n.current.observer.disconnect(),n.current.observer=null)};r.addEventListener("focusout",i,{once:!0}),n.current.observer=new MutationObserver(()=>{if(n.current.isFocused&&r.disabled){var s;(s=n.current.observer)===null||s===void 0||s.disconnect();let a=r===document.activeElement?null:document.activeElement;r.dispatchEvent(new FocusEvent("blur",{relatedTarget:a})),r.dispatchEvent(new FocusEvent("focusout",{bubbles:!0,relatedTarget:a}))}}),n.current.observer.observe(r,{attributes:!0,attributeFilter:["disabled"]})}},[t])}var yr=require("react");function vr(e){let{isDisabled:n,onFocus:t,onBlur:o,onFocusChange:r}=e,i=(0,yr.useCallback)(l=>{if(l.target===l.currentTarget)return o&&o(l),r&&r(!1),!0},[o,r]),s=uo(i),a=(0,yr.useCallback)(l=>{let c=Mt(l.target);l.target===l.currentTarget&&c.activeElement===l.target&&(t&&t(l),r&&r(!0),s(l))},[r,t,s]);return{focusProps:{onFocus:!n&&(t||r||o)?a:void 0,onBlur:!n&&(o||r)?i:void 0}}}var Pr=require("react");var Fn=null,Er=new Set,Mn=new Map,zt=!1,hr=!1,tu={Tab:!0,Escape:!0};function Rr(e,n){for(let t of Er)t(e,n)}function nu(e){return!(e.metaKey||!mr()&&e.altKey||e.ctrlKey||e.key==="Control"||e.key==="Shift"||e.key==="Meta")}function co(e){zt=!0,nu(e)&&(Fn="keyboard",Rr("keyboard",e))}function ze(e){Fn="pointer",(e.type==="mousedown"||e.type==="pointerdown")&&(zt=!0,Rr("pointer",e))}function Hi(e){br(e)&&(zt=!0,Fn="virtual")}function ki(e){e.target===window||e.target===document||(!zt&&!hr&&(Fn="virtual",Rr("virtual",e)),zt=!1,hr=!1)}function $i(){zt=!1,hr=!0}function xr(e){if(typeof window=="undefined"||Mn.get(vt(e)))return;let n=vt(e),t=Mt(e),o=n.HTMLElement.prototype.focus;n.HTMLElement.prototype.focus=function(){zt=!0,o.apply(this,arguments)},t.addEventListener("keydown",co,!0),t.addEventListener("keyup",co,!0),t.addEventListener("click",Hi,!0),n.addEventListener("focus",ki,!0),n.addEventListener("blur",$i,!1),typeof PointerEvent!="undefined"?(t.addEventListener("pointerdown",ze,!0),t.addEventListener("pointermove",ze,!0),t.addEventListener("pointerup",ze,!0)):(t.addEventListener("mousedown",ze,!0),t.addEventListener("mousemove",ze,!0),t.addEventListener("mouseup",ze,!0)),n.addEventListener("beforeunload",()=>{Ni(e)},{once:!0}),Mn.set(n,{focus:o})}var Ni=(e,n)=>{let t=vt(e),o=Mt(e);n&&o.removeEventListener("DOMContentLoaded",n),Mn.has(t)&&(t.HTMLElement.prototype.focus=Mn.get(t).focus,o.removeEventListener("keydown",co,!0),o.removeEventListener("keyup",co,!0),o.removeEventListener("click",Hi,!0),t.removeEventListener("focus",ki,!0),t.removeEventListener("blur",$i,!1),typeof PointerEvent!="undefined"?(o.removeEventListener("pointerdown",ze,!0),o.removeEventListener("pointermove",ze,!0),o.removeEventListener("pointerup",ze,!0)):(o.removeEventListener("mousedown",ze,!0),o.removeEventListener("mousemove",ze,!0),o.removeEventListener("mouseup",ze,!0)),Mn.delete(t))};function Bi(e){let n=Mt(e),t;return n.readyState!=="loading"?xr(e):(t=()=>{xr(e)},n.addEventListener("DOMContentLoaded",t)),()=>Ni(e,t)}typeof document!="undefined"&&Bi();function fo(){return Fn!=="pointer"}var ou=new Set(["checkbox","radio","range","color","file","image","button","submit","reset"]);function ru(e,n,t){var o;let r=typeof window!="undefined"?vt(t==null?void 0:t.target).HTMLInputElement:HTMLInputElement,i=typeof window!="undefined"?vt(t==null?void 0:t.target).HTMLTextAreaElement:HTMLTextAreaElement,s=typeof window!="undefined"?vt(t==null?void 0:t.target).HTMLElement:HTMLElement,a=typeof window!="undefined"?vt(t==null?void 0:t.target).KeyboardEvent:KeyboardEvent;return e=e||(t==null?void 0:t.target)instanceof r&&!ou.has(t==null||(o=t.target)===null||o===void 0?void 0:o.type)||(t==null?void 0:t.target)instanceof i||(t==null?void 0:t.target)instanceof s&&(t==null?void 0:t.target.isContentEditable),!(e&&n==="keyboard"&&t instanceof a&&!tu[t.key])}function Sr(e,n,t){xr(),(0,Pr.useEffect)(()=>{let o=(r,i)=>{ru(!!(t!=null&&t.isTextInput),r,i)&&e(fo())};return Er.add(o),()=>{Er.delete(o)}},n)}var wn=require("react");function Cr(e){let{isDisabled:n,onBlurWithin:t,onFocusWithin:o,onFocusWithinChange:r}=e,i=(0,wn.useRef)({isFocusWithin:!1}),s=(0,wn.useCallback)(c=>{i.current.isFocusWithin&&!c.currentTarget.contains(c.relatedTarget)&&(i.current.isFocusWithin=!1,t&&t(c),r&&r(!1))},[t,r,i]),a=uo(s),l=(0,wn.useCallback)(c=>{!i.current.isFocusWithin&&document.activeElement===c.target&&(o&&o(c),r&&r(!0),i.current.isFocusWithin=!0,a(c))},[o,r,a]);return n?{focusWithinProps:{onFocus:void 0,onBlur:void 0}}:{focusWithinProps:{onFocus:l,onBlur:s}}}var Et=require("react"),po=!1,Ar=0;function Lr(){po=!0,setTimeout(()=>{po=!1},50)}function Ui(e){e.pointerType==="touch"&&Lr()}function iu(){if(typeof document!="undefined")return typeof PointerEvent!="undefined"?document.addEventListener("pointerup",Ui):document.addEventListener("touchend",Lr),Ar++,()=>{Ar--,!(Ar>0)&&(typeof PointerEvent!="undefined"?document.removeEventListener("pointerup",Ui):document.removeEventListener("touchend",Lr))}}function ae(e){let{onHoverStart:n,onHoverChange:t,onHoverEnd:o,isDisabled:r}=e,[i,s]=(0,Et.useState)(!1),a=(0,Et.useRef)({isHovered:!1,ignoreEmulatedMouseEvents:!1,pointerType:"",target:null}).current;(0,Et.useEffect)(iu,[]);let{hoverProps:l,triggerHoverEnd:c}=(0,Et.useMemo)(()=>{let u=(f,m)=>{if(a.pointerType=m,r||m==="touch"||a.isHovered||!f.currentTarget.contains(f.target))return;a.isHovered=!0;let T=f.currentTarget;a.target=T,n&&n({type:"hoverstart",target:T,pointerType:m}),t&&t(!0),s(!0)},p=(f,m)=>{if(a.pointerType="",a.target=null,m==="touch"||!a.isHovered)return;a.isHovered=!1;let T=f.currentTarget;o&&o({type:"hoverend",target:T,pointerType:m}),t&&t(!1),s(!1)},d={};return typeof PointerEvent!="undefined"?(d.onPointerEnter=f=>{po&&f.pointerType==="mouse"||u(f,f.pointerType)},d.onPointerLeave=f=>{!r&&f.currentTarget.contains(f.target)&&p(f,f.pointerType)}):(d.onTouchStart=()=>{a.ignoreEmulatedMouseEvents=!0},d.onMouseEnter=f=>{!a.ignoreEmulatedMouseEvents&&!po&&u(f,"mouse"),a.ignoreEmulatedMouseEvents=!1},d.onMouseLeave=f=>{!r&&f.currentTarget.contains(f.target)&&p(f,"mouse")}),{hoverProps:d,triggerHoverEnd:p}},[n,t,o,r,a]);return(0,Et.useEffect)(()=>{r&&c({currentTarget:a.target},a.pointerType)},[r]),{hoverProps:l,isHovered:i}}var Ft=require("react");function le(e={}){let{autoFocus:n=!1,isTextInput:t,within:o}=e,r=(0,Ft.useRef)({isFocused:!1,isFocusVisible:n||fo()}),[i,s]=(0,Ft.useState)(!1),[a,l]=(0,Ft.useState)(()=>r.current.isFocused&&r.current.isFocusVisible),c=(0,Ft.useCallback)(()=>l(r.current.isFocused&&r.current.isFocusVisible),[]),u=(0,Ft.useCallback)(f=>{r.current.isFocused=f,s(f),c()},[c]);Sr(f=>{r.current.isFocusVisible=f,c()},[],{isTextInput:t});let{focusProps:p}=vr({isDisabled:o,onFocusChange:u}),{focusWithinProps:d}=Cr({isDisabled:!o,onFocusWithinChange:u});return{isFocused:i,isFocusVisible:a,focusProps:o?d:p}}var Ki=require("react");var bo=require("react");var Or=class{constructor(){so(this,"current",this.detect());so(this,"handoffState","pending");so(this,"currentId",0)}set(n){this.current!==n&&(this.handoffState="pending",this.currentId=0,this.current=n)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return this.current==="server"}get isClient(){return this.current==="client"}detect(){return typeof window=="undefined"||typeof document=="undefined"?"server":"client"}handoff(){this.handoffState==="pending"&&(this.handoffState="complete")}get isHandoffComplete(){return this.handoffState==="complete"}},Ze=new Or;function he(e){return Ze.isServer?null:e instanceof Node?e.ownerDocument:e!=null&&e.hasOwnProperty("current")&&e.current instanceof Node?e.current.ownerDocument:document}var mo=require("react");function ht(e){typeof queueMicrotask=="function"?queueMicrotask(e):Promise.resolve().then(e).catch(n=>setTimeout(()=>{throw n}))}function xe(){let e=[],n={addEventListener(t,o,r,i){return t.addEventListener(o,r,i),n.add(()=>t.removeEventListener(o,r,i))},requestAnimationFrame(...t){let o=requestAnimationFrame(...t);return n.add(()=>cancelAnimationFrame(o))},nextFrame(...t){return n.requestAnimationFrame(()=>n.requestAnimationFrame(...t))},setTimeout(...t){let o=setTimeout(...t);return n.add(()=>clearTimeout(o))},microTask(...t){let o={current:!0};return ht(()=>{o.current&&t[0]()}),n.add(()=>{o.current=!1})},style(t,o,r){let i=t.style.getPropertyValue(o);return Object.assign(t.style,{[o]:r}),this.add(()=>{Object.assign(t.style,{[o]:i})})},group(t){let o=xe();return t(o),this.add(()=>o.dispose())},add(t){return e.includes(t)||e.push(t),()=>{let o=e.indexOf(t);if(o>=0)for(let r of e.splice(o,1))r()}},dispose(){for(let t of e.splice(0))t()}};return n}function Pe(){let[e]=(0,mo.useState)(xe);return(0,mo.useEffect)(()=>()=>e.dispose(),[e]),e}var Vi=oe(require("react"),1);var Gi=require("react");var To=require("react");var N=(e,n)=>{Ze.isServer?(0,To.useEffect)(e,n):(0,To.useLayoutEffect)(e,n)};function de(e){let n=(0,Gi.useRef)(e);return N(()=>{n.current=e},[e]),n}var E=function(n){let t=de(n);return Vi.default.useCallback((...o)=>t.current(...o),[t])};function su(e){let n=e.width/2,t=e.height/2;return{top:e.clientY-t,right:e.clientX+n,bottom:e.clientY+t,left:e.clientX-n}}function lu(e,n){return!(!e||!n||e.right<n.left||e.left>n.right||e.bottom<n.top||e.top>n.bottom)}function Re({disabled:e=!1}={}){let n=(0,bo.useRef)(null),[t,o]=(0,bo.useState)(!1),r=Pe(),i=E(()=>{n.current=null,o(!1),r.dispose()}),s=E(a=>{if(r.dispose(),n.current===null){n.current=a.currentTarget,o(!0);{let l=he(a.currentTarget);r.addEventListener(l,"pointerup",i,!1),r.addEventListener(l,"pointermove",c=>{if(n.current){let u=su(c);o(lu(u,n.current.getBoundingClientRect()))}},!1),r.addEventListener(l,"pointercancel",i,!1)}}});return{pressed:t,pressProps:e?{}:{onPointerDown:s,onPointerUp:i,onClick:i}}}var on=oe(require("react"),1),Wi=(0,on.createContext)(void 0);function Te(){return(0,on.useContext)(Wi)}function go({value:e,children:n}){return on.default.createElement(Wi.Provider,{value:e},n)}var De=oe(require("react"),1);function _n(...e){return Array.from(new Set(e.flatMap(n=>typeof n=="string"?n.split(" "):[]))).filter(Boolean).join(" ")}function J(e,n,...t){if(e in n){let r=n[e];return typeof r=="function"?r(...t):r}let o=new Error(`Tried to handle "${e}" but there is no handler defined. Only defined handlers are: ${Object.keys(n).map(r=>`"${r}"`).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(o,J),o}function H(){let e=uu();return(0,De.useCallback)(n=>au({mergeRefs:e,...n}),[e])}function au({ourProps:e,theirProps:n,slot:t,defaultTag:o,features:r,visible:i=!0,name:s,mergeRefs:a}){a=a!=null?a:cu;let l=ji(n,e);if(i)return yo(l,t,o,s,a);let c=r!=null?r:0;if(c&2){let{static:u=!1,...p}=l;if(u)return yo(p,t,o,s,a)}if(c&1){let{unmount:u=!0,...p}=l;return J(u?0:1,{[0](){return null},[1](){return yo({...p,hidden:!0,style:{display:"none"}},t,o,s,a)}})}return yo(l,t,o,s,a)}function yo(e,n={},t,o,r){let{as:i=t,children:s,refName:a="ref",...l}=Dr(e,["unmount","static"]),c=e.ref!==void 0?{[a]:e.ref}:{},u=typeof s=="function"?s(n):s;"className"in l&&l.className&&typeof l.className=="function"&&(l.className=l.className(n)),l["aria-labelledby"]&&l["aria-labelledby"]===l.id&&(l["aria-labelledby"]=void 0);let p={};if(n){let d=!1,f=[];for(let[m,T]of Object.entries(n))typeof T=="boolean"&&(d=!0),T===!0&&f.push(m.replace(/([A-Z])/g,b=>`-${b.toLowerCase()}`));if(d){p["data-headlessui-state"]=f.join(" ");for(let m of f)p[`data-${m}`]=""}}if(i===De.Fragment&&(Object.keys(lt(l)).length>0||Object.keys(lt(p)).length>0))if(!(0,De.isValidElement)(u)||Array.isArray(u)&&u.length>1){if(Object.keys(lt(l)).length>0)throw new Error(['Passing props on "Fragment"!',"",`The current component <${o} /> is rendering a "Fragment".`,"However we need to passthrough the following props:",Object.keys(lt(l)).concat(Object.keys(lt(p))).map(d=>`  - ${d}`).join(`
`),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',"Render a single element as the child so that we can forward the props onto that element."].map(d=>`  - ${d}`).join(`
`)].join(`
`))}else{let d=u.props,f=d==null?void 0:d.className,m=typeof f=="function"?(...g)=>_n(f(...g),l.className):_n(f,l.className),T=m?{className:m}:{},b=ji(u.props,lt(Dr(l,["ref"])));for(let g in p)g in b&&delete p[g];return(0,De.cloneElement)(u,Object.assign({},b,p,c,{ref:r(fu(u),c.ref)},T))}return(0,De.createElement)(i,Object.assign({},Dr(l,["ref"]),i!==De.Fragment&&c,i!==De.Fragment&&p),u)}function uu(){let e=(0,De.useRef)([]),n=(0,De.useCallback)(t=>{for(let o of e.current)o!=null&&(typeof o=="function"?o(t):o.current=t)},[]);return(...t)=>{if(!t.every(o=>o==null))return e.current=t,n}}function cu(...e){return e.every(n=>n==null)?void 0:n=>{for(let t of e)t!=null&&(typeof t=="function"?t(n):t.current=n)}}function ji(...e){var o;if(e.length===0)return{};if(e.length===1)return e[0];let n={},t={};for(let r of e)for(let i in r)i.startsWith("on")&&typeof r[i]=="function"?((o=t[i])!=null||(t[i]=[]),t[i].push(r[i])):n[i]=r[i];if(n.disabled||n["aria-disabled"])for(let r in t)/^(on(?:Click|Pointer|Mouse|Key)(?:Down|Up|Press)?)$/.test(r)&&(t[r]=[i=>{var s;return(s=i==null?void 0:i.preventDefault)==null?void 0:s.call(i)}]);for(let r in t)Object.assign(n,{[r](i,...s){let a=t[r];for(let l of a){if((i instanceof Event||(i==null?void 0:i.nativeEvent)instanceof Event)&&i.defaultPrevented)return;l(i,...s)}}});return n}function re(...e){var o;if(e.length===0)return{};if(e.length===1)return e[0];let n={},t={};for(let r of e)for(let i in r)i.startsWith("on")&&typeof r[i]=="function"?((o=t[i])!=null||(t[i]=[]),t[i].push(r[i])):n[i]=r[i];for(let r in t)Object.assign(n,{[r](...i){let s=t[r];for(let a of s)a==null||a(...i)}});return n}function M(e){var n;return Object.assign((0,De.forwardRef)(e),{displayName:(n=e.displayName)!=null?n:e.name})}function lt(e){let n=Object.assign({},e);for(let t in n)n[t]===void 0&&delete n[t];return n}function Dr(e,n=[]){let t=Object.assign({},e);for(let o of n)o in t&&delete t[o];return t}function fu(e){return De.default.version.split(".")[0]>="19"?e.props.ref:e.ref}var du="button";function pu(e,n){var T;let t=Te(),{disabled:o=t||!1,autoFocus:r=!1,...i}=e,{isFocusVisible:s,focusProps:a}=le({autoFocus:r}),{isHovered:l,hoverProps:c}=ae({isDisabled:o}),{pressed:u,pressProps:p}=Re({disabled:o}),d=re({ref:n,type:(T=i.type)!=null?T:"button",disabled:o||void 0,autoFocus:r},a,c,p),f=(0,Ki.useMemo)(()=>({disabled:o,hover:l,focus:s,active:u,autofocus:r}),[o,l,s,u,r]);return H()({ourProps:d,theirProps:i,slot:f,defaultTag:du,name:"Button"})}var Ir=M(pu);var ft=oe(require("react"),1);var rn=require("react");function at(e,n,t){let[o,r]=(0,rn.useState)(t),i=e!==void 0,s=(0,rn.useRef)(i),a=(0,rn.useRef)(!1),l=(0,rn.useRef)(!1);return i&&!s.current&&!a.current?(a.current=!0,s.current=i,console.error("A component is changing from uncontrolled to controlled. This may be caused by the value changing from undefined to a defined value, which should not happen.")):!i&&s.current&&!l.current&&(l.current=!0,s.current=i,console.error("A component is changing from controlled to uncontrolled. This may be caused by the value changing from a defined value to undefined, which should not happen.")),[i?e:o,E(c=>(i||r(c),n==null?void 0:n(c)))]}var zi=require("react");function ut(e){let[n]=(0,zi.useState)(e);return n}var q=require("react");var Ce=oe(require("react"),1),Ji=require("react-dom");function Mr(e={},n=null,t=[]){for(let[o,r]of Object.entries(e))Yi(t,Xi(n,o),r);return t}function Xi(e,n){return e?e+"["+n+"]":n}function Yi(e,n,t){if(Array.isArray(t))for(let[o,r]of t.entries())Yi(e,Xi(n,o.toString()),r);else t instanceof Date?e.push([n,t.toISOString()]):typeof t=="boolean"?e.push([n,t?"1":"0"]):typeof t=="string"?e.push([n,t]):typeof t=="number"?e.push([n,`${t}`]):t==null?e.push([n,""]):Mr(t,n,e)}function wt(e){var t,o;let n=(t=e==null?void 0:e.form)!=null?t:e.closest("form");if(n){for(let r of n.elements)if(r!==e&&(r.tagName==="INPUT"&&r.type==="submit"||r.tagName==="BUTTON"&&r.type==="submit"||r.nodeName==="INPUT"&&r.type==="image")){r.click();return}(o=n.requestSubmit)==null||o.call(n)}}var mu="span";function Tu(e,n){var s;let{features:t=1,...o}=e,r={ref:n,"aria-hidden":(t&2)===2?!0:(s=o["aria-hidden"])!=null?s:void 0,hidden:(t&4)===4?!0:void 0,style:{position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",...(t&4)===4&&(t&2)!==2&&{display:"none"}}};return H()({ourProps:r,theirProps:o,slot:{},defaultTag:mu,name:"Hidden"})}var we=M(Tu);var qi=(0,Ce.createContext)(null);function Qi(e){let[n,t]=(0,Ce.useState)(null);return Ce.default.createElement(qi.Provider,{value:{target:n}},e.children,Ce.default.createElement(we,{features:4,ref:t}))}function bu({children:e}){let n=(0,Ce.useContext)(qi);if(!n)return Ce.default.createElement(Ce.default.Fragment,null,e);let{target:t}=n;return t?(0,Ji.createPortal)(Ce.default.createElement(Ce.default.Fragment,null,e),t):null}function ct({data:e,form:n,disabled:t,onReset:o,overrides:r}){let[i,s]=(0,Ce.useState)(null),a=Pe();return(0,Ce.useEffect)(()=>{if(o&&i)return a.addEventListener(i,"reset",o)},[i,n,o]),Ce.default.createElement(bu,null,Ce.default.createElement(gu,{setForm:s,formId:n}),Mr(e).map(([l,c])=>Ce.default.createElement(we,{features:4,...lt({key:l,as:"input",type:"hidden",hidden:!0,readOnly:!0,form:n,disabled:t,name:l,value:c,...r})})))}function gu({setForm:e,formId:n}){return(0,Ce.useEffect)(()=>{if(n){let t=document.getElementById(n);t&&e(t)}},[e,n]),n?null:Ce.default.createElement(we,{features:4,as:"input",type:"hidden",hidden:!0,readOnly:!0,ref:t=>{if(!t)return;let o=t.closest("form");o&&e(o)}})}var sn=oe(require("react"),1),Zi=(0,sn.createContext)(void 0);function Me(){return(0,sn.useContext)(Zi)}function es({id:e,children:n}){return sn.default.createElement(Zi.Provider,{value:e},n)}function Fe(e){let n=e.parentElement,t=null;for(;n&&!(n instanceof HTMLFieldSetElement);)n instanceof HTMLLegendElement&&(t=n),n=n.parentElement;let o=(n==null?void 0:n.getAttribute("disabled"))==="";return o&&yu(t)?!1:o}function yu(e){if(!e)return!1;let n=e.previousElementSibling;for(;n!==null;){if(n instanceof HTMLLegendElement)return!1;n=n.previousElementSibling}return!0}var Ge=oe(require("react"),1);var Eo=require("react");var ts=Symbol();function ln(e,n=!0){return Object.assign(e,{[ts]:n})}function K(...e){let n=(0,Eo.useRef)(e);(0,Eo.useEffect)(()=>{n.current=e},[e]);let t=E(o=>{for(let r of n.current)r!=null&&(typeof r=="function"?r(o):r.current=o)});return e.every(o=>o==null||(o==null?void 0:o[ts]))?void 0:t}var ho=(0,Ge.createContext)(null);ho.displayName="DescriptionContext";function ns(){let e=(0,Ge.useContext)(ho);if(e===null){let n=new Error("You used a <Description /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(n,ns),n}return e}function He(){var e,n;return(n=(e=(0,Ge.useContext)(ho))==null?void 0:e.value)!=null?n:void 0}function et(){let[e,n]=(0,Ge.useState)([]);return[e.length>0?e.join(" "):void 0,(0,Ge.useMemo)(()=>function(o){let r=E(s=>(n(a=>[...a,s]),()=>n(a=>{let l=a.slice(),c=l.indexOf(s);return c!==-1&&l.splice(c,1),l}))),i=(0,Ge.useMemo)(()=>({register:r,slot:o.slot,name:o.name,props:o.props,value:o.value}),[r,o.slot,o.name,o.props,o.value]);return Ge.default.createElement(ho.Provider,{value:i},o.children)},[n])]}var vu="p";function Eu(e,n){let t=(0,q.useId)(),o=Te(),{id:r=`headlessui-description-${t}`,...i}=e,s=ns(),a=K(n);N(()=>s.register(r),[r,s.register]);let l=o||!1,c=(0,Ge.useMemo)(()=>({...s.slot,disabled:l}),[s.slot,l]),u={ref:a,...s.props,id:r};return H()({ourProps:u,theirProps:i,slot:c,defaultTag:vu,name:s.name||"Description"})}var hu=M(Eu),xt=Object.assign(hu,{});var Ve=oe(require("react"),1);var xo=(0,Ve.createContext)(null);xo.displayName="LabelContext";function Po(){let e=(0,Ve.useContext)(xo);if(e===null){let n=new Error("You used a <Label /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(n,Po),n}return e}function Ie(e){var t,o,r;let n=(o=(t=(0,Ve.useContext)(xo))==null?void 0:t.value)!=null?o:void 0;return((r=e==null?void 0:e.length)!=null?r:0)>0?[n,...e].filter(Boolean).join(" "):n}function ke({inherit:e=!1}={}){let n=Ie(),[t,o]=(0,Ve.useState)([]),r=e?[n,...t].filter(Boolean):t;return[r.length>0?r.join(" "):void 0,(0,Ve.useMemo)(()=>function(s){let a=E(c=>(o(u=>[...u,c]),()=>o(u=>{let p=u.slice(),d=p.indexOf(c);return d!==-1&&p.splice(d,1),p}))),l=(0,Ve.useMemo)(()=>({register:a,slot:s.slot,name:s.name,props:s.props,value:s.value}),[a,s.slot,s.name,s.props,s.value]);return Ve.default.createElement(xo.Provider,{value:l},s.children)},[o])]}var xu="label";function Pu(e,n){var b;let t=(0,q.useId)(),o=Po(),r=Me(),i=Te(),{id:s=`headlessui-label-${t}`,htmlFor:a=r!=null?r:(b=o.props)==null?void 0:b.htmlFor,passive:l=!1,...c}=e,u=K(n);N(()=>o.register(s),[s,o.register]);let p=E(g=>{let y=g.currentTarget;if(y instanceof HTMLLabelElement&&g.preventDefault(),o.props&&"onClick"in o.props&&typeof o.props.onClick=="function"&&o.props.onClick(g),y instanceof HTMLLabelElement){let v=document.getElementById(y.htmlFor);if(v){let x=v.getAttribute("disabled");if(x==="true"||x==="")return;let h=v.getAttribute("aria-disabled");if(h==="true"||h==="")return;(v instanceof HTMLInputElement&&(v.type==="radio"||v.type==="checkbox")||v.role==="radio"||v.role==="checkbox"||v.role==="switch")&&v.click(),v.focus({preventScroll:!0})}}}),d=i||!1,f=(0,Ve.useMemo)(()=>({...o.slot,disabled:d}),[o.slot,d]),m={ref:u,...o.props,id:s,htmlFor:a,onClick:p};return l&&("onClick"in m&&(delete m.htmlFor,delete m.onClick),"onClick"in c&&delete c.onClick),H()({ourProps:m,theirProps:c,slot:f,defaultTag:a?xu:"div",name:o.name||"Label"})}var Ru=M(Pu),Xe=Object.assign(Ru,{});var Su="span";function Cu(e,n){let t=(0,q.useId)(),o=Me(),r=Te(),{id:i=o||`headlessui-checkbox-${t}`,disabled:s=r||!1,autoFocus:a=!1,checked:l,defaultChecked:c,onChange:u,name:p,value:d,form:f,indeterminate:m=!1,...T}=e,b=ut(c),[g,y]=at(l,u,b!=null?b:!1),v=Ie(),x=He(),h=Pe(),[L,D]=(0,ft.useState)(!1),S=E(()=>{D(!0),y==null||y(!g),h.nextFrame(()=>{D(!1)})}),_=E(O=>{if(Fe(O.currentTarget))return O.preventDefault();O.preventDefault(),S()}),C=E(O=>{O.key===" "?(O.preventDefault(),S()):O.key==="Enter"&&wt(O.currentTarget)}),F=E(O=>O.preventDefault()),{isFocusVisible:R,focusProps:W}=le({autoFocus:a}),{isHovered:j,hoverProps:X}=ae({isDisabled:s}),{pressed:B,pressProps:U}=Re({disabled:s}),te=re({ref:n,id:i,role:"checkbox","aria-checked":m?"mixed":g?"true":"false","aria-labelledby":v,"aria-describedby":x,"aria-disabled":s?!0:void 0,indeterminate:m?"true":void 0,tabIndex:s?void 0:0,onKeyUp:s?void 0:C,onKeyPress:s?void 0:F,onClick:s?void 0:_},W,X,U),w=(0,ft.useMemo)(()=>({checked:g,disabled:s,hover:j,focus:R,active:B,indeterminate:m,changing:L,autofocus:a}),[g,m,s,j,R,B,L,a]),I=(0,ft.useCallback)(()=>{if(b!==void 0)return y==null?void 0:y(b)},[y,b]),P=H();return ft.default.createElement(ft.default.Fragment,null,p!=null&&ft.default.createElement(ct,{disabled:s,data:{[p]:d||"on"},overrides:{type:"checkbox",checked:g},form:f,onReset:I}),P({ourProps:te,theirProps:T,slot:w,defaultTag:Su,name:"Checkbox"}))}var Au=M(Cu);var rs=oe(require("react"),1);var an=oe(require("react"),1),os=(0,an.createContext)(()=>{});function Ro(){return(0,an.useContext)(os)}function un({value:e,children:n}){return an.default.createElement(os.Provider,{value:e},n)}function Lu(e,n){let t=Ro();return rs.default.createElement(Ir,{ref:n,...re({onClick:t},e)})}var Ou=M(Lu);var Pt=oe(require("react"),1),ds=require("react-dom");function cn(e,n,t){var i;let o=(i=t.initialDeps)!=null?i:[],r;return()=>{var s,a,l,c;let u;t.key&&((s=t.debug)!=null&&s.call(t))&&(u=Date.now());let p=e();if(!(p.length!==o.length||p.some((m,T)=>o[T]!==m)))return r;o=p;let f;if(t.key&&((a=t.debug)!=null&&a.call(t))&&(f=Date.now()),r=n(...p),t.key&&((l=t.debug)!=null&&l.call(t))){let m=Math.round((Date.now()-u)*100)/100,T=Math.round((Date.now()-f)*100)/100,b=T/16,g=(y,v)=>{for(y=String(y);y.length<v;)y=" "+y;return y};console.info(`%c\u23F1 ${g(T,5)} /${g(m,5)} ms`,`
            font-size: .6rem;
            font-weight: bold;
            color: hsl(${Math.max(0,Math.min(120-120*b,120))}deg 100% 31%);`,t==null?void 0:t.key)}return(c=t==null?void 0:t.onChange)==null||c.call(t,r),r}}function So(e,n){if(e===void 0)throw new Error(`Unexpected undefined${n?`: ${n}`:""}`);return e}var is=(e,n)=>Math.abs(e-n)<1,ss=(e,n,t)=>{let o;return function(...r){e.clearTimeout(o),o=e.setTimeout(()=>n.apply(this,r),t)}};var Du=e=>e,Iu=e=>{let n=Math.max(e.startIndex-e.overscan,0),t=Math.min(e.endIndex+e.overscan,e.count-1),o=[];for(let r=n;r<=t;r++)o.push(r);return o},as=(e,n)=>{let t=e.scrollElement;if(!t)return;let o=e.targetWindow;if(!o)return;let r=s=>{let{width:a,height:l}=s;n({width:Math.round(a),height:Math.round(l)})};if(r(t.getBoundingClientRect()),!o.ResizeObserver)return()=>{};let i=new o.ResizeObserver(s=>{let a=s[0];if(a!=null&&a.borderBoxSize){let l=a.borderBoxSize[0];if(l){r({width:l.inlineSize,height:l.blockSize});return}}r(t.getBoundingClientRect())});return i.observe(t,{box:"border-box"}),()=>{i.unobserve(t)}},ls={passive:!0};var Mu=typeof window=="undefined"?!0:"onscrollend"in window,us=(e,n)=>{let t=e.scrollElement;if(!t)return;let o=e.targetWindow;if(!o)return;let r=0,i=Mu?()=>{}:ss(o,()=>{n(r,!1)},e.options.isScrollingResetDelay),s=c=>()=>{r=t[e.options.horizontal?"scrollLeft":"scrollTop"],i(),n(r,c)},a=s(!0),l=s(!1);return l(),t.addEventListener("scroll",a,ls),t.addEventListener("scrollend",l,ls),()=>{t.removeEventListener("scroll",a),t.removeEventListener("scrollend",l)}};var Fu=(e,n,t)=>{if(n!=null&&n.borderBoxSize){let o=n.borderBoxSize[0];if(o)return Math.round(o[t.options.horizontal?"inlineSize":"blockSize"])}return Math.round(e.getBoundingClientRect()[t.options.horizontal?"width":"height"])};var cs=(e,{adjustments:n=0,behavior:t},o)=>{var r,i;let s=e+n;(i=(r=o.scrollElement)==null?void 0:r.scrollTo)==null||i.call(r,{[o.options.horizontal?"left":"top"]:s,behavior:t})},Co=class{constructor(n){this.unsubs=[],this.scrollElement=null,this.targetWindow=null,this.isScrolling=!1,this.scrollToIndexTimeoutId=null,this.measurementsCache=[],this.itemSizeCache=new Map,this.pendingMeasuredCacheIndexes=[],this.scrollRect=null,this.scrollOffset=null,this.scrollDirection=null,this.scrollAdjustments=0,this.elementsCache=new Map,this.observer=(()=>{let t=null,o=()=>t||(!this.targetWindow||!this.targetWindow.ResizeObserver?null:t=new this.targetWindow.ResizeObserver(r=>{r.forEach(i=>{this._measureElement(i.target,i)})}));return{disconnect:()=>{var r;return(r=o())==null?void 0:r.disconnect()},observe:r=>{var i;return(i=o())==null?void 0:i.observe(r,{box:"border-box"})},unobserve:r=>{var i;return(i=o())==null?void 0:i.unobserve(r)}}})(),this.range=null,this.setOptions=t=>{Object.entries(t).forEach(([o,r])=>{typeof r=="undefined"&&delete t[o]}),this.options={debug:!1,initialOffset:0,overscan:1,paddingStart:0,paddingEnd:0,scrollPaddingStart:0,scrollPaddingEnd:0,horizontal:!1,getItemKey:Du,rangeExtractor:Iu,onChange:()=>{},measureElement:Fu,initialRect:{width:0,height:0},scrollMargin:0,gap:0,indexAttribute:"data-index",initialMeasurementsCache:[],lanes:1,isScrollingResetDelay:150,enabled:!0,...t}},this.notify=(t,o)=>{var c;var r,i;let{startIndex:s,endIndex:a}=(c=this.range)!=null?c:{startIndex:void 0,endIndex:void 0},l=this.calculateRange();(t||s!==(l==null?void 0:l.startIndex)||a!==(l==null?void 0:l.endIndex))&&((i=(r=this.options).onChange)==null||i.call(r,this,o))},this.cleanup=()=>{this.unsubs.filter(Boolean).forEach(t=>t()),this.unsubs=[],this.scrollElement=null,this.targetWindow=null,this.observer.disconnect(),this.elementsCache.clear()},this._didMount=()=>()=>{this.cleanup()},this._willUpdate=()=>{var r;var t;let o=this.options.enabled?this.options.getScrollElement():null;if(this.scrollElement!==o){if(this.cleanup(),!o){this.notify(!1,!1);return}this.scrollElement=o,this.scrollElement&&"ownerDocument"in this.scrollElement?this.targetWindow=this.scrollElement.ownerDocument.defaultView:this.targetWindow=(r=(t=this.scrollElement)==null?void 0:t.window)!=null?r:null,this._scrollToOffset(this.getScrollOffset(),{adjustments:void 0,behavior:void 0}),this.unsubs.push(this.options.observeElementRect(this,i=>{this.scrollRect=i,this.notify(!1,!1)})),this.unsubs.push(this.options.observeElementOffset(this,(i,s)=>{this.scrollAdjustments=0,this.scrollDirection=s?this.getScrollOffset()<i?"forward":"backward":null,this.scrollOffset=i;let a=this.isScrolling;this.isScrolling=s,this.notify(a!==s,s)}))}},this.getSize=()=>{var t;return this.options.enabled?(this.scrollRect=(t=this.scrollRect)!=null?t:this.options.initialRect,this.scrollRect[this.options.horizontal?"width":"height"]):(this.scrollRect=null,0)},this.getScrollOffset=()=>{var t;return this.options.enabled?(this.scrollOffset=(t=this.scrollOffset)!=null?t:typeof this.options.initialOffset=="function"?this.options.initialOffset():this.options.initialOffset,this.scrollOffset):(this.scrollOffset=null,0)},this.getFurthestMeasurement=(t,o)=>{let r=new Map,i=new Map;for(let s=o-1;s>=0;s--){let a=t[s];if(r.has(a.lane))continue;let l=i.get(a.lane);if(l==null||a.end>l.end?i.set(a.lane,a):a.end<l.end&&r.set(a.lane,!0),r.size===this.options.lanes)break}return i.size===this.options.lanes?Array.from(i.values()).sort((s,a)=>s.end===a.end?s.index-a.index:s.end-a.end)[0]:void 0},this.getMeasurementOptions=cn(()=>[this.options.count,this.options.paddingStart,this.options.scrollMargin,this.options.getItemKey,this.options.enabled],(t,o,r,i,s)=>(this.pendingMeasuredCacheIndexes=[],{count:t,paddingStart:o,scrollMargin:r,getItemKey:i,enabled:s}),{key:!1}),this.getMeasurements=cn(()=>[this.getMeasurementOptions(),this.itemSizeCache],({count:t,paddingStart:o,scrollMargin:r,getItemKey:i,enabled:s},a)=>{var l;if(!s)return this.measurementsCache=[],this.itemSizeCache.clear(),[];this.measurementsCache.length===0&&(this.measurementsCache=this.options.initialMeasurementsCache,this.measurementsCache.forEach(p=>{this.itemSizeCache.set(p.key,p.size)}));let c=this.pendingMeasuredCacheIndexes.length>0?Math.min(...this.pendingMeasuredCacheIndexes):0;this.pendingMeasuredCacheIndexes=[];let u=this.measurementsCache.slice(0,c);for(let p=c;p<t;p++){let d=(l=this.measurementsCache[p])==null?void 0:l.measureElement;d||(d=x=>{let h=i(p),L=this.elementsCache.get(h);if(!x){L&&(this.observer.unobserve(L),this.elementsCache.delete(h));return}L!==x&&(L&&this.observer.unobserve(L),this.observer.observe(x),this.elementsCache.set(h,x)),x.isConnected&&this.resizeItem(p,this.options.measureElement(x,void 0,this))});let f=i(p),m=this.options.lanes===1?u[p-1]:this.getFurthestMeasurement(u,p),T=m?m.end+this.options.gap:o+r,b=a.get(f),g=typeof b=="number"?b:this.options.estimateSize(p),y=T+g,v=m?m.lane:p%this.options.lanes;u[p]={index:p,start:T,size:g,end:y,key:f,lane:v,measureElement:d}}return this.measurementsCache=u,u},{key:!1,debug:()=>this.options.debug}),this.calculateRange=cn(()=>[this.getMeasurements(),this.getSize(),this.getScrollOffset()],(t,o,r)=>this.range=t.length>0&&o>0?wu({measurements:t,outerSize:o,scrollOffset:r}):null,{key:!1,debug:()=>this.options.debug}),this.getIndexes=cn(()=>[this.options.rangeExtractor,this.calculateRange(),this.options.overscan,this.options.count],(t,o,r,i)=>o===null?[]:t({startIndex:o.startIndex,endIndex:o.endIndex,overscan:r,count:i}),{key:!1,debug:()=>this.options.debug}),this.indexFromElement=t=>{let o=this.options.indexAttribute,r=t.getAttribute(o);return r?parseInt(r,10):(console.warn(`Missing attribute name '${o}={index}' on measured element.`),-1)},this._measureElement=(t,o)=>{let r=this.indexFromElement(t),i=this.getMeasurements()[r];if(!i||!t.isConnected){this.elementsCache.forEach((a,l)=>{a===t&&(this.observer.unobserve(t),this.elementsCache.delete(l))});return}let s=this.elementsCache.get(i.key);s!==t&&(s&&this.observer.unobserve(s),this.observer.observe(t),this.elementsCache.set(i.key,t)),this.resizeItem(r,this.options.measureElement(t,o,this))},this.resizeItem=(t,o)=>{var a;let r=this.getMeasurements()[t];if(!r)return;let i=(a=this.itemSizeCache.get(r.key))!=null?a:r.size,s=o-i;s!==0&&((this.shouldAdjustScrollPositionOnItemSizeChange!==void 0?this.shouldAdjustScrollPositionOnItemSizeChange(r,s,this):r.start<this.getScrollOffset()+this.scrollAdjustments)&&this._scrollToOffset(this.getScrollOffset(),{adjustments:this.scrollAdjustments+=s,behavior:void 0}),this.pendingMeasuredCacheIndexes.push(r.index),this.itemSizeCache=new Map(this.itemSizeCache.set(r.key,o)),this.notify(!0,!1))},this.measureElement=t=>{t&&this._measureElement(t,void 0)},this.getVirtualItems=cn(()=>[this.getIndexes(),this.getMeasurements()],(t,o)=>{let r=[];for(let i=0,s=t.length;i<s;i++){let a=t[i],l=o[a];r.push(l)}return r},{key:!1,debug:()=>this.options.debug}),this.getVirtualItemForOffset=t=>{let o=this.getMeasurements();if(o.length!==0)return So(o[fs(0,o.length-1,r=>So(o[r]).start,t)])},this.getOffsetForAlignment=(t,o)=>{let r=this.getSize(),i=this.getScrollOffset();o==="auto"&&(t<=i?o="start":t>=i+r?o="end":o="start"),o==="start"?t=t:o==="end"?t=t-r:o==="center"&&(t=t-r/2);let s=this.options.horizontal?"scrollWidth":"scrollHeight",l=(this.scrollElement?"document"in this.scrollElement?this.scrollElement.document.documentElement[s]:this.scrollElement[s]:0)-r;return Math.max(Math.min(l,t),0)},this.getOffsetForIndex=(t,o="auto")=>{t=Math.max(0,Math.min(t,this.options.count-1));let r=this.getMeasurements()[t];if(!r)return;let i=this.getSize(),s=this.getScrollOffset();if(o==="auto")if(r.end>=s+i-this.options.scrollPaddingEnd)o="end";else if(r.start<=s+this.options.scrollPaddingStart)o="start";else return[s,o];let a=o==="end"?r.end+this.options.scrollPaddingEnd:r.start-this.options.scrollPaddingStart;return[this.getOffsetForAlignment(a,o),o]},this.isDynamicMode=()=>this.elementsCache.size>0,this.cancelScrollToIndex=()=>{this.scrollToIndexTimeoutId!==null&&this.targetWindow&&(this.targetWindow.clearTimeout(this.scrollToIndexTimeoutId),this.scrollToIndexTimeoutId=null)},this.scrollToOffset=(t,{align:o="start",behavior:r}={})=>{this.cancelScrollToIndex(),r==="smooth"&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size."),this._scrollToOffset(this.getOffsetForAlignment(t,o),{adjustments:void 0,behavior:r})},this.scrollToIndex=(t,{align:o="auto",behavior:r}={})=>{t=Math.max(0,Math.min(t,this.options.count-1)),this.cancelScrollToIndex(),r==="smooth"&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size.");let i=this.getOffsetForIndex(t,o);if(!i)return;let[s,a]=i;this._scrollToOffset(s,{adjustments:void 0,behavior:r}),r!=="smooth"&&this.isDynamicMode()&&this.targetWindow&&(this.scrollToIndexTimeoutId=this.targetWindow.setTimeout(()=>{if(this.scrollToIndexTimeoutId=null,this.elementsCache.has(this.options.getItemKey(t))){let[c]=So(this.getOffsetForIndex(t,a));is(c,this.getScrollOffset())||this.scrollToIndex(t,{align:a,behavior:r})}else this.scrollToIndex(t,{align:a,behavior:r})}))},this.scrollBy=(t,{behavior:o}={})=>{this.cancelScrollToIndex(),o==="smooth"&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size."),this._scrollToOffset(this.getScrollOffset()+t,{adjustments:void 0,behavior:o})},this.getTotalSize=()=>{var i;var t;let o=this.getMeasurements(),r;return o.length===0?r=this.options.paddingStart:r=this.options.lanes===1?(i=(t=o[o.length-1])==null?void 0:t.end)!=null?i:0:Math.max(...o.slice(-this.options.lanes).map(s=>s.end)),r-this.options.scrollMargin+this.options.paddingEnd},this._scrollToOffset=(t,{adjustments:o,behavior:r})=>{this.options.scrollToFn(t,{behavior:r,adjustments:o},this)},this.measure=()=>{var t,o;this.itemSizeCache=new Map,(o=(t=this.options).onChange)==null||o.call(t,this,!1)},this.setOptions(n)}},fs=(e,n,t,o)=>{for(;e<=n;){let r=(e+n)/2|0,i=t(r);if(i<o)e=r+1;else if(i>o)n=r-1;else return r}return e>0?e-1:0};function wu({measurements:e,outerSize:n,scrollOffset:t}){let o=e.length-1,i=fs(0,o,a=>e[a].start,t),s=i;for(;s<o&&e[s].end<t+n;)s++;return{startIndex:i,endIndex:s}}var _u=typeof document!="undefined"?Pt.useLayoutEffect:Pt.useEffect;function Hu(e){let n=Pt.useReducer(()=>({}),{})[1],t={...e,onChange:(r,i)=>{var s;i?(0,ds.flushSync)(n):n(),(s=e.onChange)==null||s.call(e,r,i)}},[o]=Pt.useState(()=>new Co(t));return o.setOptions(t),Pt.useEffect(()=>o._didMount(),[]),_u(()=>o._willUpdate()),o}function ps(e){return Hu({observeElementRect:as,observeElementOffset:us,scrollToFn:cs,...e})}var z=oe(require("react"),1),en=require("react-dom");var ms=require("react");function ku(e,n){return e!==null&&n!==null&&typeof e=="object"&&typeof n=="object"&&"id"in e&&"id"in n?e.id===n.id:e===n}function fn(e=ku){return(0,ms.useCallback)((n,t)=>{if(typeof e=="string"){let o=e;return(n==null?void 0:n[o])===(t==null?void 0:t[o])}return e(n,t)},[e])}var Ao=require("react");function $u(e){if(e===null)return{width:0,height:0};let{width:n,height:t}=e.getBoundingClientRect();return{width:n,height:t}}function Rt(e,n=!1){let[t,o]=(0,Ao.useReducer)(()=>({}),{}),r=(0,Ao.useMemo)(()=>$u(e),[e,t]);return N(()=>{if(!e)return;let i=new ResizeObserver(o);return i.observe(e),()=>{i.disconnect()}},[e]),n?{width:`${r.width}px`,height:`${r.height}px`}:r}var bs=require("react");var Lo=class extends Map{constructor(t){super();this.factory=t}get(t){let o=super.get(t);return o===void 0&&(o=this.factory(t),this.set(t,o)),o}};function Oo(e,n){let t=e(),o=new Set;return{getSnapshot(){return t},subscribe(r){return o.add(r),()=>o.delete(r)},dispatch(r,...i){let s=n[r].call(t,...i);s&&(t=s,o.forEach(a=>a()))}}}var Ts=require("react");function Do(e){return(0,Ts.useSyncExternalStore)(e.subscribe,e.getSnapshot,e.getSnapshot)}var Nu=new Lo(()=>Oo(()=>[],{ADD(e){return this.includes(e)?this:[...this,e]},REMOVE(e){let n=this.indexOf(e);if(n===-1)return this;let t=this.slice();return t.splice(n,1),t}}));function tt(e,n){let t=Nu.get(n),o=(0,bs.useId)(),r=Do(t);if(N(()=>{if(e)return t.dispatch("ADD",o),()=>t.dispatch("REMOVE",o)},[t,e]),!e)return!1;let i=r.indexOf(o),s=r.length;return i===-1&&(i=s,s+=1),i===s-1}var Fr=new Map,Hn=new Map;function gs(e){var t;let n=(t=Hn.get(e))!=null?t:0;return Hn.set(e,n+1),n!==0?()=>ys(e):(Fr.set(e,{"aria-hidden":e.getAttribute("aria-hidden"),inert:e.inert}),e.setAttribute("aria-hidden","true"),e.inert=!0,()=>ys(e))}function ys(e){var o;let n=(o=Hn.get(e))!=null?o:1;if(n===1?Hn.delete(e):Hn.set(e,n-1),n!==1)return;let t=Fr.get(e);t&&(t["aria-hidden"]===null?e.removeAttribute("aria-hidden"):e.setAttribute("aria-hidden",t["aria-hidden"]),e.inert=t.inert,Fr.delete(e))}function _t(e,{allowed:n,disallowed:t}={}){let o=tt(e,"inert-others");N(()=>{var s,a;if(!o)return;let r=xe();for(let l of(s=t==null?void 0:t())!=null?s:[])l&&r.add(gs(l));let i=(a=n==null?void 0:n())!=null?a:[];for(let l of i){if(!l)continue;let c=he(l);if(!c)continue;let u=l.parentElement;for(;u&&u!==c.body;){for(let p of u.children)i.some(d=>p.contains(d))||r.add(gs(p));u=u.parentElement}}return r.dispose},[o,n,t])}var vs=require("react");function dt(e,n,t){let o=de(r=>{let i=r.getBoundingClientRect();i.x===0&&i.y===0&&i.width===0&&i.height===0&&t()});(0,vs.useEffect)(()=>{if(!e)return;let r=n===null?null:n instanceof HTMLElement?n:n.current;if(!r)return;let i=xe();if(typeof ResizeObserver!="undefined"){let s=new ResizeObserver(()=>o.current(r));s.observe(r),i.add(()=>s.disconnect())}if(typeof IntersectionObserver!="undefined"){let s=new IntersectionObserver(()=>o.current(r));s.observe(r),i.add(()=>s.disconnect())}return()=>i.dispose()},[n,o,e])}var $n=require("react");var kn=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map(e=>`${e}:not([tabindex='-1'])`).join(","),Bu=["[data-autofocus]"].map(e=>`${e}:not([tabindex='-1'])`).join(",");function dn(e=document.body){return e==null?[]:Array.from(e.querySelectorAll(kn)).sort((n,t)=>Math.sign((n.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}function Uu(e=document.body){return e==null?[]:Array.from(e.querySelectorAll(Bu)).sort((n,t)=>Math.sign((n.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}function St(e,n=0){var t;return e===((t=he(e))==null?void 0:t.body)?!1:J(n,{[0](){return e.matches(kn)},[1](){let o=e;for(;o!==null;){if(o.matches(kn))return!0;o=o.parentElement}return!1}})}function wr(e){let n=he(e);xe().nextFrame(()=>{n&&!St(n.activeElement,0)&&nt(e)})}typeof window!="undefined"&&typeof document!="undefined"&&(document.addEventListener("keydown",e=>{e.metaKey||e.altKey||e.ctrlKey||(document.documentElement.dataset.headlessuiFocusVisible="")},!0),document.addEventListener("click",e=>{e.detail===1?delete document.documentElement.dataset.headlessuiFocusVisible:e.detail===0&&(document.documentElement.dataset.headlessuiFocusVisible="")},!0));function nt(e){e==null||e.focus({preventScroll:!0})}var Gu=["textarea","input"].join(",");function Vu(e){var n,t;return(t=(n=e==null?void 0:e.matches)==null?void 0:n.call(e,Gu))!=null?t:!1}function $e(e,n=t=>t){return e.slice().sort((t,o)=>{let r=n(t),i=n(o);if(r===null||i===null)return 0;let s=r.compareDocumentPosition(i);return s&Node.DOCUMENT_POSITION_FOLLOWING?-1:s&Node.DOCUMENT_POSITION_PRECEDING?1:0})}function Io(e,n){return ye(dn(),n,{relativeTo:e})}function ye(e,n,{sorted:t=!0,relativeTo:o=null,skipElements:r=[]}={}){let i=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e.ownerDocument,s=Array.isArray(e)?t?$e(e):e:n&64?Uu(e):dn(e);r.length>0&&s.length>1&&(s=s.filter(f=>!r.some(m=>m!=null&&"current"in m?(m==null?void 0:m.current)===f:m===f))),o=o!=null?o:i.activeElement;let a=(()=>{if(n&5)return 1;if(n&10)return-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),l=(()=>{if(n&1)return 0;if(n&2)return Math.max(0,s.indexOf(o))-1;if(n&4)return Math.max(0,s.indexOf(o))+1;if(n&8)return s.length-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),c=n&32?{preventScroll:!0}:{},u=0,p=s.length,d;do{if(u>=p||u+p<=0)return 0;let f=l+u;if(n&16)f=(f+p)%p;else{if(f<0)return 3;if(f>=p)return 1}d=s[f],d==null||d.focus(c),u+=a}while(d!==i.activeElement);return n&6&&Vu(d)&&d.select(),2}function _r(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function Wu(){return/Android/gi.test(window.navigator.userAgent)}function Mo(){return _r()||Wu()}var Es=require("react");function pn(e,n,t,o){let r=de(t);(0,Es.useEffect)(()=>{if(!e)return;function i(s){r.current(s)}return document.addEventListener(n,i,o),()=>document.removeEventListener(n,i,o)},[e,n,o])}var hs=require("react");function Fo(e,n,t,o){let r=de(t);(0,hs.useEffect)(()=>{if(!e)return;function i(s){r.current(s)}return window.addEventListener(n,i,o),()=>window.removeEventListener(n,i,o)},[e,n,o])}var xs=30;function pt(e,n,t){let o=tt(e,"outside-click"),r=de(t),i=(0,$n.useCallback)(function(c,u){if(c.defaultPrevented)return;let p=u(c);if(p===null||!p.getRootNode().contains(p)||!p.isConnected)return;let d=function f(m){return typeof m=="function"?f(m()):Array.isArray(m)||m instanceof Set?m:[m]}(n);for(let f of d)if(f!==null&&(f.contains(p)||c.composed&&c.composedPath().includes(f)))return;return!St(p,1)&&p.tabIndex!==-1&&c.preventDefault(),r.current(c,p)},[r,n]),s=(0,$n.useRef)(null);pn(o,"pointerdown",l=>{var c,u;s.current=((u=(c=l.composedPath)==null?void 0:c.call(l))==null?void 0:u[0])||l.target},!0),pn(o,"mousedown",l=>{var c,u;s.current=((u=(c=l.composedPath)==null?void 0:c.call(l))==null?void 0:u[0])||l.target},!0),pn(o,"click",l=>{Mo()||s.current&&(i(l,()=>s.current),s.current=null)},!0);let a=(0,$n.useRef)({x:0,y:0});pn(o,"touchstart",l=>{a.current.x=l.touches[0].clientX,a.current.y=l.touches[0].clientY},!0),pn(o,"touchend",l=>{let c={x:l.changedTouches[0].clientX,y:l.changedTouches[0].clientY};if(!(Math.abs(c.x-a.current.x)>=xs||Math.abs(c.y-a.current.y)>=xs))return i(l,()=>l.target instanceof HTMLElement?l.target:null)},!0),Fo(o,"blur",l=>i(l,()=>window.document.activeElement instanceof HTMLIFrameElement?window.document.activeElement:null),!0)}var Ps=require("react");function Le(...e){return(0,Ps.useMemo)(()=>he(...e),[...e])}var Ss=require("react");var Rs=require("react");function Ht(e,n,t,o){let r=de(t);(0,Rs.useEffect)(()=>{e=e!=null?e:window;function i(s){r.current(s)}return e.addEventListener(n,i,o),()=>e.removeEventListener(n,i,o)},[e,n,o])}function kr(e){let n=(0,Ss.useRef)({value:"",selectionStart:null,selectionEnd:null});return Ht(e,"blur",t=>{let o=t.target;o instanceof HTMLInputElement&&(n.current={value:o.value,selectionStart:o.selectionStart,selectionEnd:o.selectionEnd})}),E(()=>{if(document.activeElement!==e&&e instanceof HTMLInputElement&&e.isConnected){if(e.focus({preventScroll:!0}),e.value!==n.current.value)e.setSelectionRange(e.value.length,e.value.length);else{let{selectionStart:t,selectionEnd:o}=n.current;t!==null&&o!==null&&e.setSelectionRange(t,o)}n.current={value:"",selectionStart:null,selectionEnd:null}}})}var Cs=require("react");function Be(e,n){return(0,Cs.useMemo)(()=>{var o;if(e.type)return e.type;let t=(o=e.as)!=null?o:"button";if(typeof t=="string"&&t.toLowerCase()==="button"||(n==null?void 0:n.tagName)==="BUTTON"&&!n.hasAttribute("type"))return"button"},[e.type,e.as,n])}function As(){let e;return{before({doc:n}){var r;let t=n.documentElement,o=(r=n.defaultView)!=null?r:window;e=Math.max(0,o.innerWidth-t.clientWidth)},after({doc:n,d:t}){let o=n.documentElement,r=Math.max(0,o.clientWidth-o.offsetWidth),i=Math.max(0,e-r);t.style(o,"paddingRight",`${i}px`)}}}function Ls(){return _r()?{before({doc:e,d:n,meta:t}){function o(r){return t.containers.flatMap(i=>i()).some(i=>i.contains(r))}n.microTask(()=>{var s;if(window.getComputedStyle(e.documentElement).scrollBehavior!=="auto"){let a=xe();a.style(e.documentElement,"scrollBehavior","auto"),n.add(()=>n.microTask(()=>a.dispose()))}let r=(s=window.scrollY)!=null?s:window.pageYOffset,i=null;n.addEventListener(e,"click",a=>{if(a.target instanceof HTMLElement)try{let l=a.target.closest("a");if(!l)return;let{hash:c}=new URL(l.href),u=e.querySelector(c);u&&!o(u)&&(i=u)}catch{}},!0),n.addEventListener(e,"touchstart",a=>{if(a.target instanceof HTMLElement)if(o(a.target)){let l=a.target;for(;l.parentElement&&o(l.parentElement);)l=l.parentElement;n.style(l,"overscrollBehavior","contain")}else n.style(a.target,"touchAction","none")}),n.addEventListener(e,"touchmove",a=>{if(a.target instanceof HTMLElement){if(a.target.tagName==="INPUT")return;if(o(a.target)){let l=a.target;for(;l.parentElement&&l.dataset.headlessuiPortal!==""&&!(l.scrollHeight>l.clientHeight||l.scrollWidth>l.clientWidth);)l=l.parentElement;l.dataset.headlessuiPortal===""&&a.preventDefault()}else a.preventDefault()}},{passive:!1}),n.add(()=>{var l;let a=(l=window.scrollY)!=null?l:window.pageYOffset;r!==a&&window.scrollTo(0,r),i&&i.isConnected&&(i.scrollIntoView({block:"nearest"}),i=null)})})}}:{}}function Os(){return{before({doc:e,d:n}){n.style(e.documentElement,"overflow","hidden")}}}function ju(e){let n={};for(let t of e)Object.assign(n,t(n));return n}var kt=Oo(()=>new Map,{PUSH(e,n){var o;let t=(o=this.get(e))!=null?o:{doc:e,count:0,d:xe(),meta:new Set};return t.count++,t.meta.add(n),this.set(e,t),this},POP(e,n){let t=this.get(e);return t&&(t.count--,t.meta.delete(n)),this},SCROLL_PREVENT({doc:e,d:n,meta:t}){let o={doc:e,d:n,meta:ju(t)},r=[Ls(),As(),Os()];r.forEach(({before:i})=>i==null?void 0:i(o)),r.forEach(({after:i})=>i==null?void 0:i(o))},SCROLL_ALLOW({d:e}){e.dispose()},TEARDOWN({doc:e}){this.delete(e)}});kt.subscribe(()=>{let e=kt.getSnapshot(),n=new Map;for(let[t]of e)n.set(t,t.documentElement.style.overflow);for(let t of e.values()){let o=n.get(t.doc)==="hidden",r=t.count!==0;(r&&!o||!r&&o)&&kt.dispatch(t.count>0?"SCROLL_PREVENT":"SCROLL_ALLOW",t),t.count===0&&kt.dispatch("TEARDOWN",t)}});function Ds(e,n,t=()=>({containers:[]})){let o=Do(kt),r=n?o.get(n):void 0,i=r?r.count>0:!1;return N(()=>{if(!(!n||!e))return kt.dispatch("PUSH",n,t),()=>kt.dispatch("POP",n,t)},[e,n]),i}function mt(e,n,t=()=>[document.body]){let o=tt(e,"scroll-lock");Ds(o,n,r=>{var i;return{containers:[...(i=r.containers)!=null?i:[],t]}})}var Ms=require("react");function Is(e){return[e.screenX,e.screenY]}function mn(){let e=(0,Ms.useRef)([-1,-1]);return{wasMoved(n){let t=Is(n);return e.current[0]===t[0]&&e.current[1]===t[1]?!1:(e.current=t,!0)},update(n){e.current=Is(n)}}}var Nn=require("react");var $t=require("react");function Fs(e=0){let[n,t]=(0,$t.useState)(e),o=(0,$t.useCallback)(l=>t(l),[n]),r=(0,$t.useCallback)(l=>t(c=>c|l),[n]),i=(0,$t.useCallback)(l=>(n&l)===l,[n]),s=(0,$t.useCallback)(l=>t(c=>c&~l),[t]),a=(0,$t.useCallback)(l=>t(c=>c^l),[t]);return{flags:n,setFlag:o,addFlag:r,hasFlag:i,removeFlag:s,toggleFlag:a}}var ws,_s;typeof process!="undefined"&&typeof globalThis!="undefined"&&typeof Element!="undefined"&&((ws=process==null?void 0:process.env)==null?void 0:ws["NODE_ENV"])==="test"&&typeof((_s=Element==null?void 0:Element.prototype)==null?void 0:_s.getAnimations)=="undefined"&&(Element.prototype.getAnimations=function(){return console.warn(["Headless UI has polyfilled `Element.prototype.getAnimations` for your tests.","Please install a proper polyfill e.g. `jsdom-testing-mocks`, to silence these warnings.","","Example usage:","```js","import { mockAnimationsApi } from 'jsdom-testing-mocks'","mockAnimationsApi()","```"].join(`
`)),[]});function We(e){let n={};for(let t in e)e[t]===!0&&(n[`data-${t}`]="");return n}function je(e,n,t,o){let[r,i]=(0,Nn.useState)(t),{hasFlag:s,addFlag:a,removeFlag:l}=Fs(e&&r?3:0),c=(0,Nn.useRef)(!1),u=(0,Nn.useRef)(!1),p=Pe();return N(()=>{var d;if(e){if(t&&i(!0),!n){t&&a(3);return}return(d=o==null?void 0:o.start)==null||d.call(o,t),Ku(n,{inFlight:c,prepare(){u.current?u.current=!1:u.current=c.current,c.current=!0,!u.current&&(t?(a(3),l(4)):(a(4),l(2)))},run(){u.current?t?(l(3),a(4)):(l(4),a(3)):t?l(1):a(1)},done(){var f;u.current&&typeof n.getAnimations=="function"&&n.getAnimations().length>0||(c.current=!1,l(7),t||i(!1),(f=o==null?void 0:o.end)==null||f.call(o,t))}})}},[e,t,n,p]),e?[r,{closed:s(1),enter:s(2),leave:s(4),transition:s(2)||s(4)}]:[t,{closed:void 0,enter:void 0,leave:void 0,transition:void 0}]}function Ku(e,{prepare:n,run:t,done:o,inFlight:r}){let i=xe();return Xu(e,{prepare:n,inFlight:r}),i.nextFrame(()=>{t(),i.requestAnimationFrame(()=>{i.add(zu(e,o))})}),i.dispose}function zu(e,n){var i,s;let t=xe();if(!e)return t.dispose;let o=!1;t.add(()=>{o=!0});let r=(s=(i=e.getAnimations)==null?void 0:i.call(e).filter(a=>a instanceof CSSTransition))!=null?s:[];return r.length===0?(n(),t.dispose):(Promise.allSettled(r.map(a=>a.finished)).then(()=>{o||n()}),t.dispose)}function Xu(e,{inFlight:n,prepare:t}){if(n!=null&&n.current){t();return}let o=e.style.transition;e.style.transition="none",t(),e.offsetHeight,e.style.transition=o}var Bn=require("react");function wo(e,{container:n,accept:t,walk:o}){let r=(0,Bn.useRef)(t),i=(0,Bn.useRef)(o);(0,Bn.useEffect)(()=>{r.current=t,i.current=o},[t,o]),N(()=>{if(!n||!e)return;let s=he(n);if(!s)return;let a=r.current,l=i.current,c=Object.assign(p=>a(p),{acceptNode:a}),u=s.createTreeWalker(n,NodeFilter.SHOW_ELEMENT,c,!1);for(;u.nextNode();)l(u.currentNode)},[n,e,r,i])}var _o=require("react");function Xt(e,n){let t=(0,_o.useRef)([]),o=E(e);(0,_o.useEffect)(()=>{let r=[...t.current];for(let[i,s]of n.entries())if(t.current[i]!==s){let a=o(n,r);return t.current=n,a}},[o,...n])}var Z=oe(require("react"),1),Xn=require("react");function Hs(e){var n;return(e==null||(n=e.ownerDocument)==null?void 0:n.defaultView)||window}function Un(e){return e instanceof Element||e instanceof Hs(e).Element}function ks(){let e=navigator.userAgentData;return e&&Array.isArray(e.brands)?e.brands.map(n=>{let{brand:t,version:o}=n;return t+"/"+o}).join(" "):navigator.userAgent}var Nt=Math.min,Ne=Math.max,Vn=Math.round,Wn=Math.floor,Ct=e=>({x:e,y:e}),Yu={left:"right",right:"left",bottom:"top",top:"bottom"},Ju={start:"end",end:"start"};function $r(e,n,t){return Ne(e,Nt(n,t))}function Tn(e,n){return typeof e=="function"?e(n):e}function At(e){return e.split("-")[0]}function bn(e){return e.split("-")[1]}function Nr(e){return e==="x"?"y":"x"}function Br(e){return e==="y"?"height":"width"}function gn(e){return["top","bottom"].includes(At(e))?"y":"x"}function Ur(e){return Nr(gn(e))}function $s(e,n,t){t===void 0&&(t=!1);let o=bn(e),r=Ur(e),i=Br(r),s=r==="x"?o===(t?"end":"start")?"right":"left":o==="start"?"bottom":"top";return n.reference[i]>n.floating[i]&&(s=Gn(s)),[s,Gn(s)]}function Ns(e){let n=Gn(e);return[Ho(e),n,Ho(n)]}function Ho(e){return e.replace(/start|end/g,n=>Ju[n])}function qu(e,n,t){let o=["left","right"],r=["right","left"],i=["top","bottom"],s=["bottom","top"];switch(e){case"top":case"bottom":return t?n?r:o:n?o:r;case"left":case"right":return n?i:s;default:return[]}}function Bs(e,n,t,o){let r=bn(e),i=qu(At(e),t==="start",o);return r&&(i=i.map(s=>s+"-"+r),n&&(i=i.concat(i.map(Ho)))),i}function Gn(e){return e.replace(/left|right|bottom|top/g,n=>Yu[n])}function Qu(e){return{top:0,right:0,bottom:0,left:0,...e}}function Us(e){return typeof e!="number"?Qu(e):{top:e,right:e,bottom:e,left:e}}function Yt(e){return{...e,top:e.y,left:e.x,right:e.x+e.width,bottom:e.y+e.height}}function Gs(e,n,t){let{reference:o,floating:r}=e,i=gn(n),s=Ur(n),a=Br(s),l=At(n),c=i==="y",u=o.x+o.width/2-r.width/2,p=o.y+o.height/2-r.height/2,d=o[a]/2-r[a]/2,f;switch(l){case"top":f={x:u,y:o.y-r.height};break;case"bottom":f={x:u,y:o.y+o.height};break;case"right":f={x:o.x+o.width,y:p};break;case"left":f={x:o.x-r.width,y:p};break;default:f={x:o.x,y:o.y}}switch(bn(n)){case"start":f[s]-=d*(t&&c?-1:1);break;case"end":f[s]+=d*(t&&c?-1:1);break}return f}var Vs=async(e,n,t)=>{let{placement:o="bottom",strategy:r="absolute",middleware:i=[],platform:s}=t,a=i.filter(Boolean),l=await(s.isRTL==null?void 0:s.isRTL(n)),c=await s.getElementRects({reference:e,floating:n,strategy:r}),{x:u,y:p}=Gs(c,o,l),d=o,f={},m=0;for(let T=0;T<a.length;T++){let{name:b,fn:g}=a[T],{x:y,y:v,data:x,reset:h}=await g({x:u,y:p,initialPlacement:o,placement:d,strategy:r,middlewareData:f,rects:c,platform:s,elements:{reference:e,floating:n}});if(u=y!=null?y:u,p=v!=null?v:p,f={...f,[b]:{...f[b],...x}},h&&m<=50){m++,typeof h=="object"&&(h.placement&&(d=h.placement),h.rects&&(c=h.rects===!0?await s.getElementRects({reference:e,floating:n,strategy:r}):h.rects),{x:u,y:p}=Gs(c,d,l)),T=-1;continue}}return{x:u,y:p,placement:d,strategy:r,middlewareData:f}};async function Tt(e,n){var t;n===void 0&&(n={});let{x:o,y:r,platform:i,rects:s,elements:a,strategy:l}=e,{boundary:c="clippingAncestors",rootBoundary:u="viewport",elementContext:p="floating",altBoundary:d=!1,padding:f=0}=Tn(n,e),m=Us(f),b=a[d?p==="floating"?"reference":"floating":p],g=Yt(await i.getClippingRect({element:(t=await(i.isElement==null?void 0:i.isElement(b)))==null||t?b:b.contextElement||await(i.getDocumentElement==null?void 0:i.getDocumentElement(a.floating)),boundary:c,rootBoundary:u,strategy:l})),y=p==="floating"?{...s.floating,x:o,y:r}:s.reference,v=await(i.getOffsetParent==null?void 0:i.getOffsetParent(a.floating)),x=await(i.isElement==null?void 0:i.isElement(v))?await(i.getScale==null?void 0:i.getScale(v))||{x:1,y:1}:{x:1,y:1},h=Yt(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({rect:y,offsetParent:v,strategy:l}):y);return{top:(g.top-h.top+m.top)/x.y,bottom:(h.bottom-g.bottom+m.bottom)/x.y,left:(g.left-h.left+m.left)/x.x,right:(h.right-g.right+m.right)/x.x}}var Gr=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(n){var t,o;let{placement:r,middlewareData:i,rects:s,initialPlacement:a,platform:l,elements:c}=n,{mainAxis:u=!0,crossAxis:p=!0,fallbackPlacements:d,fallbackStrategy:f="bestFit",fallbackAxisSideDirection:m="none",flipAlignment:T=!0,...b}=Tn(e,n);if((t=i.arrow)!=null&&t.alignmentOffset)return{};let g=At(r),y=At(a)===a,v=await(l.isRTL==null?void 0:l.isRTL(c.floating)),x=d||(y||!T?[Gn(a)]:Ns(a));!d&&m!=="none"&&x.push(...Bs(a,T,m,v));let h=[a,...x],L=await Tt(n,b),D=[],S=((o=i.flip)==null?void 0:o.overflows)||[];if(u&&D.push(L[g]),p){let R=$s(r,s,v);D.push(L[R[0]],L[R[1]])}if(S=[...S,{placement:r,overflows:D}],!D.every(R=>R<=0)){var _,C;let R=(((_=i.flip)==null?void 0:_.index)||0)+1,W=h[R];if(W)return{data:{index:R,overflows:S},reset:{placement:W}};let j=(C=S.filter(X=>X.overflows[0]<=0).sort((X,B)=>X.overflows[1]-B.overflows[1])[0])==null?void 0:C.placement;if(!j)switch(f){case"bestFit":{var F;let X=(F=S.map(B=>[B.placement,B.overflows.filter(U=>U>0).reduce((U,te)=>U+te,0)]).sort((B,U)=>B[1]-U[1])[0])==null?void 0:F[0];X&&(j=X);break}case"initialPlacement":j=a;break}if(r!==j)return{reset:{placement:j}}}return{}}}};async function Zu(e,n){let{placement:t,platform:o,elements:r}=e,i=await(o.isRTL==null?void 0:o.isRTL(r.floating)),s=At(t),a=bn(t),l=gn(t)==="y",c=["left","top"].includes(s)?-1:1,u=i&&l?-1:1,p=Tn(n,e),{mainAxis:d,crossAxis:f,alignmentAxis:m}=typeof p=="number"?{mainAxis:p,crossAxis:0,alignmentAxis:null}:{mainAxis:0,crossAxis:0,alignmentAxis:null,...p};return a&&typeof m=="number"&&(f=a==="end"?m*-1:m),l?{x:f*u,y:d*c}:{x:d*c,y:f*u}}var Vr=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(n){var t,o;let{x:r,y:i,placement:s,middlewareData:a}=n,l=await Zu(n,e);return s===((t=a.offset)==null?void 0:t.placement)&&(o=a.arrow)!=null&&o.alignmentOffset?{}:{x:r+l.x,y:i+l.y,data:{...l,placement:s}}}}},Wr=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(n){let{x:t,y:o,placement:r}=n,{mainAxis:i=!0,crossAxis:s=!1,limiter:a={fn:b=>{let{x:g,y}=b;return{x:g,y}}},...l}=Tn(e,n),c={x:t,y:o},u=await Tt(n,l),p=gn(At(r)),d=Nr(p),f=c[d],m=c[p];if(i){let b=d==="y"?"top":"left",g=d==="y"?"bottom":"right",y=f+u[b],v=f-u[g];f=$r(y,f,v)}if(s){let b=p==="y"?"top":"left",g=p==="y"?"bottom":"right",y=m+u[b],v=m-u[g];m=$r(y,m,v)}let T=a.fn({...n,[d]:f,[p]:m});return{...T,data:{x:T.x-t,y:T.y-o}}}}};var jr=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(n){let{placement:t,rects:o,platform:r,elements:i}=n,{apply:s=()=>{},...a}=Tn(e,n),l=await Tt(n,a),c=At(t),u=bn(t),p=gn(t)==="y",{width:d,height:f}=o.floating,m,T;c==="top"||c==="bottom"?(m=c,T=u===(await(r.isRTL==null?void 0:r.isRTL(i.floating))?"start":"end")?"left":"right"):(T=c,m=u==="end"?"top":"bottom");let b=f-l[m],g=d-l[T],y=!n.middlewareData.shift,v=b,x=g;if(p){let L=d-l.left-l.right;x=u||y?Nt(g,L):L}else{let L=f-l.top-l.bottom;v=u||y?Nt(b,L):L}if(y&&!u){let L=Ne(l.left,0),D=Ne(l.right,0),S=Ne(l.top,0),_=Ne(l.bottom,0);p?x=d-2*(L!==0||D!==0?L+D:Ne(l.left,l.right)):v=f-2*(S!==0||_!==0?S+_:Ne(l.top,l.bottom))}await s({...n,availableWidth:x,availableHeight:v});let h=await r.getDimensions(i.floating);return d!==h.width||f!==h.height?{reset:{rects:!0}}:{}}}};function Ot(e){return js(e)?(e.nodeName||"").toLowerCase():"#document"}function Ue(e){var n;return(e==null||(n=e.ownerDocument)==null?void 0:n.defaultView)||window}function bt(e){var n;return(n=(js(e)?e.ownerDocument:e.document)||window.document)==null?void 0:n.documentElement}function js(e){return e instanceof Node||e instanceof Ue(e).Node}function gt(e){return e instanceof Element||e instanceof Ue(e).Element}function ot(e){return e instanceof HTMLElement||e instanceof Ue(e).HTMLElement}function Ws(e){return typeof ShadowRoot=="undefined"?!1:e instanceof ShadowRoot||e instanceof Ue(e).ShadowRoot}function yn(e){let{overflow:n,overflowX:t,overflowY:o,display:r}=Ke(e);return/auto|scroll|overlay|hidden|clip/.test(n+o+t)&&!["inline","contents"].includes(r)}function Ks(e){return["table","td","th"].includes(Ot(e))}function ko(e){let n=$o(),t=Ke(e);return t.transform!=="none"||t.perspective!=="none"||(t.containerType?t.containerType!=="normal":!1)||!n&&(t.backdropFilter?t.backdropFilter!=="none":!1)||!n&&(t.filter?t.filter!=="none":!1)||["transform","perspective","filter"].some(o=>(t.willChange||"").includes(o))||["paint","layout","strict","content"].some(o=>(t.contain||"").includes(o))}function zs(e){let n=Jt(e);for(;ot(n)&&!jn(n);){if(ko(n))return n;n=Jt(n)}return null}function $o(){return typeof CSS=="undefined"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function jn(e){return["html","body","#document"].includes(Ot(e))}function Ke(e){return Ue(e).getComputedStyle(e)}function Kn(e){return gt(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.pageXOffset,scrollTop:e.pageYOffset}}function Jt(e){if(Ot(e)==="html")return e;let n=e.assignedSlot||e.parentNode||Ws(e)&&e.host||bt(e);return Ws(n)?n.host:n}function Xs(e){let n=Jt(e);return jn(n)?e.ownerDocument?e.ownerDocument.body:e.body:ot(n)&&yn(n)?n:Xs(n)}function Lt(e,n,t){var o;n===void 0&&(n=[]),t===void 0&&(t=!0);let r=Xs(e),i=r===((o=e.ownerDocument)==null?void 0:o.body),s=Ue(r);return i?n.concat(s,s.visualViewport||[],yn(r)?r:[],s.frameElement&&t?Lt(s.frameElement):[]):n.concat(r,Lt(r,[],t))}function qs(e){let n=Ke(e),t=parseFloat(n.width)||0,o=parseFloat(n.height)||0,r=ot(e),i=r?e.offsetWidth:t,s=r?e.offsetHeight:o,a=Vn(t)!==i||Vn(o)!==s;return a&&(t=i,o=s),{width:t,height:o,$:a}}function Kr(e){return gt(e)?e:e.contextElement}function vn(e){let n=Kr(e);if(!ot(n))return Ct(1);let t=n.getBoundingClientRect(),{width:o,height:r,$:i}=qs(n),s=(i?Vn(t.width):t.width)/o,a=(i?Vn(t.height):t.height)/r;return(!s||!Number.isFinite(s))&&(s=1),(!a||!Number.isFinite(a))&&(a=1),{x:s,y:a}}var ic=Ct(0);function Qs(e){let n=Ue(e);return!$o()||!n.visualViewport?ic:{x:n.visualViewport.offsetLeft,y:n.visualViewport.offsetTop}}function sc(e,n,t){return n===void 0&&(n=!1),!t||n&&t!==Ue(e)?!1:n}function qt(e,n,t,o){n===void 0&&(n=!1),t===void 0&&(t=!1);let r=e.getBoundingClientRect(),i=Kr(e),s=Ct(1);n&&(o?gt(o)&&(s=vn(o)):s=vn(e));let a=sc(i,t,o)?Qs(i):Ct(0),l=(r.left+a.x)/s.x,c=(r.top+a.y)/s.y,u=r.width/s.x,p=r.height/s.y;if(i){let d=Ue(i),f=o&&gt(o)?Ue(o):o,m=d.frameElement;for(;m&&o&&f!==d;){let T=vn(m),b=m.getBoundingClientRect(),g=Ke(m),y=b.left+(m.clientLeft+parseFloat(g.paddingLeft))*T.x,v=b.top+(m.clientTop+parseFloat(g.paddingTop))*T.y;l*=T.x,c*=T.y,u*=T.x,p*=T.y,l+=y,c+=v,m=Ue(m).frameElement}}return Yt({width:u,height:p,x:l,y:c})}function lc(e){let{rect:n,offsetParent:t,strategy:o}=e,r=ot(t),i=bt(t);if(t===i)return n;let s={scrollLeft:0,scrollTop:0},a=Ct(1),l=Ct(0);if((r||!r&&o!=="fixed")&&((Ot(t)!=="body"||yn(i))&&(s=Kn(t)),ot(t))){let c=qt(t);a=vn(t),l.x=c.x+t.clientLeft,l.y=c.y+t.clientTop}return{width:n.width*a.x,height:n.height*a.y,x:n.x*a.x-s.scrollLeft*a.x+l.x,y:n.y*a.y-s.scrollTop*a.y+l.y}}function ac(e){return Array.from(e.getClientRects())}function Zs(e){return qt(bt(e)).left+Kn(e).scrollLeft}function uc(e){let n=bt(e),t=Kn(e),o=e.ownerDocument.body,r=Ne(n.scrollWidth,n.clientWidth,o.scrollWidth,o.clientWidth),i=Ne(n.scrollHeight,n.clientHeight,o.scrollHeight,o.clientHeight),s=-t.scrollLeft+Zs(e),a=-t.scrollTop;return Ke(o).direction==="rtl"&&(s+=Ne(n.clientWidth,o.clientWidth)-r),{width:r,height:i,x:s,y:a}}function cc(e,n){let t=Ue(e),o=bt(e),r=t.visualViewport,i=o.clientWidth,s=o.clientHeight,a=0,l=0;if(r){i=r.width,s=r.height;let c=$o();(!c||c&&n==="fixed")&&(a=r.offsetLeft,l=r.offsetTop)}return{width:i,height:s,x:a,y:l}}function fc(e,n){let t=qt(e,!0,n==="fixed"),o=t.top+e.clientTop,r=t.left+e.clientLeft,i=ot(e)?vn(e):Ct(1),s=e.clientWidth*i.x,a=e.clientHeight*i.y,l=r*i.x,c=o*i.y;return{width:s,height:a,x:l,y:c}}function Ys(e,n,t){let o;if(n==="viewport")o=cc(e,t);else if(n==="document")o=uc(bt(e));else if(gt(n))o=fc(n,t);else{let r=Qs(e);o={...n,x:n.x-r.x,y:n.y-r.y}}return Yt(o)}function el(e,n){let t=Jt(e);return t===n||!gt(t)||jn(t)?!1:Ke(t).position==="fixed"||el(t,n)}function dc(e,n){let t=n.get(e);if(t)return t;let o=Lt(e,[],!1).filter(a=>gt(a)&&Ot(a)!=="body"),r=null,i=Ke(e).position==="fixed",s=i?Jt(e):e;for(;gt(s)&&!jn(s);){let a=Ke(s),l=ko(s);!l&&a.position==="fixed"&&(r=null),(i?!l&&!r:!l&&a.position==="static"&&!!r&&["absolute","fixed"].includes(r.position)||yn(s)&&!l&&el(e,s))?o=o.filter(u=>u!==s):r=a,s=Jt(s)}return n.set(e,o),o}function pc(e){let{element:n,boundary:t,rootBoundary:o,strategy:r}=e,s=[...t==="clippingAncestors"?dc(n,this._c):[].concat(t),o],a=s[0],l=s.reduce((c,u)=>{let p=Ys(n,u,r);return c.top=Ne(p.top,c.top),c.right=Nt(p.right,c.right),c.bottom=Nt(p.bottom,c.bottom),c.left=Ne(p.left,c.left),c},Ys(n,a,r));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}}function mc(e){return qs(e)}function Tc(e,n,t){let o=ot(n),r=bt(n),i=t==="fixed",s=qt(e,!0,i,n),a={scrollLeft:0,scrollTop:0},l=Ct(0);if(o||!o&&!i)if((Ot(n)!=="body"||yn(r))&&(a=Kn(n)),o){let c=qt(n,!0,i,n);l.x=c.x+n.clientLeft,l.y=c.y+n.clientTop}else r&&(l.x=Zs(r));return{x:s.left+a.scrollLeft-l.x,y:s.top+a.scrollTop-l.y,width:s.width,height:s.height}}function Js(e,n){return!ot(e)||Ke(e).position==="fixed"?null:n?n(e):e.offsetParent}function tl(e,n){let t=Ue(e);if(!ot(e))return t;let o=Js(e,n);for(;o&&Ks(o)&&Ke(o).position==="static";)o=Js(o,n);return o&&(Ot(o)==="html"||Ot(o)==="body"&&Ke(o).position==="static"&&!ko(o))?t:o||zs(e)||t}var bc=async function(e){let{reference:n,floating:t,strategy:o}=e,r=this.getOffsetParent||tl,i=this.getDimensions;return{reference:Tc(n,await r(t),o),floating:{x:0,y:0,...await i(t)}}};function gc(e){return Ke(e).direction==="rtl"}var No={convertOffsetParentRelativeRectToViewportRelativeRect:lc,getDocumentElement:bt,getClippingRect:pc,getOffsetParent:tl,getElementRects:bc,getClientRects:ac,getDimensions:mc,getScale:vn,isElement:gt,isRTL:gc};function yc(e,n){let t=null,o,r=bt(e);function i(){clearTimeout(o),t&&t.disconnect(),t=null}function s(a,l){a===void 0&&(a=!1),l===void 0&&(l=1),i();let{left:c,top:u,width:p,height:d}=e.getBoundingClientRect();if(a||n(),!p||!d)return;let f=Wn(u),m=Wn(r.clientWidth-(c+p)),T=Wn(r.clientHeight-(u+d)),b=Wn(c),y={rootMargin:-f+"px "+-m+"px "+-T+"px "+-b+"px",threshold:Ne(0,Nt(1,l))||1},v=!0;function x(h){let L=h[0].intersectionRatio;if(L!==l){if(!v)return s();L?s(!1,L):o=setTimeout(()=>{s(!1,1e-7)},100)}v=!1}try{t=new IntersectionObserver(x,{...y,root:r.ownerDocument})}catch{t=new IntersectionObserver(x,y)}t.observe(e)}return s(!0),i}function Bo(e,n,t,o){o===void 0&&(o={});let{ancestorScroll:r=!0,ancestorResize:i=!0,elementResize:s=typeof ResizeObserver=="function",layoutShift:a=typeof IntersectionObserver=="function",animationFrame:l=!1}=o,c=Kr(e),u=r||i?[...c?Lt(c):[],...Lt(n)]:[];u.forEach(g=>{r&&g.addEventListener("scroll",t,{passive:!0}),i&&g.addEventListener("resize",t)});let p=c&&a?yc(c,t):null,d=-1,f=null;s&&(f=new ResizeObserver(g=>{let[y]=g;y&&y.target===c&&f&&(f.unobserve(n),cancelAnimationFrame(d),d=requestAnimationFrame(()=>{f&&f.observe(n)})),t()}),c&&!l&&f.observe(c),f.observe(n));let m,T=l?qt(e):null;l&&b();function b(){let g=qt(e);T&&(g.x!==T.x||g.y!==T.y||g.width!==T.width||g.height!==T.height)&&t(),T=g,m=requestAnimationFrame(b)}return t(),()=>{u.forEach(g=>{r&&g.removeEventListener("scroll",t),i&&g.removeEventListener("resize",t)}),p&&p(),f&&f.disconnect(),f=null,l&&cancelAnimationFrame(m)}}var Uo=(e,n,t)=>{let o=new Map,r={platform:No,...t},i={...r.platform,_c:o};return Vs(e,n,{...r,platform:i})};var Oe=oe(require("react"),1),Wo=require("react"),rl=oe(require("react-dom"),1),Go=typeof document!="undefined"?Wo.useLayoutEffect:Wo.useEffect;function Vo(e,n){if(e===n)return!0;if(typeof e!=typeof n)return!1;if(typeof e=="function"&&e.toString()===n.toString())return!0;let t,o,r;if(e&&n&&typeof e=="object"){if(Array.isArray(e)){if(t=e.length,t!==n.length)return!1;for(o=t;o--!==0;)if(!Vo(e[o],n[o]))return!1;return!0}if(r=Object.keys(e),t=r.length,t!==Object.keys(n).length)return!1;for(o=t;o--!==0;)if(!{}.hasOwnProperty.call(n,r[o]))return!1;for(o=t;o--!==0;){let i=r[o];if(!(i==="_owner"&&e.$$typeof)&&!Vo(e[i],n[i]))return!1}return!0}return e!==e&&n!==n}function il(e){return typeof window=="undefined"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function nl(e,n){let t=il(e);return Math.round(n*t)/t}function ol(e){let n=Oe.useRef(e);return Go(()=>{n.current=e}),n}function sl(e){e===void 0&&(e={});let{placement:n="bottom",strategy:t="absolute",middleware:o=[],platform:r,elements:{reference:i,floating:s}={},transform:a=!0,whileElementsMounted:l,open:c}=e,[u,p]=Oe.useState({x:0,y:0,strategy:t,placement:n,middlewareData:{},isPositioned:!1}),[d,f]=Oe.useState(o);Vo(d,o)||f(o);let[m,T]=Oe.useState(null),[b,g]=Oe.useState(null),y=Oe.useCallback(U=>{U!==L.current&&(L.current=U,T(U))},[]),v=Oe.useCallback(U=>{U!==D.current&&(D.current=U,g(U))},[]),x=i||m,h=s||b,L=Oe.useRef(null),D=Oe.useRef(null),S=Oe.useRef(u),_=l!=null,C=ol(l),F=ol(r),R=Oe.useCallback(()=>{if(!L.current||!D.current)return;let U={placement:n,strategy:t,middleware:d};F.current&&(U.platform=F.current),Uo(L.current,D.current,U).then(te=>{let w={...te,isPositioned:!0};W.current&&!Vo(S.current,w)&&(S.current=w,rl.flushSync(()=>{p(w)}))})},[d,n,t,F]);Go(()=>{c===!1&&S.current.isPositioned&&(S.current.isPositioned=!1,p(U=>({...U,isPositioned:!1})))},[c]);let W=Oe.useRef(!1);Go(()=>(W.current=!0,()=>{W.current=!1}),[]),Go(()=>{if(x&&(L.current=x),h&&(D.current=h),x&&h){if(C.current)return C.current(x,h,R);R()}},[x,h,R,C,_]);let j=Oe.useMemo(()=>({reference:L,floating:D,setReference:y,setFloating:v}),[y,v]),X=Oe.useMemo(()=>({reference:x,floating:h}),[x,h]),B=Oe.useMemo(()=>{let U={position:t,left:0,top:0};if(!X.floating)return U;let te=nl(X.floating,u.x),w=nl(X.floating,u.y);return a?{...U,transform:"translate("+te+"px, "+w+"px)",...il(X.floating)>=1.5&&{willChange:"transform"}}:{position:t,left:te,top:w}},[t,a,X.floating,u.x,u.y]);return Oe.useMemo(()=>({...u,update:R,refs:j,elements:X,floatingStyles:B}),[u,R,j,X,B])}var zn=(e,n)=>({...Vr(e),options:[e,n]}),zr=(e,n)=>({...Wr(e),options:[e,n]});var Xr=(e,n)=>({...Gr(e),options:[e,n]}),Yr=(e,n)=>({...jr(e),options:[e,n]});var En=require("react-dom");var dl={...Z},vc=dl.useInsertionEffect,Ec=vc||(e=>e());function pl(e){let n=Z.useRef(()=>{});return Ec(()=>{n.current=e}),Z.useCallback(function(){for(var t=arguments.length,o=new Array(t),r=0;r<t;r++)o[r]=arguments[r];return n.current==null?void 0:n.current(...o)},[])}var hc="ArrowUp",xc="ArrowDown",Pc="ArrowLeft",Rc="ArrowRight";var qr=typeof document!="undefined"?Xn.useLayoutEffect:Xn.useEffect;var Sc=[Pc,Rc],Cc=[hc,xc],Iy=[...Sc,...Cc];var ll=!1,Ac=0,al=()=>"floating-ui-"+Math.random().toString(36).slice(2,6)+Ac++;function Lc(){let[e,n]=Z.useState(()=>ll?al():void 0);return qr(()=>{e==null&&n(al())},[]),Z.useEffect(()=>{ll=!0},[]),e}var Oc=dl.useId,Dc=Oc||Lc;function Ic(){let e=new Map;return{emit(n,t){var o;(o=e.get(n))==null||o.forEach(r=>r(t))},on(n,t){e.set(n,[...e.get(n)||[],t])},off(n,t){var o;e.set(n,((o=e.get(n))==null?void 0:o.filter(r=>r!==t))||[])}}}var Mc=Z.createContext(null),Fc=Z.createContext(null),wc=()=>{var e;return((e=Z.useContext(Mc))==null?void 0:e.id)||null},_c=()=>Z.useContext(Fc);function Hc(e){let{open:n=!1,onOpenChange:t,elements:o}=e,r=Dc(),i=Z.useRef({}),[s]=Z.useState(()=>Ic()),a=wc()!=null,[l,c]=Z.useState(o.reference),u=pl((f,m,T)=>{i.current.openEvent=f?m:void 0,s.emit("openchange",{open:f,event:m,reason:T,nested:a}),t==null||t(f,m,T)}),p=Z.useMemo(()=>({setPositionReference:c}),[]),d=Z.useMemo(()=>({reference:l||o.reference||null,floating:o.floating||null,domReference:o.reference}),[l,o.reference,o.floating]);return Z.useMemo(()=>({dataRef:i,open:n,onOpenChange:u,elements:d,events:s,floatingId:r,refs:p}),[n,u,d,s,r,p])}function ml(e){e===void 0&&(e={});let{nodeId:n}=e,t=Hc({...e,elements:{reference:null,floating:null,...e.elements}}),o=e.rootContext||t,r=o.elements,[i,s]=Z.useState(null),[a,l]=Z.useState(null),u=(r==null?void 0:r.reference)||i,p=Z.useRef(null),d=_c();qr(()=>{u&&(p.current=u)},[u]);let f=sl({...e,elements:{...r,...a&&{reference:a}}}),m=Z.useCallback(v=>{let x=Un(v)?{getBoundingClientRect:()=>v.getBoundingClientRect(),contextElement:v}:v;l(x),f.refs.setReference(x)},[f.refs]),T=Z.useCallback(v=>{(Un(v)||v===null)&&(p.current=v,s(v)),(Un(f.refs.reference.current)||f.refs.reference.current===null||v!==null&&!Un(v))&&f.refs.setReference(v)},[f.refs]),b=Z.useMemo(()=>({...f.refs,setReference:T,setPositionReference:m,domReference:p}),[f.refs,T,m]),g=Z.useMemo(()=>({...f.elements,domReference:u}),[f.elements,u]),y=Z.useMemo(()=>({...f,...o,refs:b,elements:g,nodeId:n}),[f,b,g,n,o]);return qr(()=>{o.dataRef.current.floatingContext=y;let v=d==null?void 0:d.nodesRef.current.find(x=>x.id===n);v&&(v.context=y)}),Z.useMemo(()=>({...f,context:y,refs:b,elements:g}),[f,b,g,y])}var ul="active",cl="selected";function Jr(e,n,t){let o=new Map,r=t==="item",i=e;if(r&&e){let{[ul]:s,[cl]:a,...l}=e;i=l}return{...t==="floating"&&{tabIndex:-1},...i,...n.map(s=>{let a=s?s[t]:null;return typeof a=="function"?e?a(e):null:a}).concat(e).reduce((s,a)=>(a&&Object.entries(a).forEach(l=>{let[c,u]=l;if(!(r&&[ul,cl].includes(c)))if(c.indexOf("on")===0){if(o.has(c)||o.set(c,[]),typeof u=="function"){var p;(p=o.get(c))==null||p.push(u),s[c]=function(){for(var d,f=arguments.length,m=new Array(f),T=0;T<f;T++)m[T]=arguments[T];return(d=o.get(c))==null?void 0:d.map(b=>b(...m)).find(b=>b!==void 0)}}}else s[c]=u}),s),{})}}function Tl(e){e===void 0&&(e=[]);let n=e,t=Z.useCallback(i=>Jr(i,e,"reference"),n),o=Z.useCallback(i=>Jr(i,e,"floating"),n),r=Z.useCallback(i=>Jr(i,e,"item"),e.map(i=>i==null?void 0:i.item));return Z.useMemo(()=>({getReferenceProps:t,getFloatingProps:o,getItemProps:r}),[t,o,r])}function fl(e,n){return{...e,rects:{...e.rects,floating:{...e.rects.floating,height:n}}}}var bl=e=>({name:"inner",options:e,async fn(n){let{listRef:t,overflowRef:o,onFallbackChange:r,offset:i=0,index:s=0,minItemsVisible:a=4,referenceOverflowThreshold:l=0,scrollRef:c,...u}=e,{rects:p,elements:{floating:d}}=n,f=t.current[s];if(!f)return{};let m={...n,...await zn(-f.offsetTop-d.clientTop-p.reference.height/2-f.offsetHeight/2-i).fn(n)},T=(c==null?void 0:c.current)||d,b=await Tt(fl(m,T.scrollHeight),u),g=await Tt(m,{...u,elementContext:"reference"}),y=Math.max(0,b.top),v=m.y+y,x=Math.max(0,T.scrollHeight-y-Math.max(0,b.bottom));return T.style.maxHeight=x+"px",T.scrollTop=y,r&&(T.offsetHeight<f.offsetHeight*Math.min(a,t.current.length-1)-1||g.top>=-l||g.bottom>=-l?(0,En.flushSync)(()=>r(!0)):(0,En.flushSync)(()=>r(!1))),o&&(o.current=await Tt(fl({...m,y:v},T.offsetHeight),u)),{y:v}}});function gl(e,n){let{open:t,elements:o}=e,{enabled:r=!0,overflowRef:i,scrollRef:s,onChange:a}=n,l=pl(a),c=Z.useRef(!1),u=Z.useRef(null),p=Z.useRef(null);return Z.useEffect(()=>{if(!r)return;function d(m){if(m.ctrlKey||!f||i.current==null)return;let T=m.deltaY,b=i.current.top>=-.5,g=i.current.bottom>=-.5,y=f.scrollHeight-f.clientHeight,v=T<0?-1:1,x=T<0?"max":"min";f.scrollHeight<=f.clientHeight||(!b&&T>0||!g&&T<0?(m.preventDefault(),(0,En.flushSync)(()=>{l(h=>h+Math[x](T,y*v))})):/firefox/i.test(ks())&&(f.scrollTop+=T))}let f=(s==null?void 0:s.current)||o.floating;if(t&&f)return f.addEventListener("wheel",d),requestAnimationFrame(()=>{u.current=f.scrollTop,i.current!=null&&(p.current={...i.current})}),()=>{u.current=null,p.current=null,f.removeEventListener("wheel",d)}},[r,t,o.floating,i,s,l]),Z.useMemo(()=>r?{floating:{onKeyDown(){c.current=!0},onWheel(){c.current=!1},onPointerMove(){c.current=!1},onScroll(){let d=(s==null?void 0:s.current)||o.floating;if(!(!i.current||!d||!c.current)){if(u.current!==null){let f=d.scrollTop-u.current;(i.current.bottom<-.5&&f<-1||i.current.top<-.5&&f>1)&&(0,En.flushSync)(()=>l(m=>m+f))}requestAnimationFrame(()=>{u.current=d.scrollTop})}}}}:{},[r,i,o.floating,s,l])}var Zr=oe(require("react"),1),ve=require("react");var hn=(0,ve.createContext)({styles:void 0,setReference:()=>{},setFloating:()=>{},getReferenceProps:()=>({}),getFloatingProps:()=>({}),slot:{}});hn.displayName="FloatingContext";var ei=(0,ve.createContext)(null);ei.displayName="PlacementContext";function Bt(e){return(0,ve.useMemo)(()=>e?typeof e=="string"?{to:e}:e:null,[e])}function Ut(){return(0,ve.useContext)(hn).setReference}function jo(){return(0,ve.useContext)(hn).getReferenceProps}function Gt(){let{getFloatingProps:e,slot:n}=(0,ve.useContext)(hn);return(0,ve.useCallback)((...t)=>Object.assign({},e(...t),{"data-anchor":n.anchor}),[e,n])}function Vt(e=null){e===!1&&(e=null),typeof e=="string"&&(e={to:e});let n=(0,ve.useContext)(ei),t=(0,ve.useMemo)(()=>e,[JSON.stringify(e,(r,i)=>{var s;return(s=i==null?void 0:i.outerHTML)!=null?s:i})]);N(()=>{n==null||n(t!=null?t:null)},[n,t]);let o=(0,ve.useContext)(hn);return(0,ve.useMemo)(()=>[o.setFloating,e?o.styles:{}],[o.setFloating,e,o.styles])}var yl=4;function Wt({children:e,enabled:n=!0}){let[t,o]=(0,ve.useState)(null),[r,i]=(0,ve.useState)(0),s=(0,ve.useRef)(null),[a,l]=(0,ve.useState)(null);kc(a);let c=n&&t!==null&&a!==null,{to:u="bottom",gap:p=0,offset:d=0,padding:f=0,inner:m}=$c(t,a),[T,b="center"]=u.split(" ");N(()=>{c&&i(0)},[c]);let{refs:g,floatingStyles:y,context:v}=ml({open:c,placement:T==="selection"?b==="center"?"bottom":`bottom-${b}`:b==="center"?`${T}`:`${T}-${b}`,strategy:"absolute",transform:!1,middleware:[zn({mainAxis:T==="selection"?0:p,crossAxis:d}),zr({padding:f}),T!=="selection"&&Xr({padding:f}),T==="selection"&&m?bl({...m,padding:f,overflowRef:s,offset:r,minItemsVisible:yl,referenceOverflowThreshold:f,onFallbackChange(F){var U,te;if(!F)return;let R=v.elements.floating;if(!R)return;let W=parseFloat(getComputedStyle(R).scrollPaddingBottom)||0,j=Math.min(yl,R.childElementCount),X=0,B=0;for(let w of(te=(U=v.elements.floating)==null?void 0:U.childNodes)!=null?te:[])if(w instanceof HTMLElement){let I=w.offsetTop,P=I+w.clientHeight+W,O=R.scrollTop,k=O+R.clientHeight;if(I>=O&&P<=k)j--;else{B=Math.max(0,Math.min(P,k)-Math.max(I,O)),X=w.clientHeight;break}}j>=1&&i(w=>{let I=X*j-B+W;return w>=I?w:I})}}):null,Yr({padding:f,apply({availableWidth:F,availableHeight:R,elements:W}){Object.assign(W.floating.style,{overflow:"auto",maxWidth:`${F}px`,maxHeight:`min(var(--anchor-max-height, 100vh), ${R}px)`})}})].filter(Boolean),whileElementsMounted:Bo}),[x=T,h=b]=v.placement.split("-");T==="selection"&&(x="selection");let L=(0,ve.useMemo)(()=>({anchor:[x,h].filter(Boolean).join(" ")}),[x,h]),D=gl(v,{overflowRef:s,onChange:i}),{getReferenceProps:S,getFloatingProps:_}=Tl([D]),C=E(F=>{l(F),g.setFloating(F)});return Zr.createElement(ei.Provider,{value:o},Zr.createElement(hn.Provider,{value:{setFloating:C,setReference:g.setReference,styles:y,getReferenceProps:S,getFloatingProps:_,slot:L}},e))}function kc(e){N(()=>{if(!e)return;let n=new MutationObserver(()=>{let t=window.getComputedStyle(e).maxHeight,o=parseFloat(t);if(isNaN(o))return;let r=parseInt(t);isNaN(r)||o!==r&&(e.style.maxHeight=`${Math.ceil(o)}px`)});return n.observe(e,{attributes:!0,attributeFilter:["style"]}),()=>{n.disconnect()}},[e])}function $c(e,n){var i,s,a;let t=Qr((i=e==null?void 0:e.gap)!=null?i:"var(--anchor-gap, 0)",n),o=Qr((s=e==null?void 0:e.offset)!=null?s:"var(--anchor-offset, 0)",n),r=Qr((a=e==null?void 0:e.padding)!=null?a:"var(--anchor-padding, 0)",n);return{...e,gap:t,offset:o,padding:r}}function Qr(e,n,t=void 0){let o=Pe(),r=E((l,c)=>{if(l==null)return[t,null];if(typeof l=="number")return[l,null];if(typeof l=="string"){if(!c)return[t,null];let u=vl(l,c);return[u,p=>{let d=El(l);{let f=d.map(m=>window.getComputedStyle(c).getPropertyValue(m));o.requestAnimationFrame(function m(){o.nextFrame(m);let T=!1;for(let[g,y]of d.entries()){let v=window.getComputedStyle(c).getPropertyValue(y);if(f[g]!==v){f[g]=v,T=!0;break}}if(!T)return;let b=vl(l,c);u!==b&&(p(b),u=b)})}return o.dispose}]}return[t,null]}),i=(0,ve.useMemo)(()=>r(e,n)[0],[e,n]),[s=i,a]=(0,ve.useState)();return N(()=>{let[l,c]=r(e,n);if(a(l),!!c)return c(a)},[e,n]),s}function El(e){let n=/var\((.*)\)/.exec(e);if(n){let t=n[1].indexOf(",");if(t===-1)return[n[1]];let o=n[1].slice(0,t).trim(),r=n[1].slice(t+1).trim();return r?[o,...El(r)]:[o]}return[]}function vl(e,n){let t=document.createElement("div");n.appendChild(t),t.style.setProperty("margin-top","0px","important"),t.style.setProperty("margin-top",e,"important");let o=parseFloat(window.getComputedStyle(t).marginTop)||0;return n.removeChild(t),o}var Yn=oe(require("react"),1);function hl({children:e,freeze:n}){let t=xn(n,e);return Yn.default.createElement(Yn.default.Fragment,null,t)}function xn(e,n){let[t,o]=(0,Yn.useState)(n);return!e&&t!==n&&o(n),e?t:n}var Qt=oe(require("react"),1),Ko=(0,Qt.createContext)(null);Ko.displayName="OpenClosedContext";function _e(){return(0,Qt.useContext)(Ko)}function Ye({value:e,children:n}){return Qt.default.createElement(Ko.Provider,{value:e},n)}function Pn({children:e}){return Qt.default.createElement(Ko.Provider,{value:null},e)}function xl(e){function n(){document.readyState!=="loading"&&(e(),document.removeEventListener("DOMContentLoaded",n))}typeof window!="undefined"&&typeof document!="undefined"&&(document.addEventListener("DOMContentLoaded",n),n())}var Je=[];xl(()=>{function e(n){if(!(n.target instanceof HTMLElement)||n.target===document.body||Je[0]===n.target)return;let t=n.target;t=t.closest(kn),Je.unshift(t!=null?t:n.target),Je=Je.filter(o=>o!=null&&o.isConnected),Je.splice(10)}window.addEventListener("click",e,{capture:!0}),window.addEventListener("mousedown",e,{capture:!0}),window.addEventListener("focus",e,{capture:!0}),document.body.addEventListener("click",e,{capture:!0}),document.body.addEventListener("mousedown",e,{capture:!0}),document.body.addEventListener("focus",e,{capture:!0})});function Nc(e){throw new Error("Unexpected object: "+e)}function rt(e,n){let t=n.resolveItems();if(t.length<=0)return null;let o=n.resolveActiveIndex(),r=o!=null?o:-1;switch(e.focus){case 0:{for(let i=0;i<t.length;++i)if(!n.resolveDisabled(t[i],i,t))return i;return o}case 1:{r===-1&&(r=t.length);for(let i=r-1;i>=0;--i)if(!n.resolveDisabled(t[i],i,t))return i;return o}case 2:{for(let i=r+1;i<t.length;++i)if(!n.resolveDisabled(t[i],i,t))return i;return o}case 3:{for(let i=t.length-1;i>=0;--i)if(!n.resolveDisabled(t[i],i,t))return i;return o}case 4:{for(let i=0;i<t.length;++i)if(n.resolveId(t[i],i,t)===e.id)return i;return o}case 5:return null;default:Nc(e)}}var be=oe(require("react"),1),Sl=require("react-dom");var zo=require("react");function Xo(e){let n=E(e),t=(0,zo.useRef)(!1);(0,zo.useEffect)(()=>(t.current=!1,()=>{t.current=!0,ht(()=>{t.current&&n()})}),[n])}var Zt=oe(require("react"),1);function Bc(){let e=typeof document=="undefined";return"useSyncExternalStore"in Zt?(o=>o.useSyncExternalStore)(Zt)(()=>()=>{},()=>!1,()=>!e):!1}function Dt(){let e=Bc(),[n,t]=Zt.useState(Ze.isHandoffComplete);return n&&Ze.isHandoffComplete===!1&&t(!1),Zt.useEffect(()=>{n!==!0&&t(!0)},[n]),Zt.useEffect(()=>Ze.handoff(),[]),e?!1:n}var Rn=oe(require("react"),1),Pl=(0,Rn.createContext)(!1);function Rl(){return(0,Rn.useContext)(Pl)}function ti(e){return Rn.default.createElement(Pl.Provider,{value:e.force},e.children)}function Uc(e){let n=Rl(),t=(0,be.useContext)(Al),o=Le(e),[r,i]=(0,be.useState)(()=>{var l;if(!n&&t!==null)return(l=t.current)!=null?l:null;if(Ze.isServer)return null;let s=o==null?void 0:o.getElementById("headlessui-portal-root");if(s)return s;if(o===null)return null;let a=o.createElement("div");return a.setAttribute("id","headlessui-portal-root"),o.body.appendChild(a)});return(0,be.useEffect)(()=>{r!==null&&(o!=null&&o.body.contains(r)||o==null||o.body.appendChild(r))},[r,o]),(0,be.useEffect)(()=>{n||t!==null&&i(t.current)},[t,i,n]),r}var Cl=be.Fragment,Gc=M(function(n,t){let o=n,r=(0,be.useRef)(null),i=K(ln(f=>{r.current=f}),t),s=Le(r),a=Uc(r),[l]=(0,be.useState)(()=>{var f;return Ze.isServer?null:(f=s==null?void 0:s.createElement("div"))!=null?f:null}),c=(0,be.useContext)(ni),u=Dt();N(()=>{!a||!l||a.contains(l)||(l.setAttribute("data-headlessui-portal",""),a.appendChild(l))},[a,l]),N(()=>{if(l&&c)return c.register(l)},[c,l]),Xo(()=>{var f;!a||!l||(l instanceof Node&&a.contains(l)&&a.removeChild(l),a.childNodes.length<=0&&((f=a.parentElement)==null||f.removeChild(a)))});let p=H();return u?!a||!l?null:(0,Sl.createPortal)(p({ourProps:{ref:i},theirProps:o,slot:{},defaultTag:Cl,name:"Portal"}),l):null});function Vc(e,n){let t=K(n),{enabled:o=!0,...r}=e,i=H();return o?be.default.createElement(Gc,{...r,ref:t}):i({ourProps:{ref:t},theirProps:r,slot:{},defaultTag:Cl,name:"Portal"})}var Wc=be.Fragment,Al=(0,be.createContext)(null);function jc(e,n){let{target:t,...o}=e,i={ref:K(n)},s=H();return be.default.createElement(Al.Provider,{value:t},s({ourProps:i,theirProps:o,defaultTag:Wc,name:"Popover.Group"}))}var ni=(0,be.createContext)(null);function Yo(){let e=(0,be.useContext)(ni),n=(0,be.useRef)([]),t=E(i=>(n.current.push(i),e&&e.register(i),()=>o(i))),o=E(i=>{let s=n.current.indexOf(i);s!==-1&&n.current.splice(s,1),e&&e.unregister(i)}),r=(0,be.useMemo)(()=>({register:t,unregister:o,portals:n}),[t,o,n]);return[n,(0,be.useMemo)(()=>function({children:s}){return be.default.createElement(ni.Provider,{value:r},s)},[r])]}var Kc=M(Vc),oi=M(jc),qe=Object.assign(Kc,{Group:oi});function ri(e,n=t=>t){let t=e.activeOptionIndex!==null?e.options[e.activeOptionIndex]:null,o=n(e.options.slice()),r=o.length>0&&o[0].dataRef.current.order!==null?o.sort((s,a)=>s.dataRef.current.order-a.dataRef.current.order):$e(o,s=>s.dataRef.current.domRef.current),i=t?r.indexOf(t):null;return i===-1&&(i=null),{options:r,activeOptionIndex:i}}var zc={[1](e){var n;return(n=e.dataRef.current)!=null&&n.disabled||e.comboboxState===1?e:{...e,activeOptionIndex:null,comboboxState:1,isTyping:!1,activationTrigger:2,__demoMode:!1}},[0](e){var n,t;if((n=e.dataRef.current)!=null&&n.disabled||e.comboboxState===0)return e;if((t=e.dataRef.current)!=null&&t.value){let o=e.dataRef.current.calculateIndex(e.dataRef.current.value);if(o!==-1)return{...e,activeOptionIndex:o,comboboxState:0,__demoMode:!1}}return{...e,comboboxState:0,__demoMode:!1}},[3](e,n){return e.isTyping===n.isTyping?e:{...e,isTyping:n.isTyping}},[2](e,n){var i,s,a,l;if((i=e.dataRef.current)!=null&&i.disabled||e.optionsElement&&!((s=e.dataRef.current)!=null&&s.optionsPropsRef.current.static)&&e.comboboxState===1)return e;if(e.virtual){let{options:c,disabled:u}=e.virtual,p=n.focus===4?n.idx:rt(n,{resolveItems:()=>c,resolveActiveIndex:()=>{var f,m;return(m=(f=e.activeOptionIndex)!=null?f:c.findIndex(T=>!u(T)))!=null?m:null},resolveDisabled:u,resolveId(){throw new Error("Function not implemented.")}}),d=(a=n.trigger)!=null?a:2;return e.activeOptionIndex===p&&e.activationTrigger===d?e:{...e,activeOptionIndex:p,activationTrigger:d,isTyping:!1,__demoMode:!1}}let t=ri(e);if(t.activeOptionIndex===null){let c=t.options.findIndex(u=>!u.dataRef.current.disabled);c!==-1&&(t.activeOptionIndex=c)}let o=n.focus===4?n.idx:rt(n,{resolveItems:()=>t.options,resolveActiveIndex:()=>t.activeOptionIndex,resolveId:c=>c.id,resolveDisabled:c=>c.dataRef.current.disabled}),r=(l=n.trigger)!=null?l:2;return e.activeOptionIndex===o&&e.activationTrigger===r?e:{...e,...t,isTyping:!1,activeOptionIndex:o,activationTrigger:r,__demoMode:!1}},[4]:(e,n)=>{var i,s,a;if((i=e.dataRef.current)!=null&&i.virtual)return{...e,options:[...e.options,n.payload]};let t=n.payload,o=ri(e,l=>(l.push(t),l));e.activeOptionIndex===null&&(s=e.dataRef.current)!=null&&s.isSelected(n.payload.dataRef.current.value)&&(o.activeOptionIndex=o.options.indexOf(t));let r={...e,...o,activationTrigger:2};return(a=e.dataRef.current)!=null&&a.__demoMode&&e.dataRef.current.value===void 0&&(r.activeOptionIndex=0),r},[5]:(e,n)=>{var o;if((o=e.dataRef.current)!=null&&o.virtual)return{...e,options:e.options.filter(r=>r.id!==n.id)};let t=ri(e,r=>{let i=r.findIndex(s=>s.id===n.id);return i!==-1&&r.splice(i,1),r});return{...e,...t,activationTrigger:2}},[6]:(e,n)=>e.activationTrigger===n.trigger?e:{...e,activationTrigger:n.trigger},[7]:(e,n)=>{var o,r;if(e.virtual===null)return{...e,virtual:{options:n.options,disabled:(o=n.disabled)!=null?o:()=>!1}};if(e.virtual.options===n.options&&e.virtual.disabled===n.disabled)return e;let t=e.activeOptionIndex;if(e.activeOptionIndex!==null){let i=n.options.indexOf(e.virtual.options[e.activeOptionIndex]);i!==-1?t=i:t=null}return{...e,activeOptionIndex:t,virtual:{options:n.options,disabled:(r=n.disabled)!=null?r:()=>!1}}},[8]:(e,n)=>e.inputElement===n.element?e:{...e,inputElement:n.element},[9]:(e,n)=>e.buttonElement===n.element?e:{...e,buttonElement:n.element},[10]:(e,n)=>e.optionsElement===n.element?e:{...e,optionsElement:n.element}},ii=(0,z.createContext)(null);ii.displayName="ComboboxActionsContext";function qn(e){let n=(0,z.useContext)(ii);if(n===null){let t=new Error(`<${e} /> is missing a parent <Combobox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,qn),t}return n}var Ol=(0,z.createContext)(null);function Xc(e){let n=Cn("VirtualProvider"),{options:t}=n.virtual,[o,r]=(0,z.useMemo)(()=>{let c=n.optionsElement;if(!c)return[0,0];let u=window.getComputedStyle(c);return[parseFloat(u.paddingBlockStart||u.paddingTop),parseFloat(u.paddingBlockEnd||u.paddingBottom)]},[n.optionsElement]),i=ps({enabled:t.length!==0,scrollPaddingStart:o,scrollPaddingEnd:r,count:t.length,estimateSize(){return 40},getScrollElement(){return n.optionsElement},overscan:12}),[s,a]=(0,z.useState)(0);N(()=>{a(c=>c+1)},[t]);let l=i.getVirtualItems();return l.length===0?null:z.default.createElement(Ol.Provider,{value:i},z.default.createElement("div",{style:{position:"relative",width:"100%",height:`${i.getTotalSize()}px`},ref:c=>{c&&n.activationTrigger!==0&&n.activeOptionIndex!==null&&t.length>n.activeOptionIndex&&i.scrollToIndex(n.activeOptionIndex)}},l.map(c=>{var u;return z.default.createElement(z.Fragment,{key:c.key},z.default.cloneElement((u=e.children)==null?void 0:u.call(e,{...e.slot,option:t[c.index]}),{key:`${s}-${c.key}`,"data-index":c.index,"aria-setsize":t.length,"aria-posinset":c.index+1,style:{position:"absolute",top:0,left:0,transform:`translateY(${c.start}px)`,overflowAnchor:"none"}}))})))}var Jn=(0,z.createContext)(null);Jn.displayName="ComboboxDataContext";function Cn(e){let n=(0,z.useContext)(Jn);if(n===null){let t=new Error(`<${e} /> is missing a parent <Combobox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,Cn),t}return n}function Yc(e,n){return J(n.type,zc,e,n)}var Jc=z.Fragment;function qc(e,n){var me,Ae;let t=Te(),{value:o,defaultValue:r,onChange:i,form:s,name:a,by:l,disabled:c=t||!1,onClose:u,__demoMode:p=!1,multiple:d=!1,immediate:f=!1,virtual:m=null,nullable:T,...b}=e,g=ut(r),[y=d?[]:void 0,v]=at(o,i,g),[x,h]=(0,z.useReducer)(Yc,{dataRef:(0,z.createRef)(),comboboxState:p?0:1,isTyping:!1,options:[],virtual:m?{options:m.options,disabled:(me=m.disabled)!=null?me:()=>!1}:null,activeOptionIndex:null,activationTrigger:2,inputElement:null,buttonElement:null,optionsElement:null,__demoMode:p}),L=(0,z.useRef)(!1),D=(0,z.useRef)({static:!1,hold:!1}),S=fn(l),_=E(Y=>m?l===null?m.options.indexOf(Y):m.options.findIndex(ge=>S(ge,Y)):x.options.findIndex(ge=>S(ge.dataRef.current.value,Y))),C=(0,z.useCallback)(Y=>J(R.mode,{[1]:()=>y.some(ge=>S(ge,Y)),[0]:()=>S(y,Y)}),[y]),F=E(Y=>x.activeOptionIndex===_(Y)),R=(0,z.useMemo)(()=>({...x,immediate:f,optionsPropsRef:D,value:y,defaultValue:g,disabled:c,mode:d?1:0,virtual:m?x.virtual:null,get activeOptionIndex(){if(L.current&&x.activeOptionIndex===null&&(m?m.options.length>0:x.options.length>0)){if(m){let ge=m.options.findIndex(st=>{var io,Fi;return!((Fi=(io=m.disabled)==null?void 0:io.call(m,st))!=null&&Fi)});if(ge!==-1)return ge}let Y=x.options.findIndex(ge=>!ge.dataRef.current.disabled);if(Y!==-1)return Y}return x.activeOptionIndex},calculateIndex:_,compare:S,isSelected:C,isActive:F}),[y,g,c,d,p,x,m]);N(()=>{var Y;m&&h({type:7,options:m.options,disabled:(Y=m.disabled)!=null?Y:null})},[m,m==null?void 0:m.options,m==null?void 0:m.disabled]),N(()=>{x.dataRef.current=R},[R]);let W=R.comboboxState===0;pt(W,[R.buttonElement,R.inputElement,R.optionsElement],()=>G.closeCombobox());let j=(0,z.useMemo)(()=>{var Y,ge,st;return{open:R.comboboxState===0,disabled:c,activeIndex:R.activeOptionIndex,activeOption:R.activeOptionIndex===null?null:R.virtual?R.virtual.options[(Y=R.activeOptionIndex)!=null?Y:0]:(st=(ge=R.options[R.activeOptionIndex])==null?void 0:ge.dataRef.current.value)!=null?st:null,value:y}},[R,c,y]),X=E(()=>{if(R.activeOptionIndex!==null){if(G.setIsTyping(!1),R.virtual)P(R.virtual.options[R.activeOptionIndex]);else{let{dataRef:Y}=R.options[R.activeOptionIndex];P(Y.current.value)}G.goToOption(4,R.activeOptionIndex)}}),B=E(()=>{h({type:0}),L.current=!0}),U=E(()=>{h({type:1}),L.current=!1,u==null||u()}),te=E(Y=>{h({type:3,isTyping:Y})}),w=E((Y,ge,st)=>(L.current=!1,Y===4?h({type:2,focus:4,idx:ge,trigger:st}):h({type:2,focus:Y,trigger:st}))),I=E((Y,ge)=>(h({type:4,payload:{id:Y,dataRef:ge}}),()=>{R.isActive(ge.current.value)&&(L.current=!0),h({type:5,id:Y})})),P=E(Y=>J(R.mode,{[0](){return v==null?void 0:v(Y)},[1](){let ge=R.value.slice(),st=ge.findIndex(io=>S(io,Y));return st===-1?ge.push(Y):ge.splice(st,1),v==null?void 0:v(ge)}})),O=E(Y=>{h({type:6,trigger:Y})}),k=E(Y=>{h({type:8,element:Y})}),A=E(Y=>{h({type:9,element:Y})}),$=E(Y=>{h({type:10,element:Y})}),G=(0,z.useMemo)(()=>({onChange:P,registerOption:I,goToOption:w,setIsTyping:te,closeCombobox:U,openCombobox:B,setActivationTrigger:O,selectActiveOption:X,setInputElement:k,setButtonElement:A,setOptionsElement:$}),[]),[ie,ce]=ke(),Se=n===null?{}:{ref:n},it=(0,z.useCallback)(()=>{if(g!==void 0)return v==null?void 0:v(g)},[v,g]),ro=H();return z.default.createElement(ce,{value:ie,props:{htmlFor:(Ae=R.inputElement)==null?void 0:Ae.id},slot:{open:R.comboboxState===0,disabled:c}},z.default.createElement(Wt,null,z.default.createElement(ii.Provider,{value:G},z.default.createElement(Jn.Provider,{value:R},z.default.createElement(Ye,{value:J(R.comboboxState,{[0]:1,[1]:2})},a!=null&&z.default.createElement(ct,{disabled:c,data:y!=null?{[a]:y}:{},form:s,onReset:it}),ro({ourProps:Se,theirProps:b,slot:j,defaultTag:Jc,name:"Combobox"}))))))}var Qc="input";function Zc(e,n){var w,I,P,O,k;let t=Cn("Combobox.Input"),o=qn("Combobox.Input"),r=(0,q.useId)(),i=Me(),{id:s=i||`headlessui-combobox-input-${r}`,onChange:a,displayValue:l,disabled:c=t.disabled||!1,autoFocus:u=!1,type:p="text",...d}=e,f=(0,z.useRef)(null),m=K(f,n,Ut(),o.setInputElement),T=Le(t.inputElement),b=Pe(),g=E(()=>{o.onChange(null),t.optionsElement&&(t.optionsElement.scrollTop=0),o.goToOption(5)}),y=(0,z.useMemo)(()=>{var A;return typeof l=="function"&&t.value!==void 0?(A=l(t.value))!=null?A:"":typeof t.value=="string"?t.value:""},[t.value,l]);Xt(([A,$],[G,ie])=>{if(t.isTyping)return;let ce=f.current;ce&&((ie===0&&$===1||A!==G)&&(ce.value=A),requestAnimationFrame(()=>{if(t.isTyping||!ce||(T==null?void 0:T.activeElement)!==ce)return;let{selectionStart:Se,selectionEnd:it}=ce;Math.abs((it!=null?it:0)-(Se!=null?Se:0))===0&&Se===0&&ce.setSelectionRange(ce.value.length,ce.value.length)}))},[y,t.comboboxState,T,t.isTyping]),Xt(([A],[$])=>{if(A===0&&$===1){if(t.isTyping)return;let G=f.current;if(!G)return;let ie=G.value,{selectionStart:ce,selectionEnd:Se,selectionDirection:it}=G;G.value="",G.value=ie,it!==null?G.setSelectionRange(ce,Se,it):G.setSelectionRange(ce,Se)}},[t.comboboxState]);let v=(0,z.useRef)(!1),x=E(()=>{v.current=!0}),h=E(()=>{b.nextFrame(()=>{v.current=!1})}),L=E(A=>{switch(o.setIsTyping(!0),A.key){case"Enter":if(t.comboboxState!==0||v.current)return;if(A.preventDefault(),A.stopPropagation(),t.activeOptionIndex===null){o.closeCombobox();return}o.selectActiveOption(),t.mode===0&&o.closeCombobox();break;case"ArrowDown":return A.preventDefault(),A.stopPropagation(),J(t.comboboxState,{[0]:()=>o.goToOption(2),[1]:()=>o.openCombobox()});case"ArrowUp":return A.preventDefault(),A.stopPropagation(),J(t.comboboxState,{[0]:()=>o.goToOption(1),[1]:()=>{(0,en.flushSync)(()=>o.openCombobox()),t.value||o.goToOption(3)}});case"Home":if(A.shiftKey)break;return A.preventDefault(),A.stopPropagation(),o.goToOption(0);case"PageUp":return A.preventDefault(),A.stopPropagation(),o.goToOption(0);case"End":if(A.shiftKey)break;return A.preventDefault(),A.stopPropagation(),o.goToOption(3);case"PageDown":return A.preventDefault(),A.stopPropagation(),o.goToOption(3);case"Escape":return t.comboboxState!==0?void 0:(A.preventDefault(),t.optionsElement&&!t.optionsPropsRef.current.static&&A.stopPropagation(),t.mode===0&&t.value===null&&g(),o.closeCombobox());case"Tab":if(t.comboboxState!==0)return;t.mode===0&&t.activationTrigger!==1&&o.selectActiveOption(),o.closeCombobox();break}}),D=E(A=>{a==null||a(A),t.mode===0&&A.target.value===""&&g(),o.openCombobox()}),S=E(A=>{var G,ie,ce;let $=(G=A.relatedTarget)!=null?G:Je.find(Se=>Se!==A.currentTarget);if(!((ie=t.optionsElement)!=null&&ie.contains($))&&!((ce=t.buttonElement)!=null&&ce.contains($))&&t.comboboxState===0)return A.preventDefault(),t.mode===0&&t.value===null&&g(),o.closeCombobox()}),_=E(A=>{var G,ie,ce;let $=(G=A.relatedTarget)!=null?G:Je.find(Se=>Se!==A.currentTarget);(ie=t.buttonElement)!=null&&ie.contains($)||(ce=t.optionsElement)!=null&&ce.contains($)||t.disabled||t.immediate&&t.comboboxState!==0&&b.microTask(()=>{(0,en.flushSync)(()=>o.openCombobox()),o.setActivationTrigger(1)})}),C=Ie(),F=He(),{isFocused:R,focusProps:W}=le({autoFocus:u}),{isHovered:j,hoverProps:X}=ae({isDisabled:c}),B=(0,z.useMemo)(()=>({open:t.comboboxState===0,disabled:c,hover:j,focus:R,autofocus:u}),[t,j,R,u,c]),U=re({ref:m,id:s,role:"combobox",type:p,"aria-controls":(w=t.optionsElement)==null?void 0:w.id,"aria-expanded":t.comboboxState===0,"aria-activedescendant":t.activeOptionIndex===null?void 0:t.virtual?(I=t.options.find(A=>!A.dataRef.current.disabled&&t.compare(A.dataRef.current.value,t.virtual.options[t.activeOptionIndex])))==null?void 0:I.id:(P=t.options[t.activeOptionIndex])==null?void 0:P.id,"aria-labelledby":C,"aria-describedby":F,"aria-autocomplete":"list",defaultValue:(k=(O=e.defaultValue)!=null?O:t.defaultValue!==void 0?l==null?void 0:l(t.defaultValue):null)!=null?k:t.defaultValue,disabled:c||void 0,autoFocus:u,onCompositionStart:x,onCompositionEnd:h,onKeyDown:L,onChange:D,onFocus:_,onBlur:S},W,X);return H()({ourProps:U,theirProps:d,slot:B,defaultTag:Qc,name:"Combobox.Input"})}var ef="button";function tf(e,n){var D;let t=Cn("Combobox.Button"),o=qn("Combobox.Button"),r=K(n,o.setButtonElement),i=(0,q.useId)(),{id:s=`headlessui-combobox-button-${i}`,disabled:a=t.disabled||!1,autoFocus:l=!1,...c}=e,u=kr(t.inputElement),p=E(S=>{switch(S.key){case" ":case"Enter":S.preventDefault(),S.stopPropagation(),t.comboboxState===1&&(0,en.flushSync)(()=>o.openCombobox()),u();return;case"ArrowDown":S.preventDefault(),S.stopPropagation(),t.comboboxState===1&&((0,en.flushSync)(()=>o.openCombobox()),t.value||o.goToOption(0)),u();return;case"ArrowUp":S.preventDefault(),S.stopPropagation(),t.comboboxState===1&&((0,en.flushSync)(()=>o.openCombobox()),t.value||o.goToOption(3)),u();return;case"Escape":if(t.comboboxState!==0)return;S.preventDefault(),t.optionsElement&&!t.optionsPropsRef.current.static&&S.stopPropagation(),(0,en.flushSync)(()=>o.closeCombobox()),u();return;default:return}}),d=E(S=>{S.preventDefault(),!Fe(S.currentTarget)&&(S.button===0&&(t.comboboxState===0?o.closeCombobox():o.openCombobox()),u())}),f=Ie([s]),{isFocusVisible:m,focusProps:T}=le({autoFocus:l}),{isHovered:b,hoverProps:g}=ae({isDisabled:a}),{pressed:y,pressProps:v}=Re({disabled:a}),x=(0,z.useMemo)(()=>({open:t.comboboxState===0,active:y||t.comboboxState===0,disabled:a,value:t.value,hover:b,focus:m}),[t,b,m,y,a]),h=re({ref:r,id:s,type:Be(e,t.buttonElement),tabIndex:-1,"aria-haspopup":"listbox","aria-controls":(D=t.optionsElement)==null?void 0:D.id,"aria-expanded":t.comboboxState===0,"aria-labelledby":f,disabled:a||void 0,autoFocus:l,onMouseDown:d,onKeyDown:p},T,g,v);return H()({ourProps:h,theirProps:c,slot:x,defaultTag:ef,name:"Combobox.Button"})}var nf="div",of=3;function rf(e,n){var w,I,P;let t=(0,q.useId)(),{id:o=`headlessui-combobox-options-${t}`,hold:r=!1,anchor:i,portal:s=!1,modal:a=!0,transition:l=!1,...c}=e,u=Cn("Combobox.Options"),p=qn("Combobox.Options"),d=Bt(i);d&&(s=!0);let[f,m]=Vt(d),[T,b]=(0,z.useState)(null),g=Gt(),y=K(n,d?f:null,p.setOptionsElement,b),v=Le(u.optionsElement),x=_e(),[h,L]=je(l,T,x!==null?(x&1)===1:u.comboboxState===0);dt(h,u.inputElement,p.closeCombobox);let D=u.__demoMode?!1:a&&u.comboboxState===0;mt(D,v);let S=u.__demoMode?!1:a&&u.comboboxState===0;_t(S,{allowed:(0,z.useCallback)(()=>[u.inputElement,u.buttonElement,u.optionsElement],[u.inputElement,u.buttonElement,u.optionsElement])}),N(()=>{var O;u.optionsPropsRef.current.static=(O=e.static)!=null?O:!1},[u.optionsPropsRef,e.static]),N(()=>{u.optionsPropsRef.current.hold=r},[u.optionsPropsRef,r]),wo(u.comboboxState===0,{container:u.optionsElement,accept(O){return O.getAttribute("role")==="option"?NodeFilter.FILTER_REJECT:O.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(O){O.setAttribute("role","none")}});let _=Ie([(w=u.buttonElement)==null?void 0:w.id]),C=(0,z.useMemo)(()=>({open:u.comboboxState===0,option:void 0}),[u.comboboxState]),F=E(()=>{p.setActivationTrigger(0)}),R=E(O=>{O.preventDefault(),p.setActivationTrigger(0)}),W=re(d?g():{},{"aria-labelledby":_,role:"listbox","aria-multiselectable":u.mode===1?!0:void 0,id:o,ref:y,style:{...c.style,...m,"--input-width":Rt(u.inputElement,!0).width,"--button-width":Rt(u.buttonElement,!0).width},onWheel:u.activationTrigger===0?void 0:F,onMouseDown:R,...We(L)}),j=h&&u.comboboxState===1,X=xn(j,(I=u.virtual)==null?void 0:I.options),B=xn(j,u.value),U=E(O=>u.compare(B,O));if(u.virtual){if(X===void 0)throw new Error("Missing `options` in virtual mode");Object.assign(c,{children:z.default.createElement(Jn.Provider,{value:X!==u.virtual.options?{...u,virtual:{...u.virtual,options:X}}:u},z.default.createElement(Xc,{slot:C},c.children))})}let te=H();return z.default.createElement(qe,{enabled:s?e.static||h:!1},z.default.createElement(Jn.Provider,{value:u.mode===1?u:{...u,isSelected:U}},te({ourProps:W,theirProps:{...c,children:z.default.createElement(hl,{freeze:j},typeof c.children=="function"?(P=c.children)==null?void 0:P.call(c,C):c.children)},slot:C,defaultTag:nf,features:of,visible:h,name:"Combobox.Options"})))}var sf="div";function lf(e,n){var R,W,j,X;let t=Cn("Combobox.Option"),o=qn("Combobox.Option"),r=(0,q.useId)(),{id:i=`headlessui-combobox-option-${r}`,value:s,disabled:a=(j=(W=(R=t.virtual)==null?void 0:R.disabled)==null?void 0:W.call(R,s))!=null?j:!1,order:l=null,...c}=e,u=kr(t.inputElement),p=t.virtual?t.activeOptionIndex===t.calculateIndex(s):t.activeOptionIndex===null?!1:((X=t.options[t.activeOptionIndex])==null?void 0:X.id)===i,d=t.isSelected(s),f=(0,z.useRef)(null),m=de({disabled:a,value:s,domRef:f,order:l}),T=(0,z.useContext)(Ol),b=K(n,f,T?T.measureElement:null),g=E(()=>{o.setIsTyping(!1),o.onChange(s)});N(()=>o.registerOption(i,m),[m,i]);let y=(0,z.useRef)(!(t.virtual||t.__demoMode));N(()=>{if(!t.virtual&&!t.__demoMode)return xe().requestAnimationFrame(()=>{y.current=!0})},[t.virtual,t.__demoMode]),N(()=>{if(y.current&&t.comboboxState===0&&p&&t.activationTrigger!==0)return xe().requestAnimationFrame(()=>{var B,U;(U=(B=f.current)==null?void 0:B.scrollIntoView)==null||U.call(B,{block:"nearest"})})},[f,p,t.comboboxState,t.activationTrigger,t.activeOptionIndex]);let v=E(B=>{B.preventDefault(),B.button===0&&(a||(g(),Mo()||requestAnimationFrame(()=>u()),t.mode===0&&o.closeCombobox()))}),x=E(()=>{if(a)return o.goToOption(5);let B=t.calculateIndex(s);o.goToOption(4,B)}),h=mn(),L=E(B=>h.update(B)),D=E(B=>{if(!h.wasMoved(B)||a||p)return;let U=t.calculateIndex(s);o.goToOption(4,U,0)}),S=E(B=>{h.wasMoved(B)&&(a||p&&(t.optionsPropsRef.current.hold||o.goToOption(5)))}),_=(0,z.useMemo)(()=>({active:p,focus:p,selected:d,disabled:a}),[p,d,a]),C={id:i,ref:b,role:"option",tabIndex:a===!0?void 0:-1,"aria-disabled":a===!0?!0:void 0,"aria-selected":d,disabled:void 0,onMouseDown:v,onFocus:x,onPointerEnter:L,onMouseEnter:L,onPointerMove:D,onMouseMove:D,onPointerLeave:S,onMouseLeave:S};return H()({ourProps:C,theirProps:c,slot:_,defaultTag:sf,name:"Combobox.Option"})}var af=M(qc),Dl=M(tf),Il=M(Zc),Ml=Xe,Fl=M(rf),wl=M(lf),uf=Object.assign(af,{Input:Il,Button:Dl,Label:Ml,Options:Fl,Option:wl});var Jo=require("react");var cf=Jo.Fragment;function ff(e,n){let{...t}=e,o=!1,{isFocusVisible:r,focusProps:i}=le(),{isHovered:s,hoverProps:a}=ae({isDisabled:o}),{pressed:l,pressProps:c}=Re({disabled:o}),u=re({ref:n},i,a,c),p=(0,Jo.useMemo)(()=>({hover:s,focus:r,active:l}),[s,r,l]);return H()({ourProps:u,theirProps:t,slot:p,defaultTag:cf,name:"DataInteractive"})}var df=M(ff);var ne=oe(require("react"),1);function _l(e,n=typeof document!="undefined"?document.defaultView:null,t){let o=tt(e,"escape");Ht(n,"keydown",r=>{o&&(r.defaultPrevented||r.key==="Escape"&&t(r))})}var si=require("react");function Hl(){var o;let[e]=(0,si.useState)(()=>typeof window!="undefined"&&typeof window.matchMedia=="function"?window.matchMedia("(pointer: coarse)"):null),[n,t]=(0,si.useState)((o=e==null?void 0:e.matches)!=null?o:!1);return N(()=>{if(!e)return;function r(i){t(i.matches)}return e.addEventListener("change",r),()=>e.removeEventListener("change",r)},[e]),n}var It=oe(require("react"),1);function qo({defaultContainers:e=[],portals:n,mainTreeNode:t}={}){let o=Le(t),r=E(()=>{var s,a;let i=[];for(let l of e)l!==null&&(l instanceof HTMLElement?i.push(l):"current"in l&&l.current instanceof HTMLElement&&i.push(l.current));if(n!=null&&n.current)for(let l of n.current)i.push(l);for(let l of(s=o==null?void 0:o.querySelectorAll("html > *, body > *"))!=null?s:[])l!==document.body&&l!==document.head&&l instanceof HTMLElement&&l.id!=="headlessui-portal-root"&&(t&&(l.contains(t)||l.contains((a=t==null?void 0:t.getRootNode())==null?void 0:a.host))||i.some(c=>l.contains(c))||i.push(l));return i});return{resolveContainers:r,contains:E(i=>r().some(s=>s.contains(i)))}}var kl=(0,It.createContext)(null);function An({children:e,node:n}){let[t,o]=(0,It.useState)(null),r=Qn(n!=null?n:t);return It.default.createElement(kl.Provider,{value:r},e,r===null&&It.default.createElement(we,{features:4,ref:i=>{var s,a;if(i){for(let l of(a=(s=he(i))==null?void 0:s.querySelectorAll("html > *, body > *"))!=null?a:[])if(l!==document.body&&l!==document.head&&l instanceof HTMLElement&&l!=null&&l.contains(i)){o(l);break}}}}))}function Qn(e=null){var n;return(n=(0,It.useContext)(kl))!=null?n:e}var yt=oe(require("react"),1);var $l=require("react");function nn(){let e=(0,$l.useRef)(!1);return N(()=>(e.current=!0,()=>{e.current=!1}),[]),e}var Nl=require("react");function Zn(){let e=(0,Nl.useRef)(0);return Fo(!0,"keydown",t=>{t.key==="Tab"&&(e.current=t.shiftKey?1:0)},!0),e}function Bl(e){if(!e)return new Set;if(typeof e=="function")return new Set(e());let n=new Set;for(let t of e.current)t.current instanceof HTMLElement&&n.add(t.current);return n}var mf="div",Qo=(s=>(s[s.None=0]="None",s[s.InitialFocus=1]="InitialFocus",s[s.TabLock=2]="TabLock",s[s.FocusLock=4]="FocusLock",s[s.RestoreFocus=8]="RestoreFocus",s[s.AutoFocus=16]="AutoFocus",s))(Qo||{});function Tf(e,n){let t=(0,yt.useRef)(null),o=K(t,n),{initialFocus:r,initialFocusFallback:i,containers:s,features:a=15,...l}=e;Dt()||(a=0);let c=Le(t);yf(a,{ownerDocument:c});let u=vf(a,{ownerDocument:c,container:t,initialFocus:r,initialFocusFallback:i});Ef(a,{ownerDocument:c,container:t,containers:s,previousActiveElement:u});let p=Zn(),d=E(y=>{let v=t.current;if(!v)return;(h=>h())(()=>{J(p.current,{[0]:()=>{ye(v,1,{skipElements:[y.relatedTarget,i]})},[1]:()=>{ye(v,8,{skipElements:[y.relatedTarget,i]})}})})}),f=tt(!!(a&2),"focus-trap#tab-lock"),m=Pe(),T=(0,yt.useRef)(!1),b={ref:o,onKeyDown(y){y.key=="Tab"&&(T.current=!0,m.requestAnimationFrame(()=>{T.current=!1}))},onBlur(y){if(!(a&4))return;let v=Bl(s);t.current instanceof HTMLElement&&v.add(t.current);let x=y.relatedTarget;x instanceof HTMLElement&&x.dataset.headlessuiFocusGuard!=="true"&&(Ul(v,x)||(T.current?ye(t.current,J(p.current,{[0]:()=>4,[1]:()=>2})|16,{relativeTo:y.target}):y.target instanceof HTMLElement&&nt(y.target)))}},g=H();return yt.default.createElement(yt.default.Fragment,null,f&&yt.default.createElement(we,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:d,features:2}),g({ourProps:b,theirProps:l,defaultTag:mf,name:"FocusTrap"}),f&&yt.default.createElement(we,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:d,features:2}))}var bf=M(Tf),ai=Object.assign(bf,{features:Qo});function gf(e=!0){let n=(0,yt.useRef)(Je.slice());return Xt(([t],[o])=>{o===!0&&t===!1&&ht(()=>{n.current.splice(0)}),o===!1&&t===!0&&(n.current=Je.slice())},[e,Je,n]),E(()=>{var t;return(t=n.current.find(o=>o!=null&&o.isConnected))!=null?t:null})}function yf(e,{ownerDocument:n}){let t=!!(e&8),o=gf(t);Xt(()=>{t||(n==null?void 0:n.activeElement)===(n==null?void 0:n.body)&&nt(o())},[t]),Xo(()=>{t&&nt(o())})}function vf(e,{ownerDocument:n,container:t,initialFocus:o,initialFocusFallback:r}){let i=(0,yt.useRef)(null),s=tt(!!(e&1),"focus-trap#initial-focus"),a=nn();return Xt(()=>{if(e===0)return;if(!s){r!=null&&r.current&&nt(r.current);return}let l=t.current;l&&ht(()=>{if(!a.current)return;let c=n==null?void 0:n.activeElement;if(o!=null&&o.current){if((o==null?void 0:o.current)===c){i.current=c;return}}else if(l.contains(c)){i.current=c;return}if(o!=null&&o.current)nt(o.current);else{if(e&16){if(ye(l,65)!==0)return}else if(ye(l,1)!==0)return;if(r!=null&&r.current&&(nt(r.current),(n==null?void 0:n.activeElement)===r.current))return;console.warn("There are no focusable elements inside the <FocusTrap />")}i.current=n==null?void 0:n.activeElement})},[r,s,e]),i}function Ef(e,{ownerDocument:n,container:t,containers:o,previousActiveElement:r}){let i=nn(),s=!!(e&4);Ht(n==null?void 0:n.defaultView,"focus",a=>{if(!s||!i.current)return;let l=Bl(o);t.current instanceof HTMLElement&&l.add(t.current);let c=r.current;if(!c)return;let u=a.target;u&&u instanceof HTMLElement?Ul(l,u)?(r.current=u,nt(u)):(a.preventDefault(),a.stopPropagation(),nt(c)):nt(r.current)},!0)}function Ul(e,n){for(let t of e)if(t.contains(n))return!0;return!1}var ee=oe(require("react"),1);function Gl(e){var n;return!!(e.enter||e.enterFrom||e.enterTo||e.leave||e.leaveFrom||e.leaveTo)||((n=e.as)!=null?n:Wl)!==ee.Fragment||ee.default.Children.count(e.children)===1}var Zo=(0,ee.createContext)(null);Zo.displayName="TransitionContext";function hf(){let e=(0,ee.useContext)(Zo);if(e===null)throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}function xf(){let e=(0,ee.useContext)(er);if(e===null)throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}var er=(0,ee.createContext)(null);er.displayName="NestingContext";function tr(e){return"children"in e?tr(e.children):e.current.filter(({el:n})=>n.current!==null).filter(({state:n})=>n==="visible").length>0}function Vl(e,n){let t=de(e),o=(0,ee.useRef)([]),r=nn(),i=Pe(),s=E((f,m=1)=>{let T=o.current.findIndex(({el:b})=>b===f);T!==-1&&(J(m,{[0](){o.current.splice(T,1)},[1](){o.current[T].state="hidden"}}),i.microTask(()=>{var b;!tr(o)&&r.current&&((b=t.current)==null||b.call(t))}))}),a=E(f=>{let m=o.current.find(({el:T})=>T===f);return m?m.state!=="visible"&&(m.state="visible"):o.current.push({el:f,state:"visible"}),()=>s(f,0)}),l=(0,ee.useRef)([]),c=(0,ee.useRef)(Promise.resolve()),u=(0,ee.useRef)({enter:[],leave:[]}),p=E((f,m,T)=>{l.current.splice(0),n&&(n.chains.current[m]=n.chains.current[m].filter(([b])=>b!==f)),n==null||n.chains.current[m].push([f,new Promise(b=>{l.current.push(b)})]),n==null||n.chains.current[m].push([f,new Promise(b=>{Promise.all(u.current[m].map(([g,y])=>y)).then(()=>b())})]),m==="enter"?c.current=c.current.then(()=>n==null?void 0:n.wait.current).then(()=>T(m)):T(m)}),d=E((f,m,T)=>{Promise.all(u.current[m].splice(0).map(([b,g])=>g)).then(()=>{var b;(b=l.current.shift())==null||b()}).then(()=>T(m))});return(0,ee.useMemo)(()=>({children:o,register:a,unregister:s,onStart:p,onStop:d,wait:c,chains:u}),[a,s,o,p,d,u,c])}var Wl=ee.Fragment,jl=1;function Pf(e,n){var $,G;let{transition:t=!0,beforeEnter:o,afterEnter:r,beforeLeave:i,afterLeave:s,enter:a,enterFrom:l,enterTo:c,entered:u,leave:p,leaveFrom:d,leaveTo:f,...m}=e,[T,b]=(0,ee.useState)(null),g=(0,ee.useRef)(null),y=Gl(e),v=K(...y?[g,n,b]:n===null?[]:[n]),x=($=m.unmount)==null||$?0:1,{show:h,appear:L,initial:D}=hf(),[S,_]=(0,ee.useState)(h?"visible":"hidden"),C=xf(),{register:F,unregister:R}=C;N(()=>F(g),[F,g]),N(()=>{if(x===1&&g.current){if(h&&S!=="visible"){_("visible");return}return J(S,{["hidden"]:()=>R(g),["visible"]:()=>F(g)})}},[S,g,F,R,h,x]);let W=Dt();N(()=>{if(y&&W&&S==="visible"&&g.current===null)throw new Error("Did you forget to passthrough the `ref` to the actual DOM node?")},[g,S,W,y]);let j=D&&!L,X=L&&h&&D,B=(0,ee.useRef)(!1),U=Vl(()=>{B.current||(_("hidden"),R(g))},C),te=E(ie=>{B.current=!0;let ce=ie?"enter":"leave";U.onStart(g,ce,Se=>{Se==="enter"?o==null||o():Se==="leave"&&(i==null||i())})}),w=E(ie=>{let ce=ie?"enter":"leave";B.current=!1,U.onStop(g,ce,Se=>{Se==="enter"?r==null||r():Se==="leave"&&(s==null||s())}),ce==="leave"&&!tr(U)&&(_("hidden"),R(g))});(0,ee.useEffect)(()=>{y&&t||(te(h),w(h))},[h,y,t]);let I=(()=>!(!t||!y||!W||j))(),[,P]=je(I,T,h,{start:te,end:w}),O=lt({ref:v,className:((G=_n(m.className,X&&a,X&&l,P.enter&&a,P.enter&&P.closed&&l,P.enter&&!P.closed&&c,P.leave&&p,P.leave&&!P.closed&&d,P.leave&&P.closed&&f,!P.transition&&h&&u))==null?void 0:G.trim())||void 0,...We(P)}),k=0;S==="visible"&&(k|=1),S==="hidden"&&(k|=2),P.enter&&(k|=8),P.leave&&(k|=4);let A=H();return ee.default.createElement(er.Provider,{value:U},ee.default.createElement(Ye,{value:k},A({ourProps:O,theirProps:m,defaultTag:Wl,features:jl,visible:S==="visible",name:"Transition.Child"})))}function Rf(e,n){let{show:t,appear:o=!1,unmount:r=!0,...i}=e,s=(0,ee.useRef)(null),a=Gl(e),l=K(...a?[s,n]:n===null?[]:[n]);Dt();let c=_e();if(t===void 0&&c!==null&&(t=(c&1)===1),t===void 0)throw new Error("A <Transition /> is used but it is missing a `show={true | false}` prop.");let[u,p]=(0,ee.useState)(t?"visible":"hidden"),d=Vl(()=>{t||p("hidden")}),[f,m]=(0,ee.useState)(!0),T=(0,ee.useRef)([t]);N(()=>{f!==!1&&T.current[T.current.length-1]!==t&&(T.current.push(t),m(!1))},[T,t]);let b=(0,ee.useMemo)(()=>({show:t,appear:o,initial:f}),[t,o,f]);N(()=>{t?p("visible"):!tr(d)&&s.current!==null&&p("hidden")},[t,d]);let g={unmount:r},y=E(()=>{var h;f&&m(!1),(h=e.beforeEnter)==null||h.call(e)}),v=E(()=>{var h;f&&m(!1),(h=e.beforeLeave)==null||h.call(e)}),x=H();return ee.default.createElement(er.Provider,{value:d},ee.default.createElement(Zo.Provider,{value:b},x({ourProps:{...g,as:ee.Fragment,children:ee.default.createElement(Kl,{ref:l,...g,...i,beforeEnter:y,beforeLeave:v})},theirProps:{},defaultTag:ee.Fragment,features:jl,visible:u==="visible",name:"Transition"})))}function Sf(e,n){let t=(0,ee.useContext)(Zo)!==null,o=_e()!==null;return ee.default.createElement(ee.default.Fragment,null,!t&&o?ee.default.createElement(ui,{ref:n,...e}):ee.default.createElement(Kl,{ref:n,...e}))}var ui=M(Rf),Kl=M(Pf),to=M(Sf),ci=Object.assign(ui,{Child:to,Root:ui});var Cf={[0](e,n){return e.titleId===n.id?e:{...e,titleId:n.id}}},fi=(0,ne.createContext)(null);fi.displayName="DialogContext";function nr(e){let n=(0,ne.useContext)(fi);if(n===null){let t=new Error(`<${e} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,nr),t}return n}function Af(e,n){return J(n.type,Cf,e,n)}var zl=M(function(n,t){let o=(0,q.useId)(),{id:r=`headlessui-dialog-${o}`,open:i,onClose:s,initialFocus:a,role:l="dialog",autoFocus:c=!0,__demoMode:u=!1,unmount:p=!1,...d}=n,f=(0,ne.useRef)(!1);l=function(){return l==="dialog"||l==="alertdialog"?l:(f.current||(f.current=!0,console.warn(`Invalid role [${l}] passed to <Dialog />. Only \`dialog\` and and \`alertdialog\` are supported. Using \`dialog\` instead.`)),"dialog")}();let m=_e();i===void 0&&m!==null&&(i=(m&1)===1);let T=(0,ne.useRef)(null),b=K(T,t),g=Le(T),y=i?0:1,[v,x]=(0,ne.useReducer)(Af,{titleId:null,descriptionId:null,panelRef:(0,ne.createRef)()}),h=E(()=>s(!1)),L=E($=>x({type:0,id:$})),S=Dt()?y===0:!1,[_,C]=Yo(),F={get current(){var $;return($=v.panelRef.current)!=null?$:T.current}},R=Qn(),{resolveContainers:W}=qo({mainTreeNode:R,portals:_,defaultContainers:[F]}),j=m!==null?(m&4)===4:!1;_t(u||j?!1:S,{allowed:E(()=>{var $,G;return[(G=($=T.current)==null?void 0:$.closest("[data-headlessui-portal]"))!=null?G:null]}),disallowed:E(()=>{var $;return[($=R==null?void 0:R.closest("body > *:not(#headlessui-portal-root)"))!=null?$:null]})}),pt(S,W,$=>{$.preventDefault(),h()}),_l(S,g==null?void 0:g.defaultView,$=>{$.preventDefault(),$.stopPropagation(),document.activeElement&&"blur"in document.activeElement&&typeof document.activeElement.blur=="function"&&document.activeElement.blur(),h()}),mt(u||j?!1:S,g,W),dt(S,T,h);let[U,te]=et(),w=(0,ne.useMemo)(()=>[{dialogState:y,close:h,setTitleId:L,unmount:p},v],[y,v,h,L,p]),I=(0,ne.useMemo)(()=>({open:y===0}),[y]),P={ref:b,id:r,role:l,tabIndex:-1,"aria-modal":u?void 0:y===0?!0:void 0,"aria-labelledby":v.titleId,"aria-describedby":U,unmount:p},O=!Hl(),k=0;S&&!u&&(k|=8,k|=2,c&&(k|=16),O&&(k|=1));let A=H();return ne.default.createElement(Pn,null,ne.default.createElement(ti,{force:!0},ne.default.createElement(qe,null,ne.default.createElement(fi.Provider,{value:w},ne.default.createElement(oi,{target:T},ne.default.createElement(ti,{force:!1},ne.default.createElement(te,{slot:I},ne.default.createElement(C,null,ne.default.createElement(ai,{initialFocus:a,initialFocusFallback:T,containers:W,features:k},ne.default.createElement(un,{value:h},A({ourProps:P,theirProps:d,slot:I,defaultTag:Lf,features:Of,visible:y===0,name:"Dialog"})))))))))))}),Lf="div",Of=3;function Df(e,n){let{transition:t=!1,open:o,...r}=e,i=_e(),s=e.hasOwnProperty("open")||i!==null,a=e.hasOwnProperty("onClose");if(!s&&!a)throw new Error("You have to provide an `open` and an `onClose` prop to the `Dialog` component.");if(!s)throw new Error("You provided an `onClose` prop to the `Dialog`, but forgot an `open` prop.");if(!a)throw new Error("You provided an `open` prop to the `Dialog`, but forgot an `onClose` prop.");if(!i&&typeof e.open!="boolean")throw new Error(`You provided an \`open\` prop to the \`Dialog\`, but the value is not a boolean. Received: ${e.open}`);if(typeof e.onClose!="function")throw new Error(`You provided an \`onClose\` prop to the \`Dialog\`, but the value is not a function. Received: ${e.onClose}`);return(o!==void 0||t)&&!r.static?ne.default.createElement(An,null,ne.default.createElement(ci,{show:o,transition:t,unmount:r.unmount},ne.default.createElement(zl,{ref:n,...r}))):ne.default.createElement(An,null,ne.default.createElement(zl,{ref:n,open:o,...r}))}var If="div";function Mf(e,n){let t=(0,q.useId)(),{id:o=`headlessui-dialog-panel-${t}`,transition:r=!1,...i}=e,[{dialogState:s,unmount:a},l]=nr("Dialog.Panel"),c=K(n,l.panelRef),u=(0,ne.useMemo)(()=>({open:s===0}),[s]),p=E(b=>{b.stopPropagation()}),d={ref:c,id:o,onClick:p},f=r?to:ne.Fragment,m=r?{unmount:a}:{},T=H();return ne.default.createElement(f,{...m},T({ourProps:d,theirProps:i,slot:u,defaultTag:If,name:"Dialog.Panel"}))}var Ff="div";function wf(e,n){let{transition:t=!1,...o}=e,[{dialogState:r,unmount:i}]=nr("Dialog.Backdrop"),s=(0,ne.useMemo)(()=>({open:r===0}),[r]),a={ref:n,"aria-hidden":!0},l=t?to:ne.Fragment,c=t?{unmount:i}:{},u=H();return ne.default.createElement(l,{...c},u({ourProps:a,theirProps:o,slot:s,defaultTag:Ff,name:"Dialog.Backdrop"}))}var _f="h2";function Hf(e,n){let t=(0,q.useId)(),{id:o=`headlessui-dialog-title-${t}`,...r}=e,[{dialogState:i,setTitleId:s}]=nr("Dialog.Title"),a=K(n);(0,ne.useEffect)(()=>(s(o),()=>s(null)),[o,s]);let l=(0,ne.useMemo)(()=>({open:i===0}),[i]),c={ref:a,id:o};return H()({ourProps:c,theirProps:r,slot:l,defaultTag:_f,name:"Dialog.Title"})}var kf=M(Df),Xl=M(Mf),$f=M(wf),Yl=M(Hf),Nf=xt,Bf=Object.assign(kf,{Panel:Xl,Title:Yl,Description:xt});var se=oe(require("react"),1);var ql=oe(require("react"),1),Jl,Ql=(Jl=ql.default.startTransition)!=null?Jl:function(n){n()};var Uf={[0]:e=>({...e,disclosureState:J(e.disclosureState,{[0]:1,[1]:0})}),[1]:e=>e.disclosureState===1?e:{...e,disclosureState:1},[2](e,n){return e.buttonId===n.buttonId?e:{...e,buttonId:n.buttonId}},[3](e,n){return e.panelId===n.panelId?e:{...e,panelId:n.panelId}},[4](e,n){return e.buttonElement===n.element?e:{...e,buttonElement:n.element}},[5](e,n){return e.panelElement===n.element?e:{...e,panelElement:n.element}}},di=(0,se.createContext)(null);di.displayName="DisclosureContext";function pi(e){let n=(0,se.useContext)(di);if(n===null){let t=new Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,pi),t}return n}var mi=(0,se.createContext)(null);mi.displayName="DisclosureAPIContext";function Zl(e){let n=(0,se.useContext)(mi);if(n===null){let t=new Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,Zl),t}return n}var Ti=(0,se.createContext)(null);Ti.displayName="DisclosurePanelContext";function Gf(){return(0,se.useContext)(Ti)}function Vf(e,n){return J(n.type,Uf,e,n)}var Wf=se.Fragment;function jf(e,n){let{defaultOpen:t=!1,...o}=e,r=(0,se.useRef)(null),i=K(n,ln(T=>{r.current=T},e.as===void 0||e.as===se.Fragment)),s=(0,se.useReducer)(Vf,{disclosureState:t?0:1,buttonElement:null,panelElement:null,buttonId:null,panelId:null}),[{disclosureState:a,buttonId:l},c]=s,u=E(T=>{c({type:1});let b=he(r);if(!b||!l)return;let g=(()=>T?T instanceof HTMLElement?T:T.current instanceof HTMLElement?T.current:b.getElementById(l):b.getElementById(l))();g==null||g.focus()}),p=(0,se.useMemo)(()=>({close:u}),[u]),d=(0,se.useMemo)(()=>({open:a===0,close:u}),[a,u]),f={ref:i},m=H();return se.default.createElement(di.Provider,{value:s},se.default.createElement(mi.Provider,{value:p},se.default.createElement(un,{value:u},se.default.createElement(Ye,{value:J(a,{[0]:1,[1]:2})},m({ourProps:f,theirProps:o,slot:d,defaultTag:Wf,name:"Disclosure"})))))}var Kf="button";function zf(e,n){let t=(0,q.useId)(),{id:o=`headlessui-disclosure-button-${t}`,disabled:r=!1,autoFocus:i=!1,...s}=e,[a,l]=pi("Disclosure.Button"),c=Gf(),u=c===null?!1:c===a.panelId,p=(0,se.useRef)(null),d=K(p,n,E(C=>{if(!u)return l({type:4,element:C})}));(0,se.useEffect)(()=>{if(!u)return l({type:2,buttonId:o}),()=>{l({type:2,buttonId:null})}},[o,l,u]);let f=E(C=>{var F;if(u){if(a.disclosureState===1)return;switch(C.key){case" ":case"Enter":C.preventDefault(),C.stopPropagation(),l({type:0}),(F=a.buttonElement)==null||F.focus();break}}else switch(C.key){case" ":case"Enter":C.preventDefault(),C.stopPropagation(),l({type:0});break}}),m=E(C=>{switch(C.key){case" ":C.preventDefault();break}}),T=E(C=>{var F;Fe(C.currentTarget)||r||(u?(l({type:0}),(F=a.buttonElement)==null||F.focus()):l({type:0}))}),{isFocusVisible:b,focusProps:g}=le({autoFocus:i}),{isHovered:y,hoverProps:v}=ae({isDisabled:r}),{pressed:x,pressProps:h}=Re({disabled:r}),L=(0,se.useMemo)(()=>({open:a.disclosureState===0,hover:y,active:x,disabled:r,focus:b,autofocus:i}),[a,y,x,b,r,i]),D=Be(e,a.buttonElement),S=u?re({ref:d,type:D,disabled:r||void 0,autoFocus:i,onKeyDown:f,onClick:T},g,v,h):re({ref:d,id:o,type:D,"aria-expanded":a.disclosureState===0,"aria-controls":a.panelElement?a.panelId:void 0,disabled:r||void 0,autoFocus:i,onKeyDown:f,onKeyUp:m,onClick:T},g,v,h);return H()({ourProps:S,theirProps:s,slot:L,defaultTag:Kf,name:"Disclosure.Button"})}var Xf="div",Yf=3;function Jf(e,n){let t=(0,q.useId)(),{id:o=`headlessui-disclosure-panel-${t}`,transition:r=!1,...i}=e,[s,a]=pi("Disclosure.Panel"),{close:l}=Zl("Disclosure.Panel"),[c,u]=(0,se.useState)(null),p=K(n,E(y=>{Ql(()=>a({type:5,element:y}))}),u);(0,se.useEffect)(()=>(a({type:3,panelId:o}),()=>{a({type:3,panelId:null})}),[o,a]);let d=_e(),[f,m]=je(r,c,d!==null?(d&1)===1:s.disclosureState===0),T=(0,se.useMemo)(()=>({open:s.disclosureState===0,close:l}),[s.disclosureState,l]),b={ref:p,id:o,...We(m)},g=H();return se.default.createElement(Pn,null,se.default.createElement(Ti.Provider,{value:s.panelId},g({ourProps:b,theirProps:i,slot:T,defaultTag:Xf,features:Yf,visible:f,name:"Disclosure.Panel"})))}var qf=M(jf),ea=M(zf),ta=M(Jf),Qf=Object.assign(qf,{Button:ea,Panel:ta});var jt=oe(require("react"),1);var Zf="div";function ed(e,n){let t=`headlessui-control-${(0,q.useId)()}`,[o,r]=ke(),[i,s]=et(),a=Te(),{disabled:l=a||!1,...c}=e,u=(0,jt.useMemo)(()=>({disabled:l}),[l]),p={ref:n,disabled:l||void 0,"aria-disabled":l||void 0},d=H();return jt.default.createElement(go,{value:l},jt.default.createElement(r,{value:o},jt.default.createElement(s,{value:i},jt.default.createElement(es,{id:t},d({ourProps:p,theirProps:{...c,children:jt.default.createElement(Qi,null,typeof c.children=="function"?c.children(u):c.children)},slot:u,defaultTag:Zf,name:"Field"})))))}var td=M(ed);var no=oe(require("react"),1);var or=require("react");function na(e){let n=typeof e=="string"?e:void 0,[t,o]=(0,or.useState)(n);return[n!=null?n:t,(0,or.useCallback)(r=>{n||r instanceof HTMLElement&&o(r.tagName.toLowerCase())},[n])]}var oa="fieldset";function nd(e,n){var f;let t=Te(),{disabled:o=t||!1,...r}=e,[i,s]=na((f=e.as)!=null?f:oa),a=K(n,s),[l,c]=ke(),u=(0,no.useMemo)(()=>({disabled:o}),[o]),p=i==="fieldset"?{ref:a,"aria-labelledby":l,disabled:o||void 0}:{ref:a,role:"group","aria-labelledby":l,"aria-disabled":o||void 0},d=H();return no.default.createElement(go,{value:o},no.default.createElement(c,null,d({ourProps:p,theirProps:r,slot:u,defaultTag:oa,name:"Fieldset"})))}var od=M(nd);var ra=require("react");var rd="input";function id(e,n){let t=(0,q.useId)(),o=Me(),r=Te(),{id:i=o||`headlessui-input-${t}`,disabled:s=r||!1,autoFocus:a=!1,invalid:l=!1,...c}=e,u=Ie(),p=He(),{isFocused:d,focusProps:f}=le({autoFocus:a}),{isHovered:m,hoverProps:T}=ae({isDisabled:s}),b=re({ref:n,id:i,"aria-labelledby":u,"aria-describedby":p,"aria-invalid":l?"":void 0,disabled:s||void 0,autoFocus:a},f,T),g=(0,ra.useMemo)(()=>({disabled:s,invalid:l,hover:m,focus:d,autofocus:a}),[s,l,m,d,a]);return H()({ourProps:b,theirProps:c,slot:g,defaultTag:rd,name:"Input"})}var sd=M(id);var ia=oe(require("react"),1);function ld(e,n){return ia.default.createElement(Xe,{as:"div",ref:n,...e})}var ad=M(ld);var Q=oe(require("react"),1),Kt=require("react-dom");var sa=require("react");function rr(e,n){let t=(0,sa.useRef)({left:0,top:0});if(N(()=>{if(!n)return;let i=n.getBoundingClientRect();i&&(t.current=i)},[e,n]),n==null||!e||n===document.activeElement)return!1;let o=n.getBoundingClientRect();return o.top!==t.current.top||o.left!==t.current.left}var bi=require("react");var la=/([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g;function aa(e){var i,s;let n=(i=e.innerText)!=null?i:"",t=e.cloneNode(!0);if(!(t instanceof HTMLElement))return n;let o=!1;for(let a of t.querySelectorAll('[hidden],[aria-hidden],[role="img"]'))a.remove(),o=!0;let r=o?(s=t.innerText)!=null?s:"":n;return la.test(r)&&(r=r.replace(la,"")),r}function ua(e){let n=e.getAttribute("aria-label");if(typeof n=="string")return n.trim();let t=e.getAttribute("aria-labelledby");if(t){let o=t.split(" ").map(r=>{let i=document.getElementById(r);if(i){let s=i.getAttribute("aria-label");return typeof s=="string"?s.trim():aa(i).trim()}return null}).filter(Boolean);if(o.length>0)return o.join(", ")}return aa(e).trim()}function ir(e){let n=(0,bi.useRef)(""),t=(0,bi.useRef)("");return E(()=>{let o=e.current;if(!o)return"";let r=o.innerText;if(n.current===r)return t.current;let i=ua(o).trim().toLowerCase();return n.current=r,t.current=i,i})}function gi(e,n=t=>t){let t=e.activeOptionIndex!==null?e.options[e.activeOptionIndex]:null,o=$e(n(e.options.slice()),i=>i.dataRef.current.domRef.current),r=t?o.indexOf(t):null;return r===-1&&(r=null),{options:o,activeOptionIndex:r}}var ud={[1](e){return e.dataRef.current.disabled||e.listboxState===1?e:{...e,activeOptionIndex:null,listboxState:1,__demoMode:!1}},[0](e){if(e.dataRef.current.disabled||e.listboxState===0)return e;let n=e.activeOptionIndex,{isSelected:t}=e.dataRef.current,o=e.options.findIndex(r=>t(r.dataRef.current.value));return o!==-1&&(n=o),{...e,listboxState:0,activeOptionIndex:n,__demoMode:!1}},[2](e,n){var i,s,a,l,c;if(e.dataRef.current.disabled||e.listboxState===1)return e;let t={...e,searchQuery:"",activationTrigger:(i=n.trigger)!=null?i:1,__demoMode:!1};if(n.focus===5)return{...t,activeOptionIndex:null};if(n.focus===4)return{...t,activeOptionIndex:e.options.findIndex(u=>u.id===n.id)};if(n.focus===1){let u=e.activeOptionIndex;if(u!==null){let p=e.options[u].dataRef.current.domRef,d=rt(n,{resolveItems:()=>e.options,resolveActiveIndex:()=>e.activeOptionIndex,resolveId:f=>f.id,resolveDisabled:f=>f.dataRef.current.disabled});if(d!==null){let f=e.options[d].dataRef.current.domRef;if(((s=p.current)==null?void 0:s.previousElementSibling)===f.current||((a=f.current)==null?void 0:a.previousElementSibling)===null)return{...t,activeOptionIndex:d}}}}else if(n.focus===2){let u=e.activeOptionIndex;if(u!==null){let p=e.options[u].dataRef.current.domRef,d=rt(n,{resolveItems:()=>e.options,resolveActiveIndex:()=>e.activeOptionIndex,resolveId:f=>f.id,resolveDisabled:f=>f.dataRef.current.disabled});if(d!==null){let f=e.options[d].dataRef.current.domRef;if(((l=p.current)==null?void 0:l.nextElementSibling)===f.current||((c=f.current)==null?void 0:c.nextElementSibling)===null)return{...t,activeOptionIndex:d}}}}let o=gi(e),r=rt(n,{resolveItems:()=>o.options,resolveActiveIndex:()=>o.activeOptionIndex,resolveId:u=>u.id,resolveDisabled:u=>u.dataRef.current.disabled});return{...t,...o,activeOptionIndex:r}},[3]:(e,n)=>{if(e.dataRef.current.disabled||e.listboxState===1)return e;let o=e.searchQuery!==""?0:1,r=e.searchQuery+n.value.toLowerCase(),s=(e.activeOptionIndex!==null?e.options.slice(e.activeOptionIndex+o).concat(e.options.slice(0,e.activeOptionIndex+o)):e.options).find(l=>{var c;return!l.dataRef.current.disabled&&((c=l.dataRef.current.textValue)==null?void 0:c.startsWith(r))}),a=s?e.options.indexOf(s):-1;return a===-1||a===e.activeOptionIndex?{...e,searchQuery:r}:{...e,searchQuery:r,activeOptionIndex:a,activationTrigger:1}},[4](e){return e.dataRef.current.disabled||e.listboxState===1||e.searchQuery===""?e:{...e,searchQuery:""}},[5]:(e,n)=>{let t={id:n.id,dataRef:n.dataRef},o=gi(e,r=>[...r,t]);return e.activeOptionIndex===null&&e.dataRef.current.isSelected(n.dataRef.current.value)&&(o.activeOptionIndex=o.options.indexOf(t)),{...e,...o}},[6]:(e,n)=>{let t=gi(e,o=>{let r=o.findIndex(i=>i.id===n.id);return r!==-1&&o.splice(r,1),o});return{...e,...t,activationTrigger:1}},[7]:(e,n)=>e.buttonElement===n.element?e:{...e,buttonElement:n.element},[8]:(e,n)=>e.optionsElement===n.element?e:{...e,optionsElement:n.element}},yi=(0,Q.createContext)(null);yi.displayName="ListboxActionsContext";function sr(e){let n=(0,Q.useContext)(yi);if(n===null){let t=new Error(`<${e} /> is missing a parent <Listbox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,sr),t}return n}var lr=(0,Q.createContext)(null);lr.displayName="ListboxDataContext";function oo(e){let n=(0,Q.useContext)(lr);if(n===null){let t=new Error(`<${e} /> is missing a parent <Listbox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,oo),t}return n}function cd(e,n){return J(n.type,ud,e,n)}var fd=Q.Fragment;function dd(e,n){var ro;let t=Te(),{value:o,defaultValue:r,form:i,name:s,onChange:a,by:l,invalid:c=!1,disabled:u=t||!1,horizontal:p=!1,multiple:d=!1,__demoMode:f=!1,...m}=e,T=p?"horizontal":"vertical",b=K(n),g=ut(r),[y=d?[]:void 0,v]=at(o,a,g),[x,h]=(0,Q.useReducer)(cd,{dataRef:(0,Q.createRef)(),listboxState:f?0:1,options:[],searchQuery:"",activeOptionIndex:null,activationTrigger:1,optionsVisible:!1,buttonElement:null,optionsElement:null,__demoMode:f}),L=(0,Q.useRef)({static:!1,hold:!1}),D=(0,Q.useRef)(new Map),S=fn(l),_=(0,Q.useCallback)(me=>J(C.mode,{[1]:()=>y.some(Ae=>S(Ae,me)),[0]:()=>S(y,me)}),[y]),C=(0,Q.useMemo)(()=>({...x,value:y,disabled:u,invalid:c,mode:d?1:0,orientation:T,compare:S,isSelected:_,optionsPropsRef:L,listRef:D}),[y,u,c,d,x,D]);N(()=>{x.dataRef.current=C},[C]);let F=C.listboxState===0;pt(F,[C.buttonElement,C.optionsElement],(me,Ae)=>{var Y;h({type:1}),St(Ae,1)||(me.preventDefault(),(Y=C.buttonElement)==null||Y.focus())});let R=(0,Q.useMemo)(()=>({open:C.listboxState===0,disabled:u,invalid:c,value:y}),[C,u,y,c]),W=E(me=>{let Ae=C.options.find(Y=>Y.id===me);Ae&&I(Ae.dataRef.current.value)}),j=E(()=>{if(C.activeOptionIndex!==null){let{dataRef:me,id:Ae}=C.options[C.activeOptionIndex];I(me.current.value),h({type:2,focus:4,id:Ae})}}),X=E(()=>h({type:0})),B=E(()=>h({type:1})),U=Pe(),te=E((me,Ae,Y)=>{U.dispose(),U.microTask(()=>me===4?h({type:2,focus:4,id:Ae,trigger:Y}):h({type:2,focus:me,trigger:Y}))}),w=E((me,Ae)=>(h({type:5,id:me,dataRef:Ae}),()=>h({type:6,id:me}))),I=E(me=>J(C.mode,{[0](){return v==null?void 0:v(me)},[1](){let Ae=C.value.slice(),Y=Ae.findIndex(ge=>S(ge,me));return Y===-1?Ae.push(me):Ae.splice(Y,1),v==null?void 0:v(Ae)}})),P=E(me=>h({type:3,value:me})),O=E(()=>h({type:4})),k=E(me=>{h({type:7,element:me})}),A=E(me=>{h({type:8,element:me})}),$=(0,Q.useMemo)(()=>({onChange:I,registerOption:w,goToOption:te,closeListbox:B,openListbox:X,selectActiveOption:j,selectOption:W,search:P,clearSearch:O,setButtonElement:k,setOptionsElement:A}),[]),[G,ie]=ke({inherit:!0}),ce={ref:b},Se=(0,Q.useCallback)(()=>{if(g!==void 0)return v==null?void 0:v(g)},[v,g]),it=H();return Q.default.createElement(ie,{value:G,props:{htmlFor:(ro=C.buttonElement)==null?void 0:ro.id},slot:{open:C.listboxState===0,disabled:u}},Q.default.createElement(Wt,null,Q.default.createElement(yi.Provider,{value:$},Q.default.createElement(lr.Provider,{value:C},Q.default.createElement(Ye,{value:J(C.listboxState,{[0]:1,[1]:2})},s!=null&&y!=null&&Q.default.createElement(ct,{disabled:u,data:{[s]:y},form:i,onReset:Se}),it({ourProps:ce,theirProps:m,slot:R,defaultTag:fd,name:"Listbox"}))))))}var pd="button";function md(e,n){var F;let t=oo("Listbox.Button"),o=sr("Listbox.Button"),r=(0,q.useId)(),i=Me(),{id:s=i||`headlessui-listbox-button-${r}`,disabled:a=t.disabled||!1,autoFocus:l=!1,...c}=e,u=K(n,Ut(),o.setButtonElement),p=jo(),d=E(R=>{switch(R.key){case"Enter":wt(R.currentTarget);break;case" ":case"ArrowDown":R.preventDefault(),(0,Kt.flushSync)(()=>o.openListbox()),t.value||o.goToOption(0);break;case"ArrowUp":R.preventDefault(),(0,Kt.flushSync)(()=>o.openListbox()),t.value||o.goToOption(3);break}}),f=E(R=>{switch(R.key){case" ":R.preventDefault();break}}),m=E(R=>{var W;if(Fe(R.currentTarget))return R.preventDefault();t.listboxState===0?((0,Kt.flushSync)(()=>o.closeListbox()),(W=t.buttonElement)==null||W.focus({preventScroll:!0})):(R.preventDefault(),o.openListbox())}),T=E(R=>R.preventDefault()),b=Ie([s]),g=He(),{isFocusVisible:y,focusProps:v}=le({autoFocus:l}),{isHovered:x,hoverProps:h}=ae({isDisabled:a}),{pressed:L,pressProps:D}=Re({disabled:a}),S=(0,Q.useMemo)(()=>({open:t.listboxState===0,active:L||t.listboxState===0,disabled:a,invalid:t.invalid,value:t.value,hover:x,focus:y,autofocus:l}),[t.listboxState,t.value,a,x,y,L,t.invalid,l]),_=re(p(),{ref:u,id:s,type:Be(e,t.buttonElement),"aria-haspopup":"listbox","aria-controls":(F=t.optionsElement)==null?void 0:F.id,"aria-expanded":t.listboxState===0,"aria-labelledby":b,"aria-describedby":g,disabled:a||void 0,autoFocus:l,onKeyDown:d,onKeyUp:f,onKeyPress:T,onClick:m},v,h,D);return H()({ourProps:_,theirProps:c,slot:S,defaultTag:pd,name:"Listbox.Button"})}var ca=(0,Q.createContext)(!1),Td="div",bd=3;function gd(e,n){var O,k;let t=(0,q.useId)(),{id:o=`headlessui-listbox-options-${t}`,anchor:r,portal:i=!1,modal:s=!0,transition:a=!1,...l}=e,c=Bt(r),[u,p]=(0,Q.useState)(null);c&&(i=!0);let d=oo("Listbox.Options"),f=sr("Listbox.Options"),m=Le(d.optionsElement),T=_e(),[b,g]=je(a,u,T!==null?(T&1)===1:d.listboxState===0);dt(b,d.buttonElement,f.closeListbox);let y=d.__demoMode?!1:s&&d.listboxState===0;mt(y,m);let v=d.__demoMode?!1:s&&d.listboxState===0;_t(v,{allowed:(0,Q.useCallback)(()=>[d.buttonElement,d.optionsElement],[d.buttonElement,d.optionsElement])});let x=d.listboxState!==0,L=rr(x,d.buttonElement)?!1:b,D=b&&d.listboxState===1,S=xn(D,d.value),_=E(A=>d.compare(S,A)),C=(0,Q.useMemo)(()=>{var $;if(c==null||!(($=c==null?void 0:c.to)!=null&&$.includes("selection")))return null;let A=d.options.findIndex(G=>_(G.dataRef.current.value));return A===-1&&(A=0),A},[c,d.options]),F=(()=>{if(c==null)return;if(C===null)return{...c,inner:void 0};let A=Array.from(d.listRef.current.values());return{...c,inner:{listRef:{current:A},index:C}}})(),[R,W]=Vt(F),j=Gt(),X=K(n,c?R:null,f.setOptionsElement,p),B=Pe();(0,Q.useEffect)(()=>{var $;let A=d.optionsElement;A&&d.listboxState===0&&A!==(($=he(A))==null?void 0:$.activeElement)&&(A==null||A.focus({preventScroll:!0}))},[d.listboxState,d.optionsElement]);let U=E(A=>{var $,G;switch(B.dispose(),A.key){case" ":if(d.searchQuery!=="")return A.preventDefault(),A.stopPropagation(),f.search(A.key);case"Enter":if(A.preventDefault(),A.stopPropagation(),d.activeOptionIndex!==null){let{dataRef:ie}=d.options[d.activeOptionIndex];f.onChange(ie.current.value)}d.mode===0&&((0,Kt.flushSync)(()=>f.closeListbox()),($=d.buttonElement)==null||$.focus({preventScroll:!0}));break;case J(d.orientation,{vertical:"ArrowDown",horizontal:"ArrowRight"}):return A.preventDefault(),A.stopPropagation(),f.goToOption(2);case J(d.orientation,{vertical:"ArrowUp",horizontal:"ArrowLeft"}):return A.preventDefault(),A.stopPropagation(),f.goToOption(1);case"Home":case"PageUp":return A.preventDefault(),A.stopPropagation(),f.goToOption(0);case"End":case"PageDown":return A.preventDefault(),A.stopPropagation(),f.goToOption(3);case"Escape":A.preventDefault(),A.stopPropagation(),(0,Kt.flushSync)(()=>f.closeListbox()),(G=d.buttonElement)==null||G.focus({preventScroll:!0});return;case"Tab":A.preventDefault(),A.stopPropagation(),(0,Kt.flushSync)(()=>f.closeListbox()),Io(d.buttonElement,A.shiftKey?2:4);break;default:A.key.length===1&&(f.search(A.key),B.setTimeout(()=>f.clearSearch(),350));break}}),te=(O=d.buttonElement)==null?void 0:O.id,w=(0,Q.useMemo)(()=>({open:d.listboxState===0}),[d.listboxState]),I=re(c?j():{},{id:o,ref:X,"aria-activedescendant":d.activeOptionIndex===null||(k=d.options[d.activeOptionIndex])==null?void 0:k.id,"aria-multiselectable":d.mode===1?!0:void 0,"aria-labelledby":te,"aria-orientation":d.orientation,onKeyDown:U,role:"listbox",tabIndex:d.listboxState===0?0:void 0,style:{...l.style,...W,"--button-width":Rt(d.buttonElement,!0).width},...We(g)}),P=H();return Q.default.createElement(qe,{enabled:i?e.static||b:!1},Q.default.createElement(lr.Provider,{value:d.mode===1?d:{...d,isSelected:_}},P({ourProps:I,theirProps:l,slot:w,defaultTag:Td,features:bd,visible:L,name:"Listbox.Options"})))}var yd="div";function vd(e,n){let t=(0,q.useId)(),{id:o=`headlessui-listbox-option-${t}`,disabled:r=!1,value:i,...s}=e,a=(0,Q.useContext)(ca)===!0,l=oo("Listbox.Option"),c=sr("Listbox.Option"),u=l.activeOptionIndex!==null?l.options[l.activeOptionIndex].id===o:!1,p=l.isSelected(i),d=(0,Q.useRef)(null),f=ir(d),m=de({disabled:r,value:i,domRef:d,get textValue(){return f()}}),T=K(n,d,_=>{_?l.listRef.current.set(o,_):l.listRef.current.delete(o)});N(()=>{if(!l.__demoMode&&l.listboxState===0&&u&&l.activationTrigger!==0)return xe().requestAnimationFrame(()=>{var _,C;(C=(_=d.current)==null?void 0:_.scrollIntoView)==null||C.call(_,{block:"nearest"})})},[d,u,l.__demoMode,l.listboxState,l.activationTrigger,l.activeOptionIndex]),N(()=>{if(!a)return c.registerOption(o,m)},[m,o,a]);let b=E(_=>{var C;if(r)return _.preventDefault();c.onChange(i),l.mode===0&&((0,Kt.flushSync)(()=>c.closeListbox()),(C=l.buttonElement)==null||C.focus({preventScroll:!0}))}),g=E(()=>{if(r)return c.goToOption(5);c.goToOption(4,o)}),y=mn(),v=E(_=>{y.update(_),!r&&(u||c.goToOption(4,o,0))}),x=E(_=>{y.wasMoved(_)&&(r||u||c.goToOption(4,o,0))}),h=E(_=>{y.wasMoved(_)&&(r||u&&c.goToOption(5))}),L=(0,Q.useMemo)(()=>({active:u,focus:u,selected:p,disabled:r,selectedOption:p&&a}),[u,p,r,a]),D=a?{}:{id:o,ref:T,role:"option",tabIndex:r===!0?void 0:-1,"aria-disabled":r===!0?!0:void 0,"aria-selected":p,disabled:void 0,onClick:b,onFocus:g,onPointerEnter:v,onMouseEnter:v,onPointerMove:x,onMouseMove:x,onPointerLeave:h,onMouseLeave:h},S=H();return!p&&a?null:S({ourProps:D,theirProps:s,slot:L,defaultTag:yd,name:"Listbox.Option"})}var Ed=Q.Fragment;function hd(e,n){let{options:t,placeholder:o,...r}=e,s={ref:K(n)},a=oo("ListboxSelectedOption"),l=(0,Q.useMemo)(()=>({}),[]),c=a.value===void 0||a.value===null||a.mode===1&&Array.isArray(a.value)&&a.value.length===0,u=H();return Q.default.createElement(ca.Provider,{value:!0},u({ourProps:s,theirProps:{...r,children:Q.default.createElement(Q.default.Fragment,null,o&&c?o:t)},slot:l,defaultTag:Ed,name:"ListboxSelectedOption"}))}var xd=M(dd),fa=M(md),da=Xe,pa=M(gd),ma=M(vd),Ta=M(hd),Pd=Object.assign(xd,{Button:fa,Label:da,Options:pa,Option:ma,SelectedOption:Ta});var ue=oe(require("react"),1),Ln=require("react-dom");function vi(e,n=t=>t){let t=e.activeItemIndex!==null?e.items[e.activeItemIndex]:null,o=$e(n(e.items.slice()),i=>i.dataRef.current.domRef.current),r=t?o.indexOf(t):null;return r===-1&&(r=null),{items:o,activeItemIndex:r}}var Rd={[1](e){return e.menuState===1?e:{...e,activeItemIndex:null,menuState:1}},[0](e){return e.menuState===0?e:{...e,__demoMode:!1,menuState:0}},[2]:(e,n)=>{var i,s,a,l,c;if(e.menuState===1)return e;let t={...e,searchQuery:"",activationTrigger:(i=n.trigger)!=null?i:1,__demoMode:!1};if(n.focus===5)return{...t,activeItemIndex:null};if(n.focus===4)return{...t,activeItemIndex:e.items.findIndex(u=>u.id===n.id)};if(n.focus===1){let u=e.activeItemIndex;if(u!==null){let p=e.items[u].dataRef.current.domRef,d=rt(n,{resolveItems:()=>e.items,resolveActiveIndex:()=>e.activeItemIndex,resolveId:f=>f.id,resolveDisabled:f=>f.dataRef.current.disabled});if(d!==null){let f=e.items[d].dataRef.current.domRef;if(((s=p.current)==null?void 0:s.previousElementSibling)===f.current||((a=f.current)==null?void 0:a.previousElementSibling)===null)return{...t,activeItemIndex:d}}}}else if(n.focus===2){let u=e.activeItemIndex;if(u!==null){let p=e.items[u].dataRef.current.domRef,d=rt(n,{resolveItems:()=>e.items,resolveActiveIndex:()=>e.activeItemIndex,resolveId:f=>f.id,resolveDisabled:f=>f.dataRef.current.disabled});if(d!==null){let f=e.items[d].dataRef.current.domRef;if(((l=p.current)==null?void 0:l.nextElementSibling)===f.current||((c=f.current)==null?void 0:c.nextElementSibling)===null)return{...t,activeItemIndex:d}}}}let o=vi(e),r=rt(n,{resolveItems:()=>o.items,resolveActiveIndex:()=>o.activeItemIndex,resolveId:u=>u.id,resolveDisabled:u=>u.dataRef.current.disabled});return{...t,...o,activeItemIndex:r}},[3]:(e,n)=>{let o=e.searchQuery!==""?0:1,r=e.searchQuery+n.value.toLowerCase(),s=(e.activeItemIndex!==null?e.items.slice(e.activeItemIndex+o).concat(e.items.slice(0,e.activeItemIndex+o)):e.items).find(l=>{var c;return((c=l.dataRef.current.textValue)==null?void 0:c.startsWith(r))&&!l.dataRef.current.disabled}),a=s?e.items.indexOf(s):-1;return a===-1||a===e.activeItemIndex?{...e,searchQuery:r}:{...e,searchQuery:r,activeItemIndex:a,activationTrigger:1}},[4](e){return e.searchQuery===""?e:{...e,searchQuery:"",searchActiveItemIndex:null}},[5]:(e,n)=>{let t=vi(e,o=>[...o,{id:n.id,dataRef:n.dataRef}]);return{...e,...t}},[6]:(e,n)=>{let t=vi(e,o=>{let r=o.findIndex(i=>i.id===n.id);return r!==-1&&o.splice(r,1),o});return{...e,...t,activationTrigger:1}},[7]:(e,n)=>e.buttonElement===n.element?e:{...e,buttonElement:n.element},[8]:(e,n)=>e.itemsElement===n.element?e:{...e,itemsElement:n.element}},Ei=(0,ue.createContext)(null);Ei.displayName="MenuContext";function ar(e){let n=(0,ue.useContext)(Ei);if(n===null){let t=new Error(`<${e} /> is missing a parent <Menu /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,ar),t}return n}function Sd(e,n){return J(n.type,Rd,e,n)}var Cd=ue.Fragment;function Ad(e,n){let{__demoMode:t=!1,...o}=e,r=(0,ue.useReducer)(Sd,{__demoMode:t,menuState:t?0:1,buttonElement:null,itemsElement:null,items:[],searchQuery:"",activeItemIndex:null,activationTrigger:1}),[{menuState:i,itemsElement:s,buttonElement:a},l]=r,c=K(n);pt(i===0,[a,s],(T,b)=>{l({type:1}),St(b,1)||(T.preventDefault(),a==null||a.focus())});let p=E(()=>{l({type:1})}),d=(0,ue.useMemo)(()=>({open:i===0,close:p}),[i,p]),f={ref:c},m=H();return ue.default.createElement(Wt,null,ue.default.createElement(Ei.Provider,{value:r},ue.default.createElement(Ye,{value:J(i,{[0]:1,[1]:2})},m({ourProps:f,theirProps:o,slot:d,defaultTag:Cd,name:"Menu"}))))}var Ld="button";function Od(e,n){var D;let t=(0,q.useId)(),{id:o=`headlessui-menu-button-${t}`,disabled:r=!1,autoFocus:i=!1,...s}=e,[a,l]=ar("Menu.Button"),c=jo(),u=K(n,Ut(),E(S=>l({type:7,element:S}))),p=E(S=>{switch(S.key){case" ":case"Enter":case"ArrowDown":S.preventDefault(),S.stopPropagation(),(0,Ln.flushSync)(()=>l({type:0})),l({type:2,focus:0});break;case"ArrowUp":S.preventDefault(),S.stopPropagation(),(0,Ln.flushSync)(()=>l({type:0})),l({type:2,focus:3});break}}),d=E(S=>{switch(S.key){case" ":S.preventDefault();break}}),f=E(S=>{var _;if(Fe(S.currentTarget))return S.preventDefault();r||(a.menuState===0?((0,Ln.flushSync)(()=>l({type:1})),(_=a.buttonElement)==null||_.focus({preventScroll:!0})):(S.preventDefault(),l({type:0})))}),{isFocusVisible:m,focusProps:T}=le({autoFocus:i}),{isHovered:b,hoverProps:g}=ae({isDisabled:r}),{pressed:y,pressProps:v}=Re({disabled:r}),x=(0,ue.useMemo)(()=>({open:a.menuState===0,active:y||a.menuState===0,disabled:r,hover:b,focus:m,autofocus:i}),[a,b,m,y,r,i]),h=re(c(),{ref:u,id:o,type:Be(e,a.buttonElement),"aria-haspopup":"menu","aria-controls":(D=a.itemsElement)==null?void 0:D.id,"aria-expanded":a.menuState===0,disabled:r||void 0,autoFocus:i,onKeyDown:p,onKeyUp:d,onClick:f},T,g,v);return H()({ourProps:h,theirProps:s,slot:x,defaultTag:Ld,name:"Menu.Button"})}var Dd="div",Id=3;function Md(e,n){var U,te;let t=(0,q.useId)(),{id:o=`headlessui-menu-items-${t}`,anchor:r,portal:i=!1,modal:s=!0,transition:a=!1,...l}=e,c=Bt(r),[u,p]=ar("Menu.Items"),[d,f]=Vt(c),m=Gt(),[T,b]=(0,ue.useState)(null),g=K(n,c?d:null,E(w=>p({type:8,element:w})),b),y=Le(u.itemsElement);c&&(i=!0);let v=_e(),[x,h]=je(a,T,v!==null?(v&1)===1:u.menuState===0);dt(x,u.buttonElement,()=>{p({type:1})});let L=u.__demoMode?!1:s&&u.menuState===0;mt(L,y);let D=u.__demoMode?!1:s&&u.menuState===0;_t(D,{allowed:(0,ue.useCallback)(()=>[u.buttonElement,u.itemsElement],[u.buttonElement,u.itemsElement])});let S=u.menuState!==0,C=rr(S,u.buttonElement)?!1:x;(0,ue.useEffect)(()=>{let w=u.itemsElement;w&&u.menuState===0&&w!==(y==null?void 0:y.activeElement)&&w.focus({preventScroll:!0})},[u.menuState,u.itemsElement,y]),wo(u.menuState===0,{container:u.itemsElement,accept(w){return w.getAttribute("role")==="menuitem"?NodeFilter.FILTER_REJECT:w.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(w){w.setAttribute("role","none")}});let F=Pe(),R=E(w=>{var I,P,O;switch(F.dispose(),w.key){case" ":if(u.searchQuery!=="")return w.preventDefault(),w.stopPropagation(),p({type:3,value:w.key});case"Enter":if(w.preventDefault(),w.stopPropagation(),p({type:1}),u.activeItemIndex!==null){let{dataRef:k}=u.items[u.activeItemIndex];(P=(I=k.current)==null?void 0:I.domRef.current)==null||P.click()}wr(u.buttonElement);break;case"ArrowDown":return w.preventDefault(),w.stopPropagation(),p({type:2,focus:2});case"ArrowUp":return w.preventDefault(),w.stopPropagation(),p({type:2,focus:1});case"Home":case"PageUp":return w.preventDefault(),w.stopPropagation(),p({type:2,focus:0});case"End":case"PageDown":return w.preventDefault(),w.stopPropagation(),p({type:2,focus:3});case"Escape":w.preventDefault(),w.stopPropagation(),(0,Ln.flushSync)(()=>p({type:1})),(O=u.buttonElement)==null||O.focus({preventScroll:!0});break;case"Tab":w.preventDefault(),w.stopPropagation(),(0,Ln.flushSync)(()=>p({type:1})),Io(u.buttonElement,w.shiftKey?2:4);break;default:w.key.length===1&&(p({type:3,value:w.key}),F.setTimeout(()=>p({type:4}),350));break}}),W=E(w=>{switch(w.key){case" ":w.preventDefault();break}}),j=(0,ue.useMemo)(()=>({open:u.menuState===0}),[u.menuState]),X=re(c?m():{},{"aria-activedescendant":u.activeItemIndex===null||(U=u.items[u.activeItemIndex])==null?void 0:U.id,"aria-labelledby":(te=u.buttonElement)==null?void 0:te.id,id:o,onKeyDown:R,onKeyUp:W,role:"menu",tabIndex:u.menuState===0?0:void 0,ref:g,style:{...l.style,...f,"--button-width":Rt(u.buttonElement,!0).width},...We(h)}),B=H();return ue.default.createElement(qe,{enabled:i?e.static||x:!1},B({ourProps:X,theirProps:l,slot:j,defaultTag:Dd,features:Id,visible:C,name:"Menu.Items"}))}var Fd=ue.Fragment;function wd(e,n){let t=(0,q.useId)(),{id:o=`headlessui-menu-item-${t}`,disabled:r=!1,...i}=e,[s,a]=ar("Menu.Item"),l=s.activeItemIndex!==null?s.items[s.activeItemIndex].id===o:!1,c=(0,ue.useRef)(null),u=K(n,c);N(()=>{if(!s.__demoMode&&s.menuState===0&&l&&s.activationTrigger!==0)return xe().requestAnimationFrame(()=>{var F,R;(R=(F=c.current)==null?void 0:F.scrollIntoView)==null||R.call(F,{block:"nearest"})})},[s.__demoMode,c,l,s.menuState,s.activationTrigger,s.activeItemIndex]);let p=ir(c),d=(0,ue.useRef)({disabled:r,domRef:c,get textValue(){return p()}});N(()=>{d.current.disabled=r},[d,r]),N(()=>(a({type:5,id:o,dataRef:d}),()=>a({type:6,id:o})),[d,o]);let f=E(()=>{a({type:1})}),m=E(F=>{if(r)return F.preventDefault();a({type:1}),wr(s.buttonElement)}),T=E(()=>{if(r)return a({type:2,focus:5});a({type:2,focus:4,id:o})}),b=mn(),g=E(F=>{b.update(F),!r&&(l||a({type:2,focus:4,id:o,trigger:0}))}),y=E(F=>{b.wasMoved(F)&&(r||l||a({type:2,focus:4,id:o,trigger:0}))}),v=E(F=>{b.wasMoved(F)&&(r||l&&a({type:2,focus:5}))}),[x,h]=ke(),[L,D]=et(),S=(0,ue.useMemo)(()=>({active:l,focus:l,disabled:r,close:f}),[l,r,f]),_={id:o,ref:u,role:"menuitem",tabIndex:r===!0?void 0:-1,"aria-disabled":r===!0?!0:void 0,"aria-labelledby":x,"aria-describedby":L,disabled:void 0,onClick:m,onFocus:T,onPointerEnter:g,onMouseEnter:g,onPointerMove:y,onMouseMove:y,onPointerLeave:v,onMouseLeave:v},C=H();return ue.default.createElement(h,null,ue.default.createElement(D,null,C({ourProps:_,theirProps:i,slot:S,defaultTag:Fd,name:"Menu.Item"})))}var _d="div";function Hd(e,n){let[t,o]=ke(),r=e,i={ref:n,"aria-labelledby":t,role:"group"},s=H();return ue.default.createElement(o,null,s({ourProps:i,theirProps:r,slot:{},defaultTag:_d,name:"Menu.Section"}))}var kd="header";function $d(e,n){let t=(0,q.useId)(),{id:o=`headlessui-menu-heading-${t}`,...r}=e,i=Po();N(()=>i.register(o),[o,i.register]);let s={id:o,ref:n,role:"presentation",...i.props};return H()({ourProps:s,theirProps:r,slot:{},defaultTag:kd,name:"Menu.Heading"})}var Nd="div";function Bd(e,n){let t=e,o={ref:n,role:"separator"};return H()({ourProps:o,theirProps:t,slot:{},defaultTag:Nd,name:"Menu.Separator"})}var Ud=M(Ad),ba=M(Od),ga=M(Md),ya=M(wd),va=M(Hd),Ea=M($d),ha=M(Bd),Gd=Object.assign(Ud,{Button:ba,Items:ga,Item:ya,Section:va,Heading:Ea,Separator:ha});var V=oe(require("react"),1);var Vd={[0]:e=>({...e,popoverState:J(e.popoverState,{[0]:1,[1]:0}),__demoMode:!1}),[1](e){return e.popoverState===1?e:{...e,popoverState:1,__demoMode:!1}},[2](e,n){return e.button===n.button?e:{...e,button:n.button}},[3](e,n){return e.buttonId===n.buttonId?e:{...e,buttonId:n.buttonId}},[4](e,n){return e.panel===n.panel?e:{...e,panel:n.panel}},[5](e,n){return e.panelId===n.panelId?e:{...e,panelId:n.panelId}}},hi=(0,V.createContext)(null);hi.displayName="PopoverContext";function ur(e){let n=(0,V.useContext)(hi);if(n===null){let t=new Error(`<${e} /> is missing a parent <Popover /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,ur),t}return n}var cr=(0,V.createContext)(null);cr.displayName="PopoverAPIContext";function xi(e){let n=(0,V.useContext)(cr);if(n===null){let t=new Error(`<${e} /> is missing a parent <Popover /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,xi),t}return n}var Pi=(0,V.createContext)(null);Pi.displayName="PopoverGroupContext";function xa(){return(0,V.useContext)(Pi)}var fr=(0,V.createContext)(null);fr.displayName="PopoverPanelContext";function Wd(){return(0,V.useContext)(fr)}function jd(e,n){return J(n.type,Vd,e,n)}var Kd="div";function zd(e,n){var w;let{__demoMode:t=!1,...o}=e,r=(0,V.useRef)(null),i=K(n,ln(I=>{r.current=I})),s=(0,V.useRef)([]),a=(0,V.useReducer)(jd,{__demoMode:t,popoverState:t?0:1,buttons:s,button:null,buttonId:null,panel:null,panelId:null,beforePanelSentinel:(0,V.createRef)(),afterPanelSentinel:(0,V.createRef)(),afterButtonSentinel:(0,V.createRef)()}),[{popoverState:l,button:c,buttonId:u,panel:p,panelId:d,beforePanelSentinel:f,afterPanelSentinel:m,afterButtonSentinel:T},b]=a,g=Le((w=r.current)!=null?w:c),y=(0,V.useMemo)(()=>{if(!c||!p)return!1;for(let G of document.querySelectorAll("body > *"))if(Number(G==null?void 0:G.contains(c))^Number(G==null?void 0:G.contains(p)))return!0;let I=dn(),P=I.indexOf(c),O=(P+I.length-1)%I.length,k=(P+1)%I.length,A=I[O],$=I[k];return!p.contains(A)&&!p.contains($)},[c,p]),v=de(u),x=de(d),h=(0,V.useMemo)(()=>({buttonId:v,panelId:x,close:()=>b({type:1})}),[v,x,b]),L=xa(),D=L==null?void 0:L.registerPopover,S=E(()=>{var I;return(I=L==null?void 0:L.isFocusWithinPopoverGroup())!=null?I:(g==null?void 0:g.activeElement)&&((c==null?void 0:c.contains(g.activeElement))||(p==null?void 0:p.contains(g.activeElement)))});(0,V.useEffect)(()=>D==null?void 0:D(h),[D,h]);let[_,C]=Yo(),F=Qn(c),R=qo({mainTreeNode:F,portals:_,defaultContainers:[c,p]});Ht(g==null?void 0:g.defaultView,"focus",I=>{var P,O,k,A,$,G;I.target!==window&&I.target instanceof HTMLElement&&l===0&&(S()||c&&p&&(R.contains(I.target)||(O=(P=f.current)==null?void 0:P.contains)!=null&&O.call(P,I.target)||(A=(k=m.current)==null?void 0:k.contains)!=null&&A.call(k,I.target)||(G=($=T.current)==null?void 0:$.contains)!=null&&G.call($,I.target)||b({type:1})))},!0),pt(l===0,R.resolveContainers,(I,P)=>{b({type:1}),St(P,1)||(I.preventDefault(),c==null||c.focus())});let j=E(I=>{b({type:1});let P=(()=>I?I instanceof HTMLElement?I:"current"in I&&I.current instanceof HTMLElement?I.current:c:c)();P==null||P.focus()}),X=(0,V.useMemo)(()=>({close:j,isPortalled:y}),[j,y]),B=(0,V.useMemo)(()=>({open:l===0,close:j}),[l,j]),U={ref:i},te=H();return V.default.createElement(An,{node:F},V.default.createElement(Wt,null,V.default.createElement(fr.Provider,{value:null},V.default.createElement(hi.Provider,{value:a},V.default.createElement(cr.Provider,{value:X},V.default.createElement(un,{value:j},V.default.createElement(Ye,{value:J(l,{[0]:1,[1]:2})},V.default.createElement(C,null,te({ourProps:U,theirProps:o,slot:B,defaultTag:Kd,name:"Popover"})))))))))}var Xd="button";function Yd(e,n){let t=(0,q.useId)(),{id:o=`headlessui-popover-button-${t}`,disabled:r=!1,autoFocus:i=!1,...s}=e,[a,l]=ur("Popover.Button"),{isPortalled:c}=xi("Popover.Button"),u=(0,V.useRef)(null),p=`headlessui-focus-sentinel-${(0,q.useId)()}`,d=xa(),f=d==null?void 0:d.closeOthers,T=Wd()!==null;(0,V.useEffect)(()=>{if(!T)return l({type:3,buttonId:o}),()=>{l({type:3,buttonId:null})}},[T,o,l]);let[b]=(0,V.useState)(()=>Symbol()),g=K(u,n,Ut(),E(P=>{if(!T){if(P)a.buttons.current.push(b);else{let O=a.buttons.current.indexOf(b);O!==-1&&a.buttons.current.splice(O,1)}a.buttons.current.length>1&&console.warn("You are already using a <Popover.Button /> but only 1 <Popover.Button /> is supported."),P&&l({type:2,button:P})}})),y=K(u,n),v=Le(u),x=E(P=>{var O,k,A;if(T){if(a.popoverState===1)return;switch(P.key){case" ":case"Enter":P.preventDefault(),(k=(O=P.target).click)==null||k.call(O),l({type:1}),(A=a.button)==null||A.focus();break}}else switch(P.key){case" ":case"Enter":P.preventDefault(),P.stopPropagation(),a.popoverState===1&&(f==null||f(a.buttonId)),l({type:0});break;case"Escape":if(a.popoverState!==0)return f==null?void 0:f(a.buttonId);if(!u.current||v!=null&&v.activeElement&&!u.current.contains(v.activeElement))return;P.preventDefault(),P.stopPropagation(),l({type:1});break}}),h=E(P=>{T||P.key===" "&&P.preventDefault()}),L=E(P=>{var O,k;Fe(P.currentTarget)||r||(T?(l({type:1}),(O=a.button)==null||O.focus()):(P.preventDefault(),P.stopPropagation(),a.popoverState===1&&(f==null||f(a.buttonId)),l({type:0}),(k=a.button)==null||k.focus()))}),D=E(P=>{P.preventDefault(),P.stopPropagation()}),{isFocusVisible:S,focusProps:_}=le({autoFocus:i}),{isHovered:C,hoverProps:F}=ae({isDisabled:r}),{pressed:R,pressProps:W}=Re({disabled:r}),j=a.popoverState===0,X=(0,V.useMemo)(()=>({open:j,active:R||j,disabled:r,hover:C,focus:S,autofocus:i}),[j,C,S,R,r,i]),B=Be(e,a.button),U=T?re({ref:y,type:B,onKeyDown:x,onClick:L,disabled:r||void 0,autoFocus:i},_,F,W):re({ref:g,id:a.buttonId,type:B,"aria-expanded":a.popoverState===0,"aria-controls":a.panel?a.panelId:void 0,disabled:r||void 0,autoFocus:i,onKeyDown:x,onKeyUp:h,onClick:L,onMouseDown:D},_,F,W),te=Zn(),w=E(()=>{let P=a.panel;if(!P)return;function O(){J(te.current,{[0]:()=>ye(P,1),[1]:()=>ye(P,8)})===0&&ye(dn().filter(A=>A.dataset.headlessuiFocusGuard!=="true"),J(te.current,{[0]:4,[1]:2}),{relativeTo:a.button})}O()}),I=H();return V.default.createElement(V.default.Fragment,null,I({ourProps:U,theirProps:s,slot:X,defaultTag:Xd,name:"Popover.Button"}),j&&!T&&c&&V.default.createElement(we,{id:p,ref:a.afterButtonSentinel,features:2,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:w}))}var Jd="div",qd=3;function Pa(e,n){let t=(0,q.useId)(),{id:o=`headlessui-popover-backdrop-${t}`,transition:r=!1,...i}=e,[{popoverState:s},a]=ur("Popover.Backdrop"),[l,c]=(0,V.useState)(null),u=K(n,c),p=_e(),[d,f]=je(r,l,p!==null?(p&1)===1:s===0),m=E(y=>{if(Fe(y.currentTarget))return y.preventDefault();a({type:1})}),T=(0,V.useMemo)(()=>({open:s===0}),[s]),b={ref:u,id:o,"aria-hidden":!0,onClick:m,...We(f)};return H()({ourProps:b,theirProps:i,slot:T,defaultTag:Jd,features:qd,visible:d,name:"Popover.Backdrop"})}var Qd="div",Zd=3;function ep(e,n){let t=(0,q.useId)(),{id:o=`headlessui-popover-panel-${t}`,focus:r=!1,anchor:i,portal:s=!1,modal:a=!1,transition:l=!1,...c}=e,[u,p]=ur("Popover.Panel"),{close:d,isPortalled:f}=xi("Popover.Panel"),m=`headlessui-focus-sentinel-before-${t}`,T=`headlessui-focus-sentinel-after-${t}`,b=(0,V.useRef)(null),g=Bt(i),[y,v]=Vt(g),x=Gt();g&&(s=!0);let[h,L]=(0,V.useState)(null),D=K(b,n,g?y:null,E(I=>p({type:4,panel:I})),L),S=Le(b);N(()=>(p({type:5,panelId:o}),()=>{p({type:5,panelId:null})}),[o,p]);let _=_e(),[C,F]=je(l,h,_!==null?(_&1)===1:u.popoverState===0);dt(C,u.button,()=>{p({type:1})});let R=u.__demoMode?!1:a&&C;mt(R,S);let W=E(I=>{var P;switch(I.key){case"Escape":if(u.popoverState!==0||!b.current||S!=null&&S.activeElement&&!b.current.contains(S.activeElement))return;I.preventDefault(),I.stopPropagation(),p({type:1}),(P=u.button)==null||P.focus();break}});(0,V.useEffect)(()=>{var I;e.static||u.popoverState===1&&((I=e.unmount)==null||I)&&p({type:4,panel:null})},[u.popoverState,e.unmount,e.static,p]),(0,V.useEffect)(()=>{if(u.__demoMode||!r||u.popoverState!==0||!b.current)return;let I=S==null?void 0:S.activeElement;b.current.contains(I)||ye(b.current,1)},[u.__demoMode,r,b.current,u.popoverState]);let j=(0,V.useMemo)(()=>({open:u.popoverState===0,close:d}),[u.popoverState,d]),X=re(g?x():{},{ref:D,id:o,onKeyDown:W,onBlur:r&&u.popoverState===0?I=>{var O,k,A,$,G;let P=I.relatedTarget;P&&b.current&&((O=b.current)!=null&&O.contains(P)||(p({type:1}),((A=(k=u.beforePanelSentinel.current)==null?void 0:k.contains)!=null&&A.call(k,P)||(G=($=u.afterPanelSentinel.current)==null?void 0:$.contains)!=null&&G.call($,P))&&P.focus({preventScroll:!0})))}:void 0,tabIndex:-1,style:{...c.style,...v,"--button-width":Rt(u.button,!0).width},...We(F)}),B=Zn(),U=E(()=>{let I=b.current;if(!I)return;function P(){J(B.current,{[0]:()=>{var k;ye(I,1)===0&&((k=u.afterPanelSentinel.current)==null||k.focus())},[1]:()=>{var O;(O=u.button)==null||O.focus({preventScroll:!0})}})}P()}),te=E(()=>{let I=b.current;if(!I)return;function P(){J(B.current,{[0]:()=>{if(!u.button)return;let O=dn(),k=O.indexOf(u.button),A=O.slice(0,k+1),G=[...O.slice(k+1),...A];for(let ie of G.slice())if(ie.dataset.headlessuiFocusGuard==="true"||h!=null&&h.contains(ie)){let ce=G.indexOf(ie);ce!==-1&&G.splice(ce,1)}ye(G,1,{sorted:!1})},[1]:()=>{var k;ye(I,2)===0&&((k=u.button)==null||k.focus())}})}P()}),w=H();return V.default.createElement(Pn,null,V.default.createElement(fr.Provider,{value:o},V.default.createElement(cr.Provider,{value:{close:d,isPortalled:f}},V.default.createElement(qe,{enabled:s?e.static||C:!1},C&&f&&V.default.createElement(we,{id:m,ref:u.beforePanelSentinel,features:2,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:U}),w({ourProps:X,theirProps:c,slot:j,defaultTag:Qd,features:Zd,visible:C,name:"Popover.Panel"}),C&&f&&V.default.createElement(we,{id:T,ref:u.afterPanelSentinel,features:2,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:te})))))}var tp="div";function np(e,n){let t=(0,V.useRef)(null),o=K(t,n),[r,i]=(0,V.useState)([]),s=E(T=>{i(b=>{let g=b.indexOf(T);if(g!==-1){let y=b.slice();return y.splice(g,1),y}return b})}),a=E(T=>(i(b=>[...b,T]),()=>s(T))),l=E(()=>{var g;let T=he(t);if(!T)return!1;let b=T.activeElement;return(g=t.current)!=null&&g.contains(b)?!0:r.some(y=>{var v,x;return((v=T.getElementById(y.buttonId.current))==null?void 0:v.contains(b))||((x=T.getElementById(y.panelId.current))==null?void 0:x.contains(b))})}),c=E(T=>{for(let b of r)b.buttonId.current!==T&&b.close()}),u=(0,V.useMemo)(()=>({registerPopover:a,unregisterPopover:s,isFocusWithinPopoverGroup:l,closeOthers:c}),[a,s,l,c]),p=(0,V.useMemo)(()=>({}),[]),d=e,f={ref:o},m=H();return V.default.createElement(An,null,V.default.createElement(Pi.Provider,{value:u},m({ourProps:f,theirProps:d,slot:p,defaultTag:tp,name:"Popover.Group"})))}var op=M(zd),Ra=M(Yd),Sa=M(Pa),Ca=M(Pa),Aa=M(ep),La=M(np),rp=Object.assign(op,{Button:Ra,Backdrop:Ca,Overlay:Sa,Panel:Aa,Group:La});var fe=oe(require("react"),1);var ip={[0](e,n){let t=[...e.options,{id:n.id,element:n.element,propsRef:n.propsRef}];return{...e,options:$e(t,o=>o.element.current)}},[1](e,n){let t=e.options.slice(),o=e.options.findIndex(r=>r.id===n.id);return o===-1?e:(t.splice(o,1),{...e,options:t})}},Ri=(0,fe.createContext)(null);Ri.displayName="RadioGroupDataContext";function Si(e){let n=(0,fe.useContext)(Ri);if(n===null){let t=new Error(`<${e} /> is missing a parent <RadioGroup /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,Si),t}return n}var Ci=(0,fe.createContext)(null);Ci.displayName="RadioGroupActionsContext";function Ai(e){let n=(0,fe.useContext)(Ci);if(n===null){let t=new Error(`<${e} /> is missing a parent <RadioGroup /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,Ai),t}return n}function sp(e,n){return J(n.type,ip,e,n)}var lp="div";function ap(e,n){let t=(0,q.useId)(),o=Te(),{id:r=`headlessui-radiogroup-${t}`,value:i,form:s,name:a,onChange:l,by:c,disabled:u=o||!1,defaultValue:p,...d}=e,f=fn(c),[m,T]=(0,fe.useReducer)(sp,{options:[]}),b=m.options,[g,y]=ke(),[v,x]=et(),h=(0,fe.useRef)(null),L=K(h,n),D=ut(p),[S,_]=at(i,l,D),C=(0,fe.useMemo)(()=>b.find(P=>!P.propsRef.current.disabled),[b]),F=(0,fe.useMemo)(()=>b.some(P=>f(P.propsRef.current.value,S)),[b,S]),R=E(P=>{var k;if(u||f(P,S))return!1;let O=(k=b.find(A=>f(A.propsRef.current.value,P)))==null?void 0:k.propsRef.current;return O!=null&&O.disabled?!1:(_==null||_(P),!0)}),W=E(P=>{let O=h.current;if(!O)return;let k=he(O),A=b.filter($=>$.propsRef.current.disabled===!1).map($=>$.element.current);switch(P.key){case"Enter":wt(P.currentTarget);break;case"ArrowLeft":case"ArrowUp":if(P.preventDefault(),P.stopPropagation(),ye(A,18)===2){let G=b.find(ie=>ie.element.current===(k==null?void 0:k.activeElement));G&&R(G.propsRef.current.value)}break;case"ArrowRight":case"ArrowDown":if(P.preventDefault(),P.stopPropagation(),ye(A,20)===2){let G=b.find(ie=>ie.element.current===(k==null?void 0:k.activeElement));G&&R(G.propsRef.current.value)}break;case" ":{P.preventDefault(),P.stopPropagation();let $=b.find(G=>G.element.current===(k==null?void 0:k.activeElement));$&&R($.propsRef.current.value)}break}}),j=E(P=>(T({type:0,...P}),()=>T({type:1,id:P.id}))),X=(0,fe.useMemo)(()=>({value:S,firstOption:C,containsCheckedOption:F,disabled:u,compare:f,...m}),[S,C,F,u,f,m]),B=(0,fe.useMemo)(()=>({registerOption:j,change:R}),[j,R]),U={ref:L,id:r,role:"radiogroup","aria-labelledby":g,"aria-describedby":v,onKeyDown:W},te=(0,fe.useMemo)(()=>({value:S}),[S]),w=(0,fe.useCallback)(()=>{if(D!==void 0)return R(D)},[R,D]),I=H();return fe.default.createElement(x,{name:"RadioGroup.Description"},fe.default.createElement(y,{name:"RadioGroup.Label"},fe.default.createElement(Ci.Provider,{value:B},fe.default.createElement(Ri.Provider,{value:X},a!=null&&fe.default.createElement(ct,{disabled:u,data:{[a]:S||"on"},overrides:{type:"radio",checked:S!=null},form:s,onReset:w}),I({ourProps:U,theirProps:d,slot:te,defaultTag:lp,name:"RadioGroup"})))))}var up="div";function cp(e,n){var F;let t=Si("RadioGroup.Option"),o=Ai("RadioGroup.Option"),r=(0,q.useId)(),{id:i=`headlessui-radiogroup-option-${r}`,value:s,disabled:a=t.disabled||!1,autoFocus:l=!1,...c}=e,u=(0,fe.useRef)(null),p=K(u,n),[d,f]=ke(),[m,T]=et(),b=de({value:s,disabled:a});N(()=>o.registerOption({id:i,element:u,propsRef:b}),[i,o,u,b]);let g=E(R=>{var W;if(Fe(R.currentTarget))return R.preventDefault();o.change(s)&&((W=u.current)==null||W.focus())}),y=((F=t.firstOption)==null?void 0:F.id)===i,{isFocusVisible:v,focusProps:x}=le({autoFocus:l}),{isHovered:h,hoverProps:L}=ae({isDisabled:a}),D=t.compare(t.value,s),S=re({ref:p,id:i,role:"radio","aria-checked":D?"true":"false","aria-labelledby":d,"aria-describedby":m,"aria-disabled":a?!0:void 0,tabIndex:(()=>a?-1:D||!t.containsCheckedOption&&y?0:-1)(),onClick:a?void 0:g,autoFocus:l},x,L),_=(0,fe.useMemo)(()=>({checked:D,disabled:a,active:v,hover:h,focus:v,autofocus:l}),[D,a,h,v,l]),C=H();return fe.default.createElement(T,{name:"RadioGroup.Description"},fe.default.createElement(f,{name:"RadioGroup.Label"},C({ourProps:S,theirProps:c,slot:_,defaultTag:up,name:"RadioGroup.Option"})))}var fp="span";function dp(e,n){var F;let t=Si("Radio"),o=Ai("Radio"),r=(0,q.useId)(),i=Me(),s=Te(),{id:a=i||`headlessui-radio-${r}`,value:l,disabled:c=t.disabled||s||!1,autoFocus:u=!1,...p}=e,d=(0,fe.useRef)(null),f=K(d,n),m=Ie(),T=He(),b=de({value:l,disabled:c});N(()=>o.registerOption({id:a,element:d,propsRef:b}),[a,o,d,b]);let g=E(R=>{var W;if(Fe(R.currentTarget))return R.preventDefault();o.change(l)&&((W=d.current)==null||W.focus())}),{isFocusVisible:y,focusProps:v}=le({autoFocus:u}),{isHovered:x,hoverProps:h}=ae({isDisabled:c}),L=((F=t.firstOption)==null?void 0:F.id)===a,D=t.compare(t.value,l),S=re({ref:f,id:a,role:"radio","aria-checked":D?"true":"false","aria-labelledby":m,"aria-describedby":T,"aria-disabled":c?!0:void 0,tabIndex:(()=>c?-1:D||!t.containsCheckedOption&&L?0:-1)(),autoFocus:u,onClick:c?void 0:g},v,h),_=(0,fe.useMemo)(()=>({checked:D,disabled:c,hover:x,focus:y,autofocus:u}),[D,c,x,y,u]);return H()({ourProps:S,theirProps:p,slot:_,defaultTag:fp,name:"Radio"})}var pp=M(ap),Oa=M(cp),Da=M(dp),Ia=Xe,Ma=xt,mp=Object.assign(pp,{Option:Oa,Radio:Da,Label:Ia,Description:Ma});var Fa=require("react");var Tp="select";function bp(e,n){let t=(0,q.useId)(),o=Me(),r=Te(),{id:i=o||`headlessui-select-${t}`,disabled:s=r||!1,invalid:a=!1,autoFocus:l=!1,...c}=e,u=Ie(),p=He(),{isFocusVisible:d,focusProps:f}=le({autoFocus:l}),{isHovered:m,hoverProps:T}=ae({isDisabled:s}),{pressed:b,pressProps:g}=Re({disabled:s}),y=re({ref:n,id:i,"aria-labelledby":u,"aria-describedby":p,"aria-invalid":a?"":void 0,disabled:s||void 0,autoFocus:l},f,T,g),v=(0,Fa.useMemo)(()=>({disabled:s,invalid:a,hover:m,focus:d,active:b,autofocus:l}),[s,a,m,d,b,l]);return H()({ourProps:y,theirProps:c,slot:v,defaultTag:Tp,name:"Select"})}var gp=M(bp);var Ee=oe(require("react"),1);var Li=(0,Ee.createContext)(null);Li.displayName="GroupContext";var yp=Ee.Fragment;function vp(e){var p;let[n,t]=(0,Ee.useState)(null),[o,r]=ke(),[i,s]=et(),a=(0,Ee.useMemo)(()=>({switch:n,setSwitch:t}),[n,t]),l={},c=e,u=H();return Ee.default.createElement(s,{name:"Switch.Description",value:i},Ee.default.createElement(r,{name:"Switch.Label",value:o,props:{htmlFor:(p=a.switch)==null?void 0:p.id,onClick(d){n&&(d.currentTarget instanceof HTMLLabelElement&&d.preventDefault(),n.click(),n.focus({preventScroll:!0}))}}},Ee.default.createElement(Li.Provider,{value:a},u({ourProps:l,theirProps:c,slot:{},defaultTag:yp,name:"Switch.Group"}))))}var Ep="button";function hp(e,n){var G;let t=(0,q.useId)(),o=Me(),r=Te(),{id:i=o||`headlessui-switch-${t}`,disabled:s=r||!1,checked:a,defaultChecked:l,onChange:c,name:u,value:p,form:d,autoFocus:f=!1,...m}=e,T=(0,Ee.useContext)(Li),[b,g]=(0,Ee.useState)(null),y=(0,Ee.useRef)(null),v=K(y,n,T===null?null:T.setSwitch,g),x=ut(l),[h,L]=at(a,c,x!=null?x:!1),D=Pe(),[S,_]=(0,Ee.useState)(!1),C=E(()=>{_(!0),L==null||L(!h),D.nextFrame(()=>{_(!1)})}),F=E(ie=>{if(Fe(ie.currentTarget))return ie.preventDefault();ie.preventDefault(),C()}),R=E(ie=>{ie.key===" "?(ie.preventDefault(),C()):ie.key==="Enter"&&wt(ie.currentTarget)}),W=E(ie=>ie.preventDefault()),j=Ie(),X=He(),{isFocusVisible:B,focusProps:U}=le({autoFocus:f}),{isHovered:te,hoverProps:w}=ae({isDisabled:s}),{pressed:I,pressProps:P}=Re({disabled:s}),O=(0,Ee.useMemo)(()=>({checked:h,disabled:s,hover:te,focus:B,active:I,autofocus:f,changing:S}),[h,te,B,I,s,S,f]),k=re({id:i,ref:v,role:"switch",type:Be(e,b),tabIndex:e.tabIndex===-1?0:(G=e.tabIndex)!=null?G:0,"aria-checked":h,"aria-labelledby":j,"aria-describedby":X,disabled:s||void 0,autoFocus:f,onClick:F,onKeyUp:R,onKeyPress:W},U,w,P),A=(0,Ee.useCallback)(()=>{if(x!==void 0)return L==null?void 0:L(x)},[L,x]),$=H();return Ee.default.createElement(Ee.default.Fragment,null,u!=null&&Ee.default.createElement(ct,{disabled:s,data:{[u]:p||"on"},overrides:{type:"checkbox",checked:h},form:d,onReset:A}),$({ourProps:k,theirProps:m,slot:O,defaultTag:Ep,name:"Switch"}))}var xp=M(hp),wa=vp,_a=Xe,Ha=xt,Pp=Object.assign(xp,{Group:wa,Label:_a,Description:Ha});var pe=oe(require("react"),1);var dr=oe(require("react"),1);function ka({onFocus:e}){let[n,t]=(0,dr.useState)(!0),o=nn();return n?dr.default.createElement(we,{as:"button",type:"button",features:2,onFocus:r=>{r.preventDefault();let i,s=50;function a(){if(s--<=0){i&&cancelAnimationFrame(i);return}if(e()){if(cancelAnimationFrame(i),!o.current)return;t(!1);return}i=requestAnimationFrame(a)}i=requestAnimationFrame(a)}}):null}var Qe=oe(require("react"),1),$a=Qe.createContext(null);function Rp(){return{groups:new Map,get(e,n){var s;let t=this.groups.get(e);t||(t=new Map,this.groups.set(e,t));let o=(s=t.get(n))!=null?s:0;t.set(n,o+1);let r=Array.from(t.keys()).indexOf(n);function i(){let a=t.get(n);a>1?t.set(n,a-1):t.delete(n)}return[r,i]}}}function Na({children:e}){let n=Qe.useRef(Rp());return Qe.createElement($a.Provider,{value:n},e)}function Oi(e){let n=Qe.useContext($a);if(!n)throw new Error("You must wrap your component in a <StableCollection>");let t=Qe.useId(),[o,r]=n.current.get(e,t);return Qe.useEffect(()=>r,[]),o}var Sp={[0](e,n){var u;let t=$e(e.tabs,p=>p.current),o=$e(e.panels,p=>p.current),r=t.filter(p=>{var d;return!((d=p.current)!=null&&d.hasAttribute("disabled"))}),i={...e,tabs:t,panels:o};if(n.index<0||n.index>t.length-1){let p=J(Math.sign(n.index-e.selectedIndex),{[-1]:()=>1,[0]:()=>J(Math.sign(n.index),{[-1]:()=>0,[0]:()=>0,[1]:()=>1}),[1]:()=>0});if(r.length===0)return i;let d=J(p,{[0]:()=>t.indexOf(r[0]),[1]:()=>t.indexOf(r[r.length-1])});return{...i,selectedIndex:d===-1?e.selectedIndex:d}}let s=t.slice(0,n.index),l=[...t.slice(n.index),...s].find(p=>r.includes(p));if(!l)return i;let c=(u=t.indexOf(l))!=null?u:e.selectedIndex;return c===-1&&(c=e.selectedIndex),{...i,selectedIndex:c}},[1](e,n){if(e.tabs.includes(n.tab))return e;let t=e.tabs[e.selectedIndex],o=$e([...e.tabs,n.tab],i=>i.current),r=e.selectedIndex;return e.info.current.isControlled||(r=o.indexOf(t),r===-1&&(r=e.selectedIndex)),{...e,tabs:o,selectedIndex:r}},[2](e,n){return{...e,tabs:e.tabs.filter(t=>t!==n.tab)}},[3](e,n){return e.panels.includes(n.panel)?e:{...e,panels:$e([...e.panels,n.panel],t=>t.current)}},[4](e,n){return{...e,panels:e.panels.filter(t=>t!==n.panel)}}},Di=(0,pe.createContext)(null);Di.displayName="TabsDataContext";function On(e){let n=(0,pe.useContext)(Di);if(n===null){let t=new Error(`<${e} /> is missing a parent <Tab.Group /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,On),t}return n}var Ii=(0,pe.createContext)(null);Ii.displayName="TabsActionsContext";function Mi(e){let n=(0,pe.useContext)(Ii);if(n===null){let t=new Error(`<${e} /> is missing a parent <Tab.Group /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,Mi),t}return n}function Cp(e,n){return J(n.type,Sp,e,n)}var Ap="div";function Lp(e,n){let{defaultIndex:t=0,vertical:o=!1,manual:r=!1,onChange:i,selectedIndex:s=null,...a}=e,l=o?"vertical":"horizontal",c=r?"manual":"auto",u=s!==null,p=de({isControlled:u}),d=K(n),[f,m]=(0,pe.useReducer)(Cp,{info:p,selectedIndex:s!=null?s:t,tabs:[],panels:[]}),T=(0,pe.useMemo)(()=>({selectedIndex:f.selectedIndex}),[f.selectedIndex]),b=de(i||(()=>{})),g=de(f.tabs),y=(0,pe.useMemo)(()=>({orientation:l,activation:c,...f}),[l,c,f]),v=E(C=>(m({type:1,tab:C}),()=>m({type:2,tab:C}))),x=E(C=>(m({type:3,panel:C}),()=>m({type:4,panel:C}))),h=E(C=>{L.current!==C&&b.current(C),u||m({type:0,index:C})}),L=de(u?e.selectedIndex:f.selectedIndex),D=(0,pe.useMemo)(()=>({registerTab:v,registerPanel:x,change:h}),[]);N(()=>{m({type:0,index:s!=null?s:t})},[s]),N(()=>{if(L.current===void 0||f.tabs.length<=0)return;let C=$e(f.tabs,R=>R.current);C.some((R,W)=>f.tabs[W]!==R)&&h(C.indexOf(f.tabs[L.current]))});let S={ref:d},_=H();return pe.default.createElement(Na,null,pe.default.createElement(Ii.Provider,{value:D},pe.default.createElement(Di.Provider,{value:y},y.tabs.length<=0&&pe.default.createElement(ka,{onFocus:()=>{var C,F;for(let R of g.current)if(((C=R.current)==null?void 0:C.tabIndex)===0)return(F=R.current)==null||F.focus(),!0;return!1}}),_({ourProps:S,theirProps:a,slot:T,defaultTag:Ap,name:"Tabs"}))))}var Op="div";function Dp(e,n){let{orientation:t,selectedIndex:o}=On("Tab.List"),r=K(n),i=(0,pe.useMemo)(()=>({selectedIndex:o}),[o]),s=e,a={ref:r,role:"tablist","aria-orientation":t};return H()({ourProps:a,theirProps:s,slot:i,defaultTag:Op,name:"Tabs.List"})}var Ip="button";function Mp(e,n){var w,I;let t=(0,q.useId)(),{id:o=`headlessui-tabs-tab-${t}`,disabled:r=!1,autoFocus:i=!1,...s}=e,{orientation:a,activation:l,selectedIndex:c,tabs:u,panels:p}=On("Tab"),d=Mi("Tab"),f=On("Tab"),[m,T]=(0,pe.useState)(null),b=(0,pe.useRef)(null),g=K(b,n,T);N(()=>d.registerTab(b),[d,b]);let y=Oi("tabs"),v=u.indexOf(b);v===-1&&(v=y);let x=v===c,h=E(P=>{var k;let O=P();if(O===2&&l==="auto"){let A=(k=he(b))==null?void 0:k.activeElement,$=f.tabs.findIndex(G=>G.current===A);$!==-1&&d.change($)}return O}),L=E(P=>{let O=u.map(A=>A.current).filter(Boolean);if(P.key===" "||P.key==="Enter"){P.preventDefault(),P.stopPropagation(),d.change(v);return}switch(P.key){case"Home":case"PageUp":return P.preventDefault(),P.stopPropagation(),h(()=>ye(O,1));case"End":case"PageDown":return P.preventDefault(),P.stopPropagation(),h(()=>ye(O,8))}if(h(()=>J(a,{vertical(){return P.key==="ArrowUp"?ye(O,18):P.key==="ArrowDown"?ye(O,20):0},horizontal(){return P.key==="ArrowLeft"?ye(O,18):P.key==="ArrowRight"?ye(O,20):0}}))===2)return P.preventDefault()}),D=(0,pe.useRef)(!1),S=E(()=>{var P;D.current||(D.current=!0,(P=b.current)==null||P.focus({preventScroll:!0}),d.change(v),ht(()=>{D.current=!1}))}),_=E(P=>{P.preventDefault()}),{isFocusVisible:C,focusProps:F}=le({autoFocus:i}),{isHovered:R,hoverProps:W}=ae({isDisabled:r}),{pressed:j,pressProps:X}=Re({disabled:r}),B=(0,pe.useMemo)(()=>({selected:x,hover:R,active:j,focus:C,autofocus:i,disabled:r}),[x,R,C,j,i,r]),U=re({ref:g,onKeyDown:L,onMouseDown:_,onClick:S,id:o,role:"tab",type:Be(e,m),"aria-controls":(I=(w=p[v])==null?void 0:w.current)==null?void 0:I.id,"aria-selected":x,tabIndex:x?0:-1,disabled:r||void 0,autoFocus:i},F,W,X);return H()({ourProps:U,theirProps:s,slot:B,defaultTag:Ip,name:"Tabs.Tab"})}var Fp="div";function wp(e,n){let{selectedIndex:t}=On("Tab.Panels"),o=K(n),r=(0,pe.useMemo)(()=>({selectedIndex:t}),[t]),i=e,s={ref:o};return H()({ourProps:s,theirProps:i,slot:r,defaultTag:Fp,name:"Tabs.Panels"})}var _p="div",Hp=3;function kp(e,n){var x,h,L,D;let t=(0,q.useId)(),{id:o=`headlessui-tabs-panel-${t}`,tabIndex:r=0,...i}=e,{selectedIndex:s,tabs:a,panels:l}=On("Tab.Panel"),c=Mi("Tab.Panel"),u=(0,pe.useRef)(null),p=K(u,n);N(()=>c.registerPanel(u),[c,u]);let d=Oi("panels"),f=l.indexOf(u);f===-1&&(f=d);let m=f===s,{isFocusVisible:T,focusProps:b}=le(),g=(0,pe.useMemo)(()=>({selected:m,focus:T}),[m,T]),y=re({ref:p,id:o,role:"tabpanel","aria-labelledby":(h=(x=a[f])==null?void 0:x.current)==null?void 0:h.id,tabIndex:m?r:-1},b),v=H();return!m&&((L=i.unmount)==null||L)&&!((D=i.static)!=null&&D)?pe.default.createElement(we,{"aria-hidden":"true",...y}):v({ourProps:y,theirProps:i,slot:g,defaultTag:_p,features:Hp,visible:m,name:"Tabs.Panel"})}var $p=M(Mp),Ba=M(Lp),Ua=M(Dp),Ga=M(wp),Va=M(kp),Np=Object.assign($p,{Group:Ba,List:Ua,Panels:Ga,Panel:Va});var Wa=require("react");var Bp="textarea";function Up(e,n){let t=(0,q.useId)(),o=Me(),r=Te(),{id:i=o||`headlessui-textarea-${t}`,disabled:s=r||!1,autoFocus:a=!1,invalid:l=!1,...c}=e,u=Ie(),p=He(),{isFocused:d,focusProps:f}=le({autoFocus:a}),{isHovered:m,hoverProps:T}=ae({isDisabled:s}),b=re({ref:n,id:i,"aria-labelledby":u,"aria-describedby":p,"aria-invalid":l?"":void 0,disabled:s||void 0,autoFocus:a},f,T),g=(0,Wa.useMemo)(()=>({disabled:s,invalid:l,hover:m,focus:d,autofocus:a}),[s,l,m,d,a]);return H()({ourProps:b,theirProps:c,slot:g,defaultTag:Bp,name:"Textarea"})}var Gp=M(Up);
