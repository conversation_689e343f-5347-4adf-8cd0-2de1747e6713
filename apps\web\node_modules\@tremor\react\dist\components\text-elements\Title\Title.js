import{__rest as t}from"tslib";import{colorPalette as r}from"../../../lib/theme.js";import{tremorTwMerge as e}from"../../../lib/tremorTwMerge.js";import{getColorClassNames as o}from"../../../lib/utils.js";import m from"react";const s=m.forwardRef(((s,l)=>{const{color:a,children:i,className:c}=s,n=t(s,["color","children","className"]);return m.createElement("p",Object.assign({ref:l,className:e("font-medium text-tremor-title",a?o(a,r.darkText).textColor:"text-tremor-content-strong dark:text-dark-tremor-content-strong",c)},n),i)}));s.displayName="Title";export{s as default};
