"use strict";var e=require("tslib"),r=require("../../../lib/tremorTwMerge.cjs");function t(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var a=t(require("react"));const s=a.default.forwardRef(((t,s)=>{const{children:l,className:c}=t,i=e.__rest(t,["children","className"]);return a.default.createElement("b",Object.assign({ref:s,className:r.tremorTwMerge("text-inherit font-bold",c)},i),l)}));s.displayName="Bold",module.exports=s;
