'use client';
import{__rest as e}from"tslib";import{tremorTwMerge as r}from"../../../lib/tremorTwMerge.js";import t,{useMemo as a}from"react";import{startOfToday as o,startOfMonth as n}from"date-fns";import{enUS as l}from"date-fns/locale";import{Popover as d,PopoverButton as s,Transition as m,PopoverPanel as i}from"@headlessui/react";import c from"../../../assets/CalendarIcon.js";import u from"../../../assets/XCircleIcon.js";import b from"../Calendar/Calendar.js";import{makeDatePickerClassName as f}from"./datePickerUtils.js";import p from"../../../hooks/useInternalState.js";import{formatSelectedDates as k}from"../DateRangePicker/dateRangePickerUtils.js";import{getSelectButtonColors as h,hasValue as g}from"../selectUtils.js";const w=o(),v=t.forwardRef(((o,v)=>{var x;const{value:y,defaultValue:N,onValueChange:D,minDate:j,maxDate:C,placeholder:E="Select date",disabled:S=!1,locale:O=l,enableClear:F=!0,displayFormat:I,className:P,enableYearNavigation:V=!1,weekStartsOn:R=0,disabledDates:T}=o,U=e(o,["value","defaultValue","onValueChange","minDate","maxDate","placeholder","disabled","locale","enableClear","displayFormat","className","enableYearNavigation","weekStartsOn","disabledDates"]),[Y,M]=p(N,y),z=a((()=>{const e=[];return j&&e.push({before:j}),C&&e.push({after:C}),[...e,...null!=T?T:[]]}),[j,C,T]),X=Y?k(Y,void 0,O,I):E,q=n(null!==(x=null!=Y?Y:C)&&void 0!==x?x:w),A=F&&!S;return t.createElement(d,Object.assign({ref:v,as:"div",className:r("relative w-full min-w-[10rem] text-tremor-default","focus:ring-2 focus:ring-tremor-brand-muted dark:focus:ring-dark-tremor-brand-muted",P)},U),t.createElement(s,{disabled:S,className:r("w-full outline-none text-left whitespace-nowrap truncate focus:ring-2 transition duration-100 rounded-tremor-default flex flex-nowrap border pl-3 py-2","border-tremor-border shadow-tremor-input text-tremor-content-emphasis focus:border-tremor-brand-subtle focus:ring-tremor-brand-muted","dark:border-dark-tremor-border dark:shadow-dark-tremor-input dark:text-dark-tremor-content-emphasis dark:focus:border-dark-tremor-brand-subtle dark:focus:ring-dark-tremor-brand-muted",A?"pr-8":"pr-4",h(g(Y),S))},t.createElement(c,{className:r(f("calendarIcon"),"flex-none shrink-0 h-5 w-5 mr-2 -ml-0.5","text-tremor-content-subtle","dark:text-dark-tremor-content-subtle"),"aria-hidden":"true"}),t.createElement("p",{className:"truncate"},X)),A&&Y?t.createElement("button",{type:"button",className:r("absolute outline-none inset-y-0 right-0 flex items-center transition duration-100 mr-4"),onClick:e=>{e.preventDefault(),null==D||D(void 0),M(void 0)}},t.createElement(u,{className:r("flex-none h-4 w-4","text-tremor-content-subtle","dark:text-dark-tremor-content-subtle")})):null,t.createElement(m,{enter:"transition ease duration-100 transform",enterFrom:"opacity-0 -translate-y-4",enterTo:"opacity-100 translate-y-0",leave:"transition ease duration-100 transform",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 -translate-y-4"},t.createElement(i,{anchor:"bottom start",className:r("z-10 min-w-min divide-y overflow-y-auto outline-none rounded-tremor-default p-3 border [--anchor-gap:4px]","bg-tremor-background border-tremor-border divide-tremor-border shadow-tremor-dropdown","dark:bg-dark-tremor-background dark:border-dark-tremor-border dark:divide-dark-tremor-border dark:shadow-dark-tremor-dropdown")},(({close:e})=>t.createElement(b,{showOutsideDays:!0,mode:"single",defaultMonth:q,selected:Y,weekStartsOn:R,onSelect:r=>{null==D||D(r),M(r),e()},locale:O,disabled:z,enableYearNavigation:V})))))}));v.displayName="DatePicker";export{v as default};
