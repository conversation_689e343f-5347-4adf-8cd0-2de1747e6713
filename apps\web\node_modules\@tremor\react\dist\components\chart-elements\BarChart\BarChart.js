'use client';
import{__rest as e}from"tslib";import{BaseColors as t}from"../../../lib/constants.js";import{themeColorRange as a,colorPalette as l}from"../../../lib/theme.js";import{tremorTwMerge as i}from"../../../lib/tremorTwMerge.js";import{getColorClassNames as o,defaultValueFormatter as r}from"../../../lib/utils.js";import n,{useState as s}from"react";import{ResponsiveContainer as m,Bar<PERSON>hart as c,CartesianGrid as d,XAxis as u,Label as p,YAxis as v,Tooltip as f,Legend as h,Bar as g}from"recharts";import y from"../common/ChartLegend.js";import k from"../common/ChartTooltip.js";import b from"../common/NoData.js";import{constructCategoryColors as x,deepEqual as w,getYAxisDomain as E}from"../common/utils.js";const L=n.forwardRef(((L,A)=>{const{data:j=[],categories:N=[],index:C,colors:T=a,valueFormatter:O=r,layout:D="horizontal",stack:G=!1,relative:S=!1,startEndOnly:V=!1,animationDuration:F=900,showAnimation:K=!1,showXAxis:M=!0,showYAxis:z=!0,yAxisWidth:B=56,intervalType:P="equidistantPreserveStart",showTooltip:X=!0,showLegend:H=!0,showGridLines:W=!0,autoMinValue:Y=!1,minValue:q,maxValue:I,allowDecimals:R=!0,noDataText:$,onValueChange:J,enableLegendSlider:Q=!1,customTooltip:U,rotateLabelX:Z,barCategoryGap:_,tickGap:ee=5,xAxisLabel:te,yAxisLabel:ae,className:le,padding:ie=(M||z?{left:20,right:20}:{left:0,right:0})}=L,oe=e(L,["data","categories","index","colors","valueFormatter","layout","stack","relative","startEndOnly","animationDuration","showAnimation","showXAxis","showYAxis","yAxisWidth","intervalType","showTooltip","showLegend","showGridLines","autoMinValue","minValue","maxValue","allowDecimals","noDataText","onValueChange","enableLegendSlider","customTooltip","rotateLabelX","barCategoryGap","tickGap","xAxisLabel","yAxisLabel","className","padding"]),re=U,[ne,se]=s(60),me=x(N,T),[ce,de]=n.useState(void 0),[ue,pe]=s(void 0),ve=!!J;function fe(e,t,a){var l,i,o,r;a.stopPropagation(),J&&(w(ce,Object.assign(Object.assign({},e.payload),{value:e.value}))?(pe(void 0),de(void 0),null==J||J(null)):(pe(null===(i=null===(l=e.tooltipPayload)||void 0===l?void 0:l[0])||void 0===i?void 0:i.dataKey),de(Object.assign(Object.assign({},e.payload),{value:e.value})),null==J||J(Object.assign({eventType:"bar",categoryClicked:null===(r=null===(o=e.tooltipPayload)||void 0===o?void 0:o[0])||void 0===r?void 0:r.dataKey},e.payload))))}const he=E(Y,q,I);return n.createElement("div",Object.assign({ref:A,className:i("w-full h-80",le)},oe),n.createElement(m,{className:"h-full w-full"},(null==j?void 0:j.length)?n.createElement(c,{barCategoryGap:_,data:j,stackOffset:G?"sign":S?"expand":"none",layout:"vertical"===D?"vertical":"horizontal",onClick:ve&&(ue||ce)?()=>{de(void 0),pe(void 0),null==J||J(null)}:void 0,margin:{bottom:te?30:void 0,left:ae?20:void 0,right:ae?5:void 0,top:5}},W?n.createElement(d,{className:i("stroke-1","stroke-tremor-border","dark:stroke-dark-tremor-border"),horizontal:"vertical"!==D,vertical:"vertical"===D}):null,"vertical"!==D?n.createElement(u,{padding:ie,hide:!M,dataKey:C,interval:V?"preserveStartEnd":P,tick:{transform:"translate(0, 6)"},ticks:V?[j[0][C],j[j.length-1][C]]:void 0,fill:"",stroke:"",className:i("mt-4 text-tremor-label","fill-tremor-content","dark:fill-dark-tremor-content"),tickLine:!1,axisLine:!1,angle:null==Z?void 0:Z.angle,dy:null==Z?void 0:Z.verticalShift,height:null==Z?void 0:Z.xAxisHeight,minTickGap:ee},te&&n.createElement(p,{position:"insideBottom",offset:-20,className:"fill-tremor-content-emphasis text-tremor-default font-medium dark:fill-dark-tremor-content-emphasis"},te)):n.createElement(u,{hide:!M,type:"number",tick:{transform:"translate(-3, 0)"},domain:he,fill:"",stroke:"",className:i("text-tremor-label","fill-tremor-content","dark:fill-dark-tremor-content"),tickLine:!1,axisLine:!1,tickFormatter:O,minTickGap:ee,allowDecimals:R,angle:null==Z?void 0:Z.angle,dy:null==Z?void 0:Z.verticalShift,height:null==Z?void 0:Z.xAxisHeight},te&&n.createElement(p,{position:"insideBottom",offset:-20,className:"fill-tremor-content-emphasis text-tremor-default font-medium dark:fill-dark-tremor-content-emphasis"},te)),"vertical"!==D?n.createElement(v,{width:B,hide:!z,axisLine:!1,tickLine:!1,type:"number",domain:he,tick:{transform:"translate(-3, 0)"},fill:"",stroke:"",className:i("text-tremor-label","fill-tremor-content","dark:fill-dark-tremor-content"),tickFormatter:S?e=>`${(100*e).toString()} %`:O,allowDecimals:R},ae&&n.createElement(p,{position:"insideLeft",style:{textAnchor:"middle"},angle:-90,offset:-15,className:"fill-tremor-content-emphasis text-tremor-default font-medium dark:fill-dark-tremor-content-emphasis"},ae)):n.createElement(v,{width:B,hide:!z,dataKey:C,axisLine:!1,tickLine:!1,ticks:V?[j[0][C],j[j.length-1][C]]:void 0,type:"category",interval:"preserveStartEnd",tick:{transform:"translate(0, 6)"},fill:"",stroke:"",className:i("text-tremor-label","fill-tremor-content","dark:fill-dark-tremor-content")},ae&&n.createElement(p,{position:"insideLeft",style:{textAnchor:"middle"},angle:-90,offset:-15,className:"fill-tremor-content-emphasis text-tremor-default font-medium dark:fill-dark-tremor-content-emphasis"},ae)),n.createElement(f,{wrapperStyle:{outline:"none"},isAnimationActive:!1,cursor:{fill:"#d1d5db",opacity:"0.15"},content:X?({active:e,payload:a,label:l})=>re?n.createElement(re,{payload:null==a?void 0:a.map((e=>{var a;return Object.assign(Object.assign({},e),{color:null!==(a=me.get(e.dataKey))&&void 0!==a?a:t.Gray})})),active:e,label:l}):n.createElement(k,{active:e,payload:a,label:l,valueFormatter:O,categoryColors:me}):n.createElement(n.Fragment,null),position:{y:0}}),H?n.createElement(h,{verticalAlign:"top",height:ne,content:({payload:e})=>y({payload:e},me,se,ue,ve?e=>{return t=e,void(ve&&(t!==ue||ce?(pe(t),null==J||J({eventType:"category",categoryClicked:t})):(pe(void 0),null==J||J(null)),de(void 0)));var t}:void 0,Q)}):null,N.map((e=>{var a;return n.createElement(g,{className:i(o(null!==(a=me.get(e))&&void 0!==a?a:t.Gray,l.background).fillColor,J?"cursor-pointer":""),key:e,name:e,type:"linear",stackId:G||S?"a":void 0,dataKey:e,fill:"",isAnimationActive:K,animationDuration:F,shape:e=>((e,t,a,l)=>{const{fillOpacity:i,name:o,payload:r,value:s}=e;let{x:m,width:c,y:d,height:u}=e;return"horizontal"===l&&u<0?(d+=u,u=Math.abs(u)):"vertical"===l&&c<0&&(m+=c,c=Math.abs(c)),n.createElement("rect",{x:m,y:d,width:c,height:u,opacity:t||a&&a!==o?w(t,Object.assign(Object.assign({},r),{value:s}))?i:.3:i})})(e,ce,ue,D),onClick:fe})}))):n.createElement(b,{noDataText:$})))}));L.displayName="BarChart";export{L as default};
