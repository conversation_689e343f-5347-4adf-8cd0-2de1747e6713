'use client';
import{__rest as e}from"tslib";import{Disclosure as r}from"@headlessui/react";import"../../../contexts/BaseColorContext.js";import"../../../contexts/IndexContext.js";import o from"../../../contexts/RootStylesContext.js";import"../../../contexts/SelectedValueContext.js";import{tremorTwMerge as t}from"../../../lib/tremorTwMerge.js";import{makeClassName as a}from"../../../lib/utils.js";import d,{createContext as s,useContext as n}from"react";const m=a("Accordion"),i=s({isOpen:!1}),l=d.forwardRef(((a,s)=>{var l;const{defaultOpen:c=!1,children:p,className:f}=a,u=e(a,["defaultOpen","children","className"]),b=null!==(l=n(o))&&void 0!==l?l:t("rounded-tremor-default border");return d.createElement(r,Object.assign({as:"div",ref:s,className:t(m("root"),"overflow-hidden","bg-tremor-background border-tremor-border","dark:bg-dark-tremor-background dark:border-dark-tremor-border",b,f),defaultOpen:c},u),(({open:e})=>d.createElement(i.Provider,{value:{isOpen:e}},p)))}));l.displayName="Accordion";export{i as OpenContext,l as default};
