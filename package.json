{"name": "ai-interviewspark", "version": "1.0.0", "description": "Advanced AI-powered mock interview platform with real-time emotional analysis", "private": true, "scripts": {"dev": "concurrently \"npm run dev:api\" \"npm run dev:web\"", "dev:api": "cd apps/api && npm run dev", "dev:web": "cd apps/web && npm run dev", "build": "npm run build:shared && npm run build:api && npm run build:web", "build:shared": "cd packages/shared && npm run build", "build:api": "cd apps/api && npm run build", "build:web": "cd apps/web && npm run build", "test": "npm run test:api && npm run test:web", "test:api": "cd apps/api && npm run test", "test:web": "cd apps/web && npm run test", "lint": "npm run lint:api && npm run lint:web", "lint:api": "cd apps/api && npm run lint", "lint:web": "cd apps/web && npm run lint", "clean": "npm run clean:shared && npm run clean:api && npm run clean:web", "clean:shared": "cd packages/shared && npm run clean", "clean:api": "cd apps/api && npm run clean", "clean:web": "cd apps/web && npm run clean", "db:setup": "cd apps/api && npm run db:setup", "db:reset": "cd apps/api && npm run db:reset", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "prepare": "husky install"}, "devDependencies": {"@types/node": "^20.10.0", "concurrently": "^8.2.2", "eslint": "^8.55.0", "husky": "^8.0.3", "lint-staged": "^15.2.0", "prettier": "^3.1.0", "turbo": "^1.11.0", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}, "dependencies": {"axios": "^1.10.0"}}