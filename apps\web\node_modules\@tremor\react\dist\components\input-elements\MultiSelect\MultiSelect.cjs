'use client';
"use strict";var e=require("tslib"),t=require("react");require("../../../contexts/BaseColorContext.cjs"),require("../../../contexts/IndexContext.cjs"),require("../../../contexts/RootStylesContext.cjs");var r=require("../../../contexts/SelectedValueContext.cjs"),a=require("../../../hooks/useInternalState.cjs"),l=require("../../../assets/ArrowDownHeadIcon.cjs"),o=require("../../../assets/SearchIcon.cjs"),n=require("../../../assets/XCircleIcon.cjs"),s=require("../../../assets/XIcon.cjs"),d=require("../../../lib/tremorTwMerge.cjs"),u=require("../../../lib/utils.cjs"),c=require("../selectUtils.cjs"),m=require("@headlessui/react");function i(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var f=i(t);const p=u.makeClassName("MultiSelect"),b=f.default.forwardRef(((u,i)=>{const{defaultValue:b=[],value:g,onValueChange:x,placeholder:h="Select...",placeholderSearch:k="Search",disabled:w=!1,icon:v,children:E,className:y,required:N,name:M,error:T=!1,errorMessage:C,id:q}=u,j=e.__rest(u,["defaultValue","value","onValueChange","placeholder","placeholderSearch","disabled","icon","children","className","required","name","error","errorMessage","id"]),S=t.useRef(null),I=v,[V,D]=a(b,g),{reactElementChildren:F,optionsAvailable:A}=t.useMemo((()=>{const e=f.default.Children.toArray(E).filter(t.isValidElement);return{reactElementChildren:e,optionsAvailable:c.getFilteredOptions("",e)}}),[E]),[O,B]=t.useState(""),R=(null!=V?V:[]).length>0,L=t.useMemo((()=>O?c.getFilteredOptions(O,F):A),[O,F,A]),z=()=>{B("")};return f.default.createElement("div",{className:d.tremorTwMerge("w-full min-w-[10rem] text-tremor-default",y)},f.default.createElement("div",{className:"relative"},f.default.createElement("select",{title:"multi-select-hidden",required:N,className:d.tremorTwMerge("h-full w-full absolute left-0 top-0 -z-10 opacity-0"),value:V,onChange:e=>{e.preventDefault()},name:M,disabled:w,multiple:!0,id:q,onFocus:()=>{const e=S.current;e&&e.focus()}},f.default.createElement("option",{className:"hidden",value:"",disabled:!0,hidden:!0},h),L.map((e=>{const t=e.props.value,r=e.props.children;return f.default.createElement("option",{className:"hidden",key:t,value:t},r)}))),f.default.createElement(m.Listbox,Object.assign({as:"div",ref:i,defaultValue:V,value:V,onChange:e=>{null==x||x(e),D(e)},disabled:w,id:q,multiple:!0},j),(({value:e})=>f.default.createElement(f.default.Fragment,null,f.default.createElement(m.ListboxButton,{className:d.tremorTwMerge("w-full outline-none text-left whitespace-nowrap truncate rounded-tremor-default focus:ring-2 transition duration-100 border pr-8 py-1.5","border-tremor-border shadow-tremor-input focus:border-tremor-brand-subtle focus:ring-tremor-brand-muted","dark:border-dark-tremor-border dark:shadow-dark-tremor-input dark:focus:border-dark-tremor-brand-subtle dark:focus:ring-dark-tremor-brand-muted",I?"pl-11 -ml-0.5":"pl-3",c.getSelectButtonColors(e.length>0,w,T)),ref:S},I&&f.default.createElement("span",{className:d.tremorTwMerge("absolute inset-y-0 left-0 flex items-center ml-px pl-2.5")},f.default.createElement(I,{className:d.tremorTwMerge(p("Icon"),"flex-none h-5 w-5","text-tremor-content-subtle","dark:text-dark-tremor-content-subtle")})),f.default.createElement("div",{className:"h-6 flex items-center"},e.length>0?f.default.createElement("div",{className:"flex flex-nowrap overflow-x-scroll [&::-webkit-scrollbar]:hidden [scrollbar-width:none] gap-x-1 mr-5 -ml-1.5 relative"},A.filter((t=>e.includes(t.props.value))).map(((t,r)=>{var a;return f.default.createElement("div",{key:r,className:d.tremorTwMerge("max-w-[100px] lg:max-w-[200px] flex justify-center items-center pl-2 pr-1.5 py-1 font-medium","rounded-tremor-small","bg-tremor-background-muted dark:bg-dark-tremor-background-muted","bg-tremor-background-subtle dark:bg-dark-tremor-background-subtle","text-tremor-content-default dark:text-dark-tremor-content-default","text-tremor-content-emphasis dark:text-dark-tremor-content-emphasis")},f.default.createElement("div",{className:"text-xs truncate "},null!==(a=t.props.children)&&void 0!==a?a:t.props.value),f.default.createElement("div",{onClick:r=>{r.preventDefault();const a=e.filter((e=>e!==t.props.value));null==x||x(a),D(a)}},f.default.createElement(s,{className:d.tremorTwMerge(p("clearIconItem"),"cursor-pointer rounded-tremor-full w-3.5 h-3.5 ml-2","text-tremor-content-subtle hover:text-tremor-content","dark:text-dark-tremor-content-subtle dark:hover:text-tremor-content")})))}))):f.default.createElement("span",null,h)),f.default.createElement("span",{className:d.tremorTwMerge("absolute inset-y-0 right-0 flex items-center mr-2.5")},f.default.createElement(l,{className:d.tremorTwMerge(p("arrowDownIcon"),"flex-none h-5 w-5","text-tremor-content-subtle","dark:text-dark-tremor-content-subtle")}))),R&&!w?f.default.createElement("button",{type:"button",className:d.tremorTwMerge("absolute inset-y-0 right-0 flex items-center mr-8"),onClick:e=>{e.preventDefault(),D([]),null==x||x([])}},f.default.createElement(n,{className:d.tremorTwMerge(p("clearIconAllItems"),"flex-none h-4 w-4","text-tremor-content-subtle","dark:text-dark-tremor-content-subtle")})):null,f.default.createElement(m.Transition,{enter:"transition ease duration-100 transform",enterFrom:"opacity-0 -translate-y-4",enterTo:"opacity-100 translate-y-0",leave:"transition ease duration-100 transform",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 -translate-y-4"},f.default.createElement(m.ListboxOptions,{anchor:"bottom start",className:d.tremorTwMerge("z-10 divide-y w-[var(--button-width)] overflow-y-auto outline-none rounded-tremor-default max-h-[228px]  border [--anchor-gap:4px]","bg-tremor-background border-tremor-border divide-tremor-border shadow-tremor-dropdown","dark:bg-dark-tremor-background dark:border-dark-tremor-border dark:divide-dark-tremor-border dark:shadow-dark-tremor-dropdown")},f.default.createElement("div",{className:d.tremorTwMerge("flex items-center w-full px-2.5","bg-tremor-background-muted","dark:bg-dark-tremor-background-muted")},f.default.createElement("span",null,f.default.createElement(o,{className:d.tremorTwMerge("flex-none w-4 h-4 mr-2","text-tremor-content-subtle","dark:text-dark-tremor-content-subtle")})),f.default.createElement("input",{name:"search",type:"input",autoComplete:"off",placeholder:k,className:d.tremorTwMerge("w-full focus:outline-none focus:ring-none bg-transparent text-tremor-default py-2","text-tremor-content-emphasis","dark:text-dark-tremor-content-subtle"),onKeyDown:e=>{"Space"===e.code&&""!==e.target.value&&e.stopPropagation()},onChange:e=>B(e.target.value),value:O})),f.default.createElement(r.Provider,Object.assign({},{onBlur:{handleResetSearch:z}},{value:{selectedValue:e}}),L))))))),T&&C?f.default.createElement("p",{className:d.tremorTwMerge("errorMessage","text-sm text-rose-500 mt-1")},C):null)}));b.displayName="MultiSelect",module.exports=b;
