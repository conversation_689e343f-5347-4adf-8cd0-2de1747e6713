"use client";import{useFocusRing as Se}from"@react-aria/focus";import{useHover as Ie}from"@react-aria/interactions";import{useVirtualizer as He}from"@tanstack/react-virtual";import M,{Fragment as Pe,createContext as ce,createRef as Ue,use<PERSON><PERSON>back as fe,useContext as Te,useMemo as W,useReducer as Ge,useRef as ee,useState as Ae}from"react";import{flushSync as te}from"react-dom";import{useActivePress as ze}from'../../hooks/use-active-press.js';import{useByComparator as Ke}from'../../hooks/use-by-comparator.js';import{useControllable as je}from'../../hooks/use-controllable.js';import{useDefaultValue as We}from'../../hooks/use-default-value.js';import{useDisposables as $e}from'../../hooks/use-disposables.js';import{useElementSize as Re}from'../../hooks/use-element-size.js';import{useEvent as c}from'../../hooks/use-event.js';import{useId as de}from'../../hooks/use-id.js';import{useInertOthers as Xe}from'../../hooks/use-inert-others.js';import{useIsoMorphicEffect as q}from'../../hooks/use-iso-morphic-effect.js';import{useLatestValue as Je}from'../../hooks/use-latest-value.js';import{useOnDisappear as qe}from'../../hooks/use-on-disappear.js';import{useOutsideClick as Ye}from'../../hooks/use-outside-click.js';import{useOwnerDocument as _e}from'../../hooks/use-owner.js';import{useRefocusableInput as he}from'../../hooks/use-refocusable-input.js';import{useResolveButtonType as Qe}from'../../hooks/use-resolve-button-type.js';import{useScrollLock as Ze}from'../../hooks/use-scroll-lock.js';import{useSyncRefs as be}from'../../hooks/use-sync-refs.js';import{useTrackedPointer as et}from'../../hooks/use-tracked-pointer.js';import{transitionDataAttributes as tt,useTransition as ot}from'../../hooks/use-transition.js';import{useTreeWalker as nt}from'../../hooks/use-tree-walker.js';import{useWatch as Me}from'../../hooks/use-watch.js';import{useDisabled as rt}from'../../internal/disabled.js';import{FloatingProvider as it,useFloatingPanel as lt,useFloatingPanelProps as at,useFloatingReference as ut,useResolvedAnchor as pt}from'../../internal/floating.js';import{FormFields as st}from'../../internal/form-fields.js';import{Frozen as dt,useFrozenData as De}from'../../internal/frozen.js';import{useProvidedId as bt}from'../../internal/id.js';import{OpenClosedProvider as mt,State as me,useOpenClosed as ct}from'../../internal/open-closed.js';import{history as Fe}from'../../utils/active-element-history.js';import{isDisabledReactIssue7711 as ft}from'../../utils/bugs.js';import{Focus as A,calculateActiveIndex as Ve}from'../../utils/calculate-active-index.js';import{disposables as Le}from'../../utils/disposables.js';import{sortByDomNode as Tt}from'../../utils/focus-management.js';import{match as oe}from'../../utils/match.js';import{isMobile as xt}from'../../utils/platform.js';import{RenderFeatures as Be,forwardRefWithAs as re,mergeProps as xe,useRender as ie}from'../../utils/render.js';import{useDescribedBy as gt}from'../description/description.js';import{Keys as F}from'../keyboard.js';import{Label as vt,useLabelledBy as ge,useLabels as yt}from'../label/label.js';import{MouseButton as we}from'../mouse.js';import{Portal as Ot}from'../portal/portal.js';var Ct=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(Ct||{}),Et=(e=>(e[e.Single=0]="Single",e[e.Multi=1]="Multi",e))(Et||{}),St=(o=>(o[o.Pointer=0]="Pointer",o[o.Focus=1]="Focus",o[o.Other=2]="Other",o))(St||{}),It=(l=>(l[l.OpenCombobox=0]="OpenCombobox",l[l.CloseCombobox=1]="CloseCombobox",l[l.GoToOption=2]="GoToOption",l[l.SetTyping=3]="SetTyping",l[l.RegisterOption=4]="RegisterOption",l[l.UnregisterOption=5]="UnregisterOption",l[l.SetActivationTrigger=6]="SetActivationTrigger",l[l.UpdateVirtualConfiguration=7]="UpdateVirtualConfiguration",l[l.SetInputElement=8]="SetInputElement",l[l.SetButtonElement=9]="SetButtonElement",l[l.SetOptionsElement=10]="SetOptionsElement",l))(It||{});function ve(t,n=e=>e){let e=t.activeOptionIndex!==null?t.options[t.activeOptionIndex]:null,o=n(t.options.slice()),f=o.length>0&&o[0].dataRef.current.order!==null?o.sort((m,d)=>m.dataRef.current.order-d.dataRef.current.order):Tt(o,m=>m.dataRef.current.domRef.current),b=e?f.indexOf(e):null;return b===-1&&(b=null),{options:f,activeOptionIndex:b}}let Pt={[1](t){var n;return(n=t.dataRef.current)!=null&&n.disabled||t.comboboxState===1?t:{...t,activeOptionIndex:null,comboboxState:1,isTyping:!1,activationTrigger:2,__demoMode:!1}},[0](t){var n,e;if((n=t.dataRef.current)!=null&&n.disabled||t.comboboxState===0)return t;if((e=t.dataRef.current)!=null&&e.value){let o=t.dataRef.current.calculateIndex(t.dataRef.current.value);if(o!==-1)return{...t,activeOptionIndex:o,comboboxState:0,__demoMode:!1}}return{...t,comboboxState:0,__demoMode:!1}},[3](t,n){return t.isTyping===n.isTyping?t:{...t,isTyping:n.isTyping}},[2](t,n){var b,m,d,x;if((b=t.dataRef.current)!=null&&b.disabled||t.optionsElement&&!((m=t.dataRef.current)!=null&&m.optionsPropsRef.current.static)&&t.comboboxState===1)return t;if(t.virtual){let{options:i,disabled:r}=t.virtual,l=n.focus===A.Specific?n.idx:Ve(n,{resolveItems:()=>i,resolveActiveIndex:()=>{var R,s;return(s=(R=t.activeOptionIndex)!=null?R:i.findIndex(D=>!r(D)))!=null?s:null},resolveDisabled:r,resolveId(){throw new Error("Function not implemented.")}}),S=(d=n.trigger)!=null?d:2;return t.activeOptionIndex===l&&t.activationTrigger===S?t:{...t,activeOptionIndex:l,activationTrigger:S,isTyping:!1,__demoMode:!1}}let e=ve(t);if(e.activeOptionIndex===null){let i=e.options.findIndex(r=>!r.dataRef.current.disabled);i!==-1&&(e.activeOptionIndex=i)}let o=n.focus===A.Specific?n.idx:Ve(n,{resolveItems:()=>e.options,resolveActiveIndex:()=>e.activeOptionIndex,resolveId:i=>i.id,resolveDisabled:i=>i.dataRef.current.disabled}),f=(x=n.trigger)!=null?x:2;return t.activeOptionIndex===o&&t.activationTrigger===f?t:{...t,...e,isTyping:!1,activeOptionIndex:o,activationTrigger:f,__demoMode:!1}},[4]:(t,n)=>{var b,m,d;if((b=t.dataRef.current)!=null&&b.virtual)return{...t,options:[...t.options,n.payload]};let e=n.payload,o=ve(t,x=>(x.push(e),x));t.activeOptionIndex===null&&(m=t.dataRef.current)!=null&&m.isSelected(n.payload.dataRef.current.value)&&(o.activeOptionIndex=o.options.indexOf(e));let f={...t,...o,activationTrigger:2};return(d=t.dataRef.current)!=null&&d.__demoMode&&t.dataRef.current.value===void 0&&(f.activeOptionIndex=0),f},[5]:(t,n)=>{var o;if((o=t.dataRef.current)!=null&&o.virtual)return{...t,options:t.options.filter(f=>f.id!==n.id)};let e=ve(t,f=>{let b=f.findIndex(m=>m.id===n.id);return b!==-1&&f.splice(b,1),f});return{...t,...e,activationTrigger:2}},[6]:(t,n)=>t.activationTrigger===n.trigger?t:{...t,activationTrigger:n.trigger},[7]:(t,n)=>{var o,f;if(t.virtual===null)return{...t,virtual:{options:n.options,disabled:(o=n.disabled)!=null?o:()=>!1}};if(t.virtual.options===n.options&&t.virtual.disabled===n.disabled)return t;let e=t.activeOptionIndex;if(t.activeOptionIndex!==null){let b=n.options.indexOf(t.virtual.options[t.activeOptionIndex]);b!==-1?e=b:e=null}return{...t,activeOptionIndex:e,virtual:{options:n.options,disabled:(f=n.disabled)!=null?f:()=>!1}}},[8]:(t,n)=>t.inputElement===n.element?t:{...t,inputElement:n.element},[9]:(t,n)=>t.buttonElement===n.element?t:{...t,buttonElement:n.element},[10]:(t,n)=>t.optionsElement===n.element?t:{...t,optionsElement:n.element}},ye=ce(null);ye.displayName="ComboboxActionsContext";function le(t){let n=Te(ye);if(n===null){let e=new Error(`<${t} /> is missing a parent <Combobox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(e,le),e}return n}let Ne=ce(null);function At(t){let n=ne("VirtualProvider"),{options:e}=n.virtual,[o,f]=W(()=>{let i=n.optionsElement;if(!i)return[0,0];let r=window.getComputedStyle(i);return[parseFloat(r.paddingBlockStart||r.paddingTop),parseFloat(r.paddingBlockEnd||r.paddingBottom)]},[n.optionsElement]),b=He({enabled:e.length!==0,scrollPaddingStart:o,scrollPaddingEnd:f,count:e.length,estimateSize(){return 40},getScrollElement(){return n.optionsElement},overscan:12}),[m,d]=Ae(0);q(()=>{d(i=>i+1)},[e]);let x=b.getVirtualItems();return x.length===0?null:M.createElement(Ne.Provider,{value:b},M.createElement("div",{style:{position:"relative",width:"100%",height:`${b.getTotalSize()}px`},ref:i=>{i&&n.activationTrigger!==0&&n.activeOptionIndex!==null&&e.length>n.activeOptionIndex&&b.scrollToIndex(n.activeOptionIndex)}},x.map(i=>{var r;return M.createElement(Pe,{key:i.key},M.cloneElement((r=t.children)==null?void 0:r.call(t,{...t.slot,option:e[i.index]}),{key:`${m}-${i.key}`,"data-index":i.index,"aria-setsize":e.length,"aria-posinset":i.index+1,style:{position:"absolute",top:0,left:0,transform:`translateY(${i.start}px)`,overflowAnchor:"none"}}))})))}let ae=ce(null);ae.displayName="ComboboxDataContext";function ne(t){let n=Te(ae);if(n===null){let e=new Error(`<${t} /> is missing a parent <Combobox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(e,ne),e}return n}function Rt(t,n){return oe(n.type,Pt,t,n)}let _t=Pe;function ht(t,n){var Oe,Ce;let e=rt(),{value:o,defaultValue:f,onChange:b,form:m,name:d,by:x,disabled:i=e||!1,onClose:r,__demoMode:l=!1,multiple:S=!1,immediate:R=!1,virtual:s=null,nullable:D,...B}=t,_=We(f),[I=S?[]:void 0,O]=je(o,b,_),[P,g]=Ge(Rt,{dataRef:Ue(),comboboxState:l?0:1,isTyping:!1,options:[],virtual:s?{options:s.options,disabled:(Oe=s.disabled)!=null?Oe:()=>!1}:null,activeOptionIndex:null,activationTrigger:2,inputElement:null,buttonElement:null,optionsElement:null,__demoMode:l}),V=ee(!1),w=ee({static:!1,hold:!1}),T=Ke(x),$=c(p=>s?x===null?s.options.indexOf(p):s.options.findIndex(v=>T(v,p)):P.options.findIndex(v=>T(v.dataRef.current.value,p))),K=fe(p=>oe(u.mode,{[1]:()=>I.some(v=>T(v,p)),[0]:()=>T(I,p)}),[I]),Z=c(p=>P.activeOptionIndex===$(p)),u=W(()=>({...P,immediate:R,optionsPropsRef:w,value:I,defaultValue:_,disabled:i,mode:S?1:0,virtual:s?P.virtual:null,get activeOptionIndex(){if(V.current&&P.activeOptionIndex===null&&(s?s.options.length>0:P.options.length>0)){if(s){let v=s.options.findIndex(z=>{var se,Ee;return!((Ee=(se=s.disabled)==null?void 0:se.call(s,z))!=null&&Ee)});if(v!==-1)return v}let p=P.options.findIndex(v=>!v.dataRef.current.disabled);if(p!==-1)return p}return P.activeOptionIndex},calculateIndex:$,compare:T,isSelected:K,isActive:Z}),[I,_,i,S,l,P,s]);q(()=>{var p;s&&g({type:7,options:s.options,disabled:(p=s.disabled)!=null?p:null})},[s,s==null?void 0:s.options,s==null?void 0:s.disabled]),q(()=>{P.dataRef.current=u},[u]);let X=u.comboboxState===0;Ye(X,[u.buttonElement,u.inputElement,u.optionsElement],()=>C.closeCombobox());let N=W(()=>{var p,v,z;return{open:u.comboboxState===0,disabled:i,activeIndex:u.activeOptionIndex,activeOption:u.activeOptionIndex===null?null:u.virtual?u.virtual.options[(p=u.activeOptionIndex)!=null?p:0]:(z=(v=u.options[u.activeOptionIndex])==null?void 0:v.dataRef.current.value)!=null?z:null,value:I}},[u,i,I]),U=c(()=>{if(u.activeOptionIndex!==null){if(C.setIsTyping(!1),u.virtual)j(u.virtual.options[u.activeOptionIndex]);else{let{dataRef:p}=u.options[u.activeOptionIndex];j(p.current.value)}C.goToOption(A.Specific,u.activeOptionIndex)}}),y=c(()=>{g({type:0}),V.current=!0}),G=c(()=>{g({type:1}),V.current=!1,r==null||r()}),ue=c(p=>{g({type:3,isTyping:p})}),Y=c((p,v,z)=>(V.current=!1,p===A.Specific?g({type:2,focus:A.Specific,idx:v,trigger:z}):g({type:2,focus:p,trigger:z}))),Q=c((p,v)=>(g({type:4,payload:{id:p,dataRef:v}}),()=>{u.isActive(v.current.value)&&(V.current=!0),g({type:5,id:p})})),j=c(p=>oe(u.mode,{[0](){return O==null?void 0:O(p)},[1](){let v=u.value.slice(),z=v.findIndex(se=>T(se,p));return z===-1?v.push(p):v.splice(z,1),O==null?void 0:O(v)}})),h=c(p=>{g({type:6,trigger:p})}),pe=c(p=>{g({type:8,element:p})}),a=c(p=>{g({type:9,element:p})}),k=c(p=>{g({type:10,element:p})}),C=W(()=>({onChange:j,registerOption:Q,goToOption:Y,setIsTyping:ue,closeCombobox:G,openCombobox:y,setActivationTrigger:h,selectActiveOption:U,setInputElement:pe,setButtonElement:a,setOptionsElement:k}),[]),[H,E]=yt(),L=n===null?{}:{ref:n},J=fe(()=>{if(_!==void 0)return O==null?void 0:O(_)},[O,_]),ke=ie();return M.createElement(E,{value:H,props:{htmlFor:(Ce=u.inputElement)==null?void 0:Ce.id},slot:{open:u.comboboxState===0,disabled:i}},M.createElement(it,null,M.createElement(ye.Provider,{value:C},M.createElement(ae.Provider,{value:u},M.createElement(mt,{value:oe(u.comboboxState,{[0]:me.Open,[1]:me.Closed})},d!=null&&M.createElement(st,{disabled:i,data:I!=null?{[d]:I}:{},form:m,onReset:J}),ke({ourProps:L,theirProps:B,slot:N,defaultTag:_t,name:"Combobox"}))))))}let Mt="input";function Dt(t,n){var Y,Q,j,h,pe;let e=ne("Combobox.Input"),o=le("Combobox.Input"),f=de(),b=bt(),{id:m=b||`headlessui-combobox-input-${f}`,onChange:d,displayValue:x,disabled:i=e.disabled||!1,autoFocus:r=!1,type:l="text",...S}=t,R=ee(null),s=be(R,n,ut(),o.setInputElement),D=_e(e.inputElement),B=$e(),_=c(()=>{o.onChange(null),e.optionsElement&&(e.optionsElement.scrollTop=0),o.goToOption(A.Nothing)}),I=W(()=>{var a;return typeof x=="function"&&e.value!==void 0?(a=x(e.value))!=null?a:"":typeof e.value=="string"?e.value:""},[e.value,x]);Me(([a,k],[C,H])=>{if(e.isTyping)return;let E=R.current;E&&((H===0&&k===1||a!==C)&&(E.value=a),requestAnimationFrame(()=>{if(e.isTyping||!E||(D==null?void 0:D.activeElement)!==E)return;let{selectionStart:L,selectionEnd:J}=E;Math.abs((J!=null?J:0)-(L!=null?L:0))===0&&L===0&&E.setSelectionRange(E.value.length,E.value.length)}))},[I,e.comboboxState,D,e.isTyping]),Me(([a],[k])=>{if(a===0&&k===1){if(e.isTyping)return;let C=R.current;if(!C)return;let H=C.value,{selectionStart:E,selectionEnd:L,selectionDirection:J}=C;C.value="",C.value=H,J!==null?C.setSelectionRange(E,L,J):C.setSelectionRange(E,L)}},[e.comboboxState]);let O=ee(!1),P=c(()=>{O.current=!0}),g=c(()=>{B.nextFrame(()=>{O.current=!1})}),V=c(a=>{switch(o.setIsTyping(!0),a.key){case F.Enter:if(e.comboboxState!==0||O.current)return;if(a.preventDefault(),a.stopPropagation(),e.activeOptionIndex===null){o.closeCombobox();return}o.selectActiveOption(),e.mode===0&&o.closeCombobox();break;case F.ArrowDown:return a.preventDefault(),a.stopPropagation(),oe(e.comboboxState,{[0]:()=>o.goToOption(A.Next),[1]:()=>o.openCombobox()});case F.ArrowUp:return a.preventDefault(),a.stopPropagation(),oe(e.comboboxState,{[0]:()=>o.goToOption(A.Previous),[1]:()=>{te(()=>o.openCombobox()),e.value||o.goToOption(A.Last)}});case F.Home:if(a.shiftKey)break;return a.preventDefault(),a.stopPropagation(),o.goToOption(A.First);case F.PageUp:return a.preventDefault(),a.stopPropagation(),o.goToOption(A.First);case F.End:if(a.shiftKey)break;return a.preventDefault(),a.stopPropagation(),o.goToOption(A.Last);case F.PageDown:return a.preventDefault(),a.stopPropagation(),o.goToOption(A.Last);case F.Escape:return e.comboboxState!==0?void 0:(a.preventDefault(),e.optionsElement&&!e.optionsPropsRef.current.static&&a.stopPropagation(),e.mode===0&&e.value===null&&_(),o.closeCombobox());case F.Tab:if(e.comboboxState!==0)return;e.mode===0&&e.activationTrigger!==1&&o.selectActiveOption(),o.closeCombobox();break}}),w=c(a=>{d==null||d(a),e.mode===0&&a.target.value===""&&_(),o.openCombobox()}),T=c(a=>{var C,H,E;let k=(C=a.relatedTarget)!=null?C:Fe.find(L=>L!==a.currentTarget);if(!((H=e.optionsElement)!=null&&H.contains(k))&&!((E=e.buttonElement)!=null&&E.contains(k))&&e.comboboxState===0)return a.preventDefault(),e.mode===0&&e.value===null&&_(),o.closeCombobox()}),$=c(a=>{var C,H,E;let k=(C=a.relatedTarget)!=null?C:Fe.find(L=>L!==a.currentTarget);(H=e.buttonElement)!=null&&H.contains(k)||(E=e.optionsElement)!=null&&E.contains(k)||e.disabled||e.immediate&&e.comboboxState!==0&&B.microTask(()=>{te(()=>o.openCombobox()),o.setActivationTrigger(1)})}),K=ge(),Z=gt(),{isFocused:u,focusProps:X}=Se({autoFocus:r}),{isHovered:N,hoverProps:U}=Ie({isDisabled:i}),y=W(()=>({open:e.comboboxState===0,disabled:i,hover:N,focus:u,autofocus:r}),[e,N,u,r,i]),G=xe({ref:s,id:m,role:"combobox",type:l,"aria-controls":(Y=e.optionsElement)==null?void 0:Y.id,"aria-expanded":e.comboboxState===0,"aria-activedescendant":e.activeOptionIndex===null?void 0:e.virtual?(Q=e.options.find(a=>!a.dataRef.current.disabled&&e.compare(a.dataRef.current.value,e.virtual.options[e.activeOptionIndex])))==null?void 0:Q.id:(j=e.options[e.activeOptionIndex])==null?void 0:j.id,"aria-labelledby":K,"aria-describedby":Z,"aria-autocomplete":"list",defaultValue:(pe=(h=t.defaultValue)!=null?h:e.defaultValue!==void 0?x==null?void 0:x(e.defaultValue):null)!=null?pe:e.defaultValue,disabled:i||void 0,autoFocus:r,onCompositionStart:P,onCompositionEnd:g,onKeyDown:V,onChange:w,onFocus:$,onBlur:T},X,U);return ie()({ourProps:G,theirProps:S,slot:y,defaultTag:Mt,name:"Combobox.Input"})}let Ft="button";function Vt(t,n){var w;let e=ne("Combobox.Button"),o=le("Combobox.Button"),f=be(n,o.setButtonElement),b=de(),{id:m=`headlessui-combobox-button-${b}`,disabled:d=e.disabled||!1,autoFocus:x=!1,...i}=t,r=he(e.inputElement),l=c(T=>{switch(T.key){case F.Space:case F.Enter:T.preventDefault(),T.stopPropagation(),e.comboboxState===1&&te(()=>o.openCombobox()),r();return;case F.ArrowDown:T.preventDefault(),T.stopPropagation(),e.comboboxState===1&&(te(()=>o.openCombobox()),e.value||o.goToOption(A.First)),r();return;case F.ArrowUp:T.preventDefault(),T.stopPropagation(),e.comboboxState===1&&(te(()=>o.openCombobox()),e.value||o.goToOption(A.Last)),r();return;case F.Escape:if(e.comboboxState!==0)return;T.preventDefault(),e.optionsElement&&!e.optionsPropsRef.current.static&&T.stopPropagation(),te(()=>o.closeCombobox()),r();return;default:return}}),S=c(T=>{T.preventDefault(),!ft(T.currentTarget)&&(T.button===we.Left&&(e.comboboxState===0?o.closeCombobox():o.openCombobox()),r())}),R=ge([m]),{isFocusVisible:s,focusProps:D}=Se({autoFocus:x}),{isHovered:B,hoverProps:_}=Ie({isDisabled:d}),{pressed:I,pressProps:O}=ze({disabled:d}),P=W(()=>({open:e.comboboxState===0,active:I||e.comboboxState===0,disabled:d,value:e.value,hover:B,focus:s}),[e,B,s,I,d]),g=xe({ref:f,id:m,type:Qe(t,e.buttonElement),tabIndex:-1,"aria-haspopup":"listbox","aria-controls":(w=e.optionsElement)==null?void 0:w.id,"aria-expanded":e.comboboxState===0,"aria-labelledby":R,disabled:d||void 0,autoFocus:x,onMouseDown:S,onKeyDown:l},D,_,O);return ie()({ourProps:g,theirProps:i,slot:P,defaultTag:Ft,name:"Combobox.Button"})}let Lt="div",Bt=Be.RenderStrategy|Be.Static;function wt(t,n){var Y,Q,j;let e=de(),{id:o=`headlessui-combobox-options-${e}`,hold:f=!1,anchor:b,portal:m=!1,modal:d=!0,transition:x=!1,...i}=t,r=ne("Combobox.Options"),l=le("Combobox.Options"),S=pt(b);S&&(m=!0);let[R,s]=lt(S),[D,B]=Ae(null),_=at(),I=be(n,S?R:null,l.setOptionsElement,B),O=_e(r.optionsElement),P=ct(),[g,V]=ot(x,D,P!==null?(P&me.Open)===me.Open:r.comboboxState===0);qe(g,r.inputElement,l.closeCombobox);let w=r.__demoMode?!1:d&&r.comboboxState===0;Ze(w,O);let T=r.__demoMode?!1:d&&r.comboboxState===0;Xe(T,{allowed:fe(()=>[r.inputElement,r.buttonElement,r.optionsElement],[r.inputElement,r.buttonElement,r.optionsElement])}),q(()=>{var h;r.optionsPropsRef.current.static=(h=t.static)!=null?h:!1},[r.optionsPropsRef,t.static]),q(()=>{r.optionsPropsRef.current.hold=f},[r.optionsPropsRef,f]),nt(r.comboboxState===0,{container:r.optionsElement,accept(h){return h.getAttribute("role")==="option"?NodeFilter.FILTER_REJECT:h.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(h){h.setAttribute("role","none")}});let $=ge([(Y=r.buttonElement)==null?void 0:Y.id]),K=W(()=>({open:r.comboboxState===0,option:void 0}),[r.comboboxState]),Z=c(()=>{l.setActivationTrigger(0)}),u=c(h=>{h.preventDefault(),l.setActivationTrigger(0)}),X=xe(S?_():{},{"aria-labelledby":$,role:"listbox","aria-multiselectable":r.mode===1?!0:void 0,id:o,ref:I,style:{...i.style,...s,"--input-width":Re(r.inputElement,!0).width,"--button-width":Re(r.buttonElement,!0).width},onWheel:r.activationTrigger===0?void 0:Z,onMouseDown:u,...tt(V)}),N=g&&r.comboboxState===1,U=De(N,(Q=r.virtual)==null?void 0:Q.options),y=De(N,r.value),G=c(h=>r.compare(y,h));if(r.virtual){if(U===void 0)throw new Error("Missing `options` in virtual mode");Object.assign(i,{children:M.createElement(ae.Provider,{value:U!==r.virtual.options?{...r,virtual:{...r.virtual,options:U}}:r},M.createElement(At,{slot:K},i.children))})}let ue=ie();return M.createElement(Ot,{enabled:m?t.static||g:!1},M.createElement(ae.Provider,{value:r.mode===1?r:{...r,isSelected:G}},ue({ourProps:X,theirProps:{...i,children:M.createElement(dt,{freeze:N},typeof i.children=="function"?(j=i.children)==null?void 0:j.call(i,K):i.children)},slot:K,defaultTag:Lt,features:Bt,visible:g,name:"Combobox.Options"})))}let Nt="div";function kt(t,n){var u,X,N,U;let e=ne("Combobox.Option"),o=le("Combobox.Option"),f=de(),{id:b=`headlessui-combobox-option-${f}`,value:m,disabled:d=(N=(X=(u=e.virtual)==null?void 0:u.disabled)==null?void 0:X.call(u,m))!=null?N:!1,order:x=null,...i}=t,r=he(e.inputElement),l=e.virtual?e.activeOptionIndex===e.calculateIndex(m):e.activeOptionIndex===null?!1:((U=e.options[e.activeOptionIndex])==null?void 0:U.id)===b,S=e.isSelected(m),R=ee(null),s=Je({disabled:d,value:m,domRef:R,order:x}),D=Te(Ne),B=be(n,R,D?D.measureElement:null),_=c(()=>{o.setIsTyping(!1),o.onChange(m)});q(()=>o.registerOption(b,s),[s,b]);let I=ee(!(e.virtual||e.__demoMode));q(()=>{if(!e.virtual&&!e.__demoMode)return Le().requestAnimationFrame(()=>{I.current=!0})},[e.virtual,e.__demoMode]),q(()=>{if(I.current&&e.comboboxState===0&&l&&e.activationTrigger!==0)return Le().requestAnimationFrame(()=>{var y,G;(G=(y=R.current)==null?void 0:y.scrollIntoView)==null||G.call(y,{block:"nearest"})})},[R,l,e.comboboxState,e.activationTrigger,e.activeOptionIndex]);let O=c(y=>{y.preventDefault(),y.button===we.Left&&(d||(_(),xt()||requestAnimationFrame(()=>r()),e.mode===0&&o.closeCombobox()))}),P=c(()=>{if(d)return o.goToOption(A.Nothing);let y=e.calculateIndex(m);o.goToOption(A.Specific,y)}),g=et(),V=c(y=>g.update(y)),w=c(y=>{if(!g.wasMoved(y)||d||l)return;let G=e.calculateIndex(m);o.goToOption(A.Specific,G,0)}),T=c(y=>{g.wasMoved(y)&&(d||l&&(e.optionsPropsRef.current.hold||o.goToOption(A.Nothing)))}),$=W(()=>({active:l,focus:l,selected:S,disabled:d}),[l,S,d]),K={id:b,ref:B,role:"option",tabIndex:d===!0?void 0:-1,"aria-disabled":d===!0?!0:void 0,"aria-selected":S,disabled:void 0,onMouseDown:O,onFocus:P,onPointerEnter:V,onMouseEnter:V,onPointerMove:w,onMouseMove:w,onPointerLeave:T,onMouseLeave:T};return ie()({ourProps:K,theirProps:i,slot:$,defaultTag:Nt,name:"Combobox.Option"})}let Ht=re(ht),Ut=re(Vt),Gt=re(Dt),zt=vt,Kt=re(wt),jt=re(kt),Ho=Object.assign(Ht,{Input:Gt,Button:Ut,Label:zt,Options:Kt,Option:jt});export{Ho as Combobox,Ut as ComboboxButton,Gt as ComboboxInput,zt as ComboboxLabel,jt as ComboboxOption,Kt as ComboboxOptions};
