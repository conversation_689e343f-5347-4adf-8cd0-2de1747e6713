"use strict";var e=require("tslib"),r=require("react"),t=require("../../../lib/tremorTwMerge.cjs"),l=require("../../../lib/utils.cjs");function a(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var s=a(r);const i=l.makeClassName("TableCell"),c=s.default.forwardRef(((r,l)=>{const{children:a,className:c}=r,u=e.__rest(r,["children","className"]);return s.default.createElement(s.default.Fragment,null,s.default.createElement("td",Object.assign({ref:l,className:t.tremorTwMerge(i("root"),"align-middle whitespace-nowrap text-left p-4",c)},u),a))}));c.displayName="TableCell",module.exports=c;
