'use client';
"use strict";var e=require("tslib"),t=require("react"),a=require("recharts"),r=require("../common/ChartLegend.cjs"),o=require("../common/ChartTooltip.cjs"),l=require("../common/NoData.cjs"),n=require("../common/utils.cjs"),i=require("../../../lib/constants.cjs"),s=require("../../../lib/theme.cjs"),d=require("../../../lib/tremorTwMerge.cjs"),c=require("../../../lib/utils.cjs");function u(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var m=u(t);const k=m.default.forwardRef(((u,k)=>{const{data:y=[],categories:f=[],index:p,colors:g=s.themeColorRange,valueFormatter:h=c.defaultValueFormatter,startEndOnly:v=!1,showXAxis:x=!0,showYAxis:L=!0,yAxisWidth:b=56,intervalType:C="equidistantPreserveStart",animationDuration:T=900,showAnimation:w=!1,showTooltip:A=!0,showLegend:E=!0,showGridLines:N=!0,autoMinValue:K=!1,curveType:j="linear",minValue:D,maxValue:O,connectNulls:q=!1,allowDecimals:V=!0,noDataText:M,className:F,onValueChange:G,enableLegendSlider:S=!1,customTooltip:W,rotateLabelX:P,padding:B=(x||L?{left:20,right:20}:{left:0,right:0}),tickGap:X=5,xAxisLabel:Y,yAxisLabel:R}=u,_=e.__rest(u,["data","categories","index","colors","valueFormatter","startEndOnly","showXAxis","showYAxis","yAxisWidth","intervalType","animationDuration","showAnimation","showTooltip","showLegend","showGridLines","autoMinValue","curveType","minValue","maxValue","connectNulls","allowDecimals","noDataText","className","onValueChange","enableLegendSlider","customTooltip","rotateLabelX","padding","tickGap","xAxisLabel","yAxisLabel"]),z=W,[H,I]=t.useState(60),[J,Q]=t.useState(void 0),[U,Z]=t.useState(void 0),$=n.constructCategoryColors(f,g),ee=n.getYAxisDomain(K,D,O),te=!!G;function ae(e){te&&(e===U&&!J||n.hasOnlyOneValueForThisKey(y,e)&&J&&J.dataKey===e?(Z(void 0),null==G||G(null)):(Z(e),null==G||G({eventType:"category",categoryClicked:e})),Q(void 0))}return m.default.createElement("div",Object.assign({ref:k,className:d.tremorTwMerge("w-full h-80",F)},_),m.default.createElement(a.ResponsiveContainer,{className:"h-full w-full"},(null==y?void 0:y.length)?m.default.createElement(a.LineChart,{data:y,onClick:te&&(U||J)?()=>{Q(void 0),Z(void 0),null==G||G(null)}:void 0,margin:{bottom:Y?30:void 0,left:R?20:void 0,right:R?5:void 0,top:5}},N?m.default.createElement(a.CartesianGrid,{className:d.tremorTwMerge("stroke-1","stroke-tremor-border","dark:stroke-dark-tremor-border"),horizontal:!0,vertical:!1}):null,m.default.createElement(a.XAxis,{padding:B,hide:!x,dataKey:p,interval:v?"preserveStartEnd":C,tick:{transform:"translate(0, 6)"},ticks:v?[y[0][p],y[y.length-1][p]]:void 0,fill:"",stroke:"",className:d.tremorTwMerge("text-tremor-label","fill-tremor-content","dark:fill-dark-tremor-content"),tickLine:!1,axisLine:!1,minTickGap:X,angle:null==P?void 0:P.angle,dy:null==P?void 0:P.verticalShift,height:null==P?void 0:P.xAxisHeight},Y&&m.default.createElement(a.Label,{position:"insideBottom",offset:-20,className:"fill-tremor-content-emphasis text-tremor-default font-medium dark:fill-dark-tremor-content-emphasis"},Y)),m.default.createElement(a.YAxis,{width:b,hide:!L,axisLine:!1,tickLine:!1,type:"number",domain:ee,tick:{transform:"translate(-3, 0)"},fill:"",stroke:"",className:d.tremorTwMerge("text-tremor-label","fill-tremor-content","dark:fill-dark-tremor-content"),tickFormatter:h,allowDecimals:V},R&&m.default.createElement(a.Label,{position:"insideLeft",style:{textAnchor:"middle"},angle:-90,offset:-15,className:"fill-tremor-content-emphasis text-tremor-default font-medium dark:fill-dark-tremor-content-emphasis"},R)),m.default.createElement(a.Tooltip,{wrapperStyle:{outline:"none"},isAnimationActive:!1,cursor:{stroke:"#d1d5db",strokeWidth:1},content:A?({active:e,payload:t,label:a})=>z?m.default.createElement(z,{payload:null==t?void 0:t.map((e=>{var t;return Object.assign(Object.assign({},e),{color:null!==(t=$.get(e.dataKey))&&void 0!==t?t:i.BaseColors.Gray})})),active:e,label:a}):m.default.createElement(o.default,{active:e,payload:t,label:a,valueFormatter:h,categoryColors:$}):m.default.createElement(m.default.Fragment,null),position:{y:0}}),E?m.default.createElement(a.Legend,{verticalAlign:"top",height:H,content:({payload:e})=>r({payload:e},$,I,U,te?e=>ae(e):void 0,S)}):null,f.map((e=>{var r;return m.default.createElement(a.Line,{className:d.tremorTwMerge(c.getColorClassNames(null!==(r=$.get(e))&&void 0!==r?r:i.BaseColors.Gray,s.colorPalette.text).strokeColor),strokeOpacity:J||U&&U!==e?.3:1,activeDot:e=>{var t;const{cx:r,cy:o,stroke:l,strokeLinecap:u,strokeLinejoin:k,strokeWidth:f,dataKey:p}=e;return m.default.createElement(a.Dot,{className:d.tremorTwMerge("stroke-tremor-background dark:stroke-dark-tremor-background",G?"cursor-pointer":"",c.getColorClassNames(null!==(t=$.get(p))&&void 0!==t?t:i.BaseColors.Gray,s.colorPalette.text).fillColor),cx:r,cy:o,r:5,fill:"",stroke:l,strokeLinecap:u,strokeLinejoin:k,strokeWidth:f,onClick:(t,a)=>function(e,t){t.stopPropagation(),te&&(e.index===(null==J?void 0:J.index)&&e.dataKey===(null==J?void 0:J.dataKey)||n.hasOnlyOneValueForThisKey(y,e.dataKey)&&U&&U===e.dataKey?(Z(void 0),Q(void 0),null==G||G(null)):(Z(e.dataKey),Q({index:e.index,dataKey:e.dataKey}),null==G||G(Object.assign({eventType:"dot",categoryClicked:e.dataKey},e.payload))))}(e,a)})},dot:r=>{var o;const{stroke:l,strokeLinecap:u,strokeLinejoin:k,strokeWidth:f,cx:p,cy:g,dataKey:h,index:v}=r;return n.hasOnlyOneValueForThisKey(y,e)&&!(J||U&&U!==e)||(null==J?void 0:J.index)===v&&(null==J?void 0:J.dataKey)===e?m.default.createElement(a.Dot,{key:v,cx:p,cy:g,r:5,stroke:l,fill:"",strokeLinecap:u,strokeLinejoin:k,strokeWidth:f,className:d.tremorTwMerge("stroke-tremor-background dark:stroke-dark-tremor-background",G?"cursor-pointer":"",c.getColorClassNames(null!==(o=$.get(h))&&void 0!==o?o:i.BaseColors.Gray,s.colorPalette.text).fillColor)}):m.default.createElement(t.Fragment,{key:v})},key:e,name:e,type:j,dataKey:e,stroke:"",strokeWidth:2,strokeLinejoin:"round",strokeLinecap:"round",isAnimationActive:w,animationDuration:T,connectNulls:q})})),G?f.map((e=>m.default.createElement(a.Line,{className:d.tremorTwMerge("cursor-pointer"),strokeOpacity:0,key:e,name:e,type:j,dataKey:e,stroke:"transparent",fill:"transparent",legendType:"none",tooltipType:"none",strokeWidth:12,connectNulls:q,onClick:(e,t)=>{t.stopPropagation();const{name:a}=e;ae(a)}}))):null):m.default.createElement(l,{noDataText:M})))}));k.displayName="LineChart",module.exports=k;
