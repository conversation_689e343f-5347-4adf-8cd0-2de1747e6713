'use client';
import{__rest as t}from"tslib";import e from"react";import{ListboxOption as r}from"@headlessui/react";import{tremorTwMerge as a}from"../../../lib/tremorTwMerge.js";import{makeClassName as o}from"../../../lib/utils.js";const s=o("SelectItem"),c=e.forwardRef(((o,c)=>{const{value:m,icon:n,className:d,children:l}=o,u=t(o,["value","icon","className","children"]),i=n;return e.createElement(r,Object.assign({className:a(s("root"),"flex justify-start items-center cursor-default text-tremor-default px-2.5 py-2.5","data-[focus]:bg-tremor-background-muted  data-[focus]:text-tremor-content-strong data-[selected]:text-tremor-content-strong data-[selected]:bg-tremor-background-muted text-tremor-content-emphasis","dark:data-[focus]:bg-dark-tremor-background-muted  dark:data-[focus]:text-dark-tremor-content-strong dark:data-[selected]:text-dark-tremor-content-strong dark:data-[selected]:bg-dark-tremor-background-muted dark:text-dark-tremor-content-emphasis",d),ref:c,key:m,value:m},u),i&&e.createElement(i,{className:a(s("icon"),"flex-none w-5 h-5 mr-1.5","text-tremor-content-subtle","dark:text-dark-tremor-content-subtle")}),e.createElement("span",{className:"whitespace-nowrap truncate"},null!=l?l:m))}));c.displayName="SelectItem";export{c as default};
