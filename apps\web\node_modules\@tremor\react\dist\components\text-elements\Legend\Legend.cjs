"use strict";var e=require("tslib"),t=require("react"),r=require("../../../lib/theme.cjs"),l=require("../../../lib/tremorTwMerge.cjs"),o=require("../../../lib/utils.cjs"),n=require("../../../assets/ChevronLeftFill.cjs"),a=require("../../../assets/ChevronRightFill.cjs");function u(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var c=u(t);const s=o.makeClassName("Legend"),i=({name:e,color:t,onClick:n,activeLegend:a})=>{const u=!!n;return c.default.createElement("li",{className:l.tremorTwMerge(s("legendItem"),"group inline-flex items-center px-2 py-0.5 rounded-tremor-small transition whitespace-nowrap",u?"cursor-pointer":"cursor-default","text-tremor-content",u?"hover:bg-tremor-background-subtle":"","dark:text-dark-tremor-content",u?"dark:hover:bg-dark-tremor-background-subtle":""),onClick:r=>{r.stopPropagation(),null==n||n(e,t)}},c.default.createElement("svg",{className:l.tremorTwMerge("flex-none h-2 w-2 mr-1.5",o.getColorClassNames(t,r.colorPalette.text).textColor,a&&a!==e?"opacity-40":"opacity-100"),fill:"currentColor",viewBox:"0 0 8 8"},c.default.createElement("circle",{cx:4,cy:4,r:4})),c.default.createElement("p",{className:l.tremorTwMerge("whitespace-nowrap truncate text-tremor-default","text-tremor-content",u?"group-hover:text-tremor-content-emphasis":"","dark:text-dark-tremor-content",a&&a!==e?"opacity-40":"opacity-100",u?"dark:group-hover:text-dark-tremor-content-emphasis":"")},e))},d=({icon:e,onClick:r,disabled:o})=>{const n=e,[a,u]=c.default.useState(!1),i=c.default.useRef(null);return c.default.useEffect((()=>(a?i.current=setInterval((()=>{null==r||r()}),300):clearInterval(i.current),()=>clearInterval(i.current))),[a,r]),t.useEffect((()=>{o&&(clearInterval(i.current),u(!1))}),[o]),c.default.createElement("button",{type:"button",className:l.tremorTwMerge(s("legendSliderButton"),"w-5 group inline-flex items-center truncate rounded-tremor-small transition",o?"cursor-not-allowed":"cursor-pointer",o?"text-tremor-content-subtle":"text-tremor-content hover:text-tremor-content-emphasis hover:bg-tremor-background-subtle",o?"dark:text-dark-tremor-subtle":"dark:text-dark-tremor dark:hover:text-tremor-content-emphasis dark:hover:bg-dark-tremor-background-subtle"),disabled:o,onClick:e=>{e.stopPropagation(),null==r||r()},onMouseDown:e=>{e.stopPropagation(),u(!0)},onMouseUp:e=>{e.stopPropagation(),u(!1)}},c.default.createElement(n,{className:"w-full"}))},m=c.default.forwardRef(((o,u)=>{const{categories:m,colors:f=r.themeColorRange,className:g,onClickLegendItem:v,activeLegend:p,enableLegendSlider:k=!1}=o,b=e.__rest(o,["categories","colors","className","onClickLegendItem","activeLegend","enableLegendSlider"]),h=c.default.useRef(null),w=c.default.useRef(null),[x,E]=c.default.useState(null),[L,y]=c.default.useState(null),C=c.default.useRef(null),N=t.useCallback((()=>{const e=null==h?void 0:h.current;if(!e)return;const t=e.scrollLeft>0,r=e.scrollWidth-e.clientWidth>e.scrollLeft;E({left:t,right:r})}),[E]),I=t.useCallback((e=>{var t,r;const l=null==h?void 0:h.current,o=null==w?void 0:w.current,n=null!==(t=null==l?void 0:l.clientWidth)&&void 0!==t?t:0,a=null!==(r=null==o?void 0:o.clientWidth)&&void 0!==r?r:0;l&&k&&(l.scrollTo({left:"left"===e?l.scrollLeft-n+a:l.scrollLeft+n-a,behavior:"smooth"}),setTimeout((()=>{N()}),400))}),[k,N]);c.default.useEffect((()=>{const e=e=>{"ArrowLeft"===e?I("left"):"ArrowRight"===e&&I("right")};return L?(e(L),C.current=setInterval((()=>{e(L)}),300)):clearInterval(C.current),()=>clearInterval(C.current)}),[L,I]);const M=e=>{e.stopPropagation(),"ArrowLeft"!==e.key&&"ArrowRight"!==e.key||(e.preventDefault(),y(e.key))},T=e=>{e.stopPropagation(),y(null)};return c.default.useEffect((()=>{const e=null==h?void 0:h.current;return k&&(N(),null==e||e.addEventListener("keydown",M),null==e||e.addEventListener("keyup",T)),()=>{null==e||e.removeEventListener("keydown",M),null==e||e.removeEventListener("keyup",T)}}),[N,k]),c.default.createElement("ol",Object.assign({ref:u,className:l.tremorTwMerge(s("root"),"relative overflow-hidden",g)},b),c.default.createElement("div",{ref:h,tabIndex:0,className:l.tremorTwMerge("h-full flex",k?(null==x?void 0:x.right)||(null==x?void 0:x.left)?"pl-4 pr-12  items-center overflow-auto snap-mandatory [&::-webkit-scrollbar]:hidden [scrollbar-width:none]":"":"flex-wrap")},m.map(((e,t)=>c.default.createElement(i,{key:`item-${t}`,name:e,color:f[t%f.length],onClick:v,activeLegend:p})))),k&&((null==x?void 0:x.right)||(null==x?void 0:x.left))?c.default.createElement(c.default.Fragment,null,c.default.createElement("div",{className:l.tremorTwMerge("bg-tremor-background","dark:bg-dark-tremor-background","absolute flex top-0 pr-1 bottom-0 right-0 items-center justify-center h-full"),ref:w},c.default.createElement(d,{icon:n,onClick:()=>{y(null),I("left")},disabled:!(null==x?void 0:x.left)}),c.default.createElement(d,{icon:a,onClick:()=>{y(null),I("right")},disabled:!(null==x?void 0:x.right)}))):null)}));m.displayName="Legend",module.exports=m;
