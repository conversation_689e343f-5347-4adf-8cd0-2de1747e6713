"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("../../../lib/constants.cjs"),o=require("../../../lib/theme.cjs");require("../../../lib/tremorTwMerge.cjs");var r=require("../../../lib/utils.cjs");require("tslib"),require("react");var t=require("../../../assets/ArrowDownIcon.cjs"),s=require("../../../assets/ArrowDownRightIcon.cjs"),l=require("../../../assets/ArrowRightIcon.cjs"),a=require("../../../assets/ArrowUpIcon.cjs"),i=require("../../../assets/ArrowUpRightIcon.cjs");const n={[e.DeltaTypes.Increase]:{bgColor:r.getColorClassNames(e.BaseColors.Emerald,o.colorPalette.background).bgColor,textColor:r.getColorClassNames(e.BaseColors.Emerald,o.colorPalette.iconText).textColor,ringColor:r.getColorClassNames(e.BaseColors.Emerald,o.colorPalette.iconRing).ringColor},[e.DeltaTypes.ModerateIncrease]:{bgColor:r.getColorClassNames(e.BaseColors.Emerald,o.colorPalette.background).bgColor,textColor:r.getColorClassNames(e.BaseColors.Emerald,o.colorPalette.iconText).textColor,ringColor:r.getColorClassNames(e.BaseColors.Emerald,o.colorPalette.iconRing).ringColor},[e.DeltaTypes.Decrease]:{bgColor:r.getColorClassNames(e.BaseColors.Red,o.colorPalette.background).bgColor,textColor:r.getColorClassNames(e.BaseColors.Red,o.colorPalette.iconText).textColor,ringColor:r.getColorClassNames(e.BaseColors.Red,o.colorPalette.iconRing).ringColor},[e.DeltaTypes.ModerateDecrease]:{bgColor:r.getColorClassNames(e.BaseColors.Red,o.colorPalette.background).bgColor,textColor:r.getColorClassNames(e.BaseColors.Red,o.colorPalette.iconText).textColor,ringColor:r.getColorClassNames(e.BaseColors.Red,o.colorPalette.iconRing).ringColor},[e.DeltaTypes.Unchanged]:{bgColor:r.getColorClassNames(e.BaseColors.Orange,o.colorPalette.background).bgColor,textColor:r.getColorClassNames(e.BaseColors.Orange,o.colorPalette.iconText).textColor,ringColor:r.getColorClassNames(e.BaseColors.Orange,o.colorPalette.iconRing).ringColor}},g={[e.DeltaTypes.Increase]:a,[e.DeltaTypes.ModerateIncrease]:i,[e.DeltaTypes.Decrease]:t,[e.DeltaTypes.ModerateDecrease]:s,[e.DeltaTypes.Unchanged]:l};exports.badgeProportionsIconOnly={xs:{paddingX:"px-2",paddingY:"py-0.5",fontSize:"text-xs"},sm:{paddingX:"px-2.5",paddingY:"py-1",fontSize:"text-sm"},md:{paddingX:"px-3",paddingY:"py-1.5",fontSize:"text-md"},lg:{paddingX:"px-3.5",paddingY:"py-1.5",fontSize:"text-lg"},xl:{paddingX:"px-3.5",paddingY:"py-1.5",fontSize:"text-xl"}},exports.badgeProportionsWithText={xs:{paddingX:"px-2",paddingY:"py-0.5",fontSize:"text-xs"},sm:{paddingX:"px-2.5",paddingY:"py-0.5",fontSize:"text-sm"},md:{paddingX:"px-3",paddingY:"py-0.5",fontSize:"text-md"},lg:{paddingX:"px-3.5",paddingY:"py-0.5",fontSize:"text-lg"},xl:{paddingX:"px-4",paddingY:"py-1",fontSize:"text-xl"}},exports.colors=n,exports.deltaIcons=g,exports.iconSizes={xs:{height:"h-4",width:"w-4"},sm:{height:"h-4",width:"w-4"},md:{height:"h-4",width:"w-4"},lg:{height:"h-5",width:"w-5"},xl:{height:"h-6",width:"w-6"}};
