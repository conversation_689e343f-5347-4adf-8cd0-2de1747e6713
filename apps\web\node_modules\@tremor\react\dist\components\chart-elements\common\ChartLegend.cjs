"use strict";var e=require("react"),t=require("../../../hooks/useOnWindowResize.cjs"),r=require("../../text-elements/Legend/Legend.cjs");function n(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var l=n(e);module.exports=({payload:n},a,u,i,c,o)=>{const s=e.useRef(null);t((()=>{var e;var t;u((t=null===(e=s.current)||void 0===e?void 0:e.clientHeight)?Number(t)+20:60)}));const d=n.filter((e=>"none"!==e.type));return l.default.createElement("div",{ref:s,className:"flex items-center justify-end"},l.default.createElement(r,{categories:d.map((e=>e.value)),colors:d.map((e=>a.get(e.value))),onClickLegendItem:c,activeLegend:i,enableLegendSlider:o}))};
