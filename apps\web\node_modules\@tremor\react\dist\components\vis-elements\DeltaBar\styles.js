import{DeltaTypes as o,BaseColors as r}from"../../../lib/constants.js";import{colorPalette as e}from"../../../lib/theme.js";import"../../../lib/tremorTwMerge.js";import{getColorClassNames as b}from"../../../lib/utils.js";const g={[o.Increase]:{bgColor:b(r.Emerald,e.background).bgColor},[o.ModerateIncrease]:{bgColor:b(r.<PERSON>,e.background).bgColor},[o.Decrease]:{bgColor:b(r<PERSON><PERSON>,e.background).bgColor},[o.ModerateDecrease]:{bgColor:b(r<PERSON><PERSON>,e.background).bgColor},[o.Unchanged]:{bgColor:b(r.Orange,e.background).bgColor}};export{g as colors};
