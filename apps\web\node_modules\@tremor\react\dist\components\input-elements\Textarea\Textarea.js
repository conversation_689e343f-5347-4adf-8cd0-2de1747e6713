'use client';
import{__rest as e}from"tslib";import{getSelectButtonColors as r,hasValue as t}from"../selectUtils.js";import a from"../../../hooks/useInternalState.js";import o,{useRef as l,useEffect as n}from"react";import{tremorTwMerge as s}from"../../../lib/tremorTwMerge.js";import{mergeRefs as d,makeClassName as u}from"../../../lib/utils.js";const m=u("Textarea"),c=o.forwardRef(((u,c)=>{const{value:i,defaultValue:p="",placeholder:f="Type...",error:h=!1,errorMessage:g,disabled:b=!1,className:x,onChange:k,onValueChange:j,autoHeight:v=!1}=u,w=e(u,["value","defaultValue","placeholder","error","errorMessage","disabled","className","onChange","onValueChange","autoHeight"]),[y,C]=a(p,i),N=l(null),T=t(y);return n((()=>{const e=N.current;if(v&&e){e.style.height="60px";const r=e.scrollHeight;e.style.height=r+"px"}}),[v,N,y]),o.createElement(o.Fragment,null,o.createElement("textarea",Object.assign({ref:d([N,c]),value:y,placeholder:f,disabled:b,className:s(m("Textarea"),"w-full flex items-center outline-none rounded-tremor-default px-3 py-2 text-tremor-default focus:ring-2 transition duration-100 border","shadow-tremor-input focus:border-tremor-brand-subtle focus:ring-tremor-brand-muted","dark:shadow-dark-tremor-input focus:dark:border-dark-tremor-brand-subtle focus:dark:ring-dark-tremor-brand-muted",r(T,b,h),b?"placeholder:text-tremor-content-subtle dark:placeholder:text-dark-tremor-content-subtle":"placeholder:text-tremor-content dark:placeholder:text-dark-tremor-content",x),"data-testid":"text-area",onChange:e=>{null==k||k(e),C(e.target.value),null==j||j(e.target.value)}},w)),h&&g?o.createElement("p",{className:s(m("errorMessage"),"text-sm text-red-500 mt-1")},g):null)}));c.displayName="Textarea";export{c as default};
