'use client';
import{__rest as e}from"tslib";import{Tab as t}from"@headlessui/react";import{colorPalette as r}from"../../../lib/theme.js";import{tremorTwMerge as o}from"../../../lib/tremorTwMerge.js";import{getColorClassNames as a,makeClassName as d}from"../../../lib/utils.js";import n,{useContext as s}from"react";import{TabVariantContext as c}from"./TabList.js";import m from"../../../contexts/BaseColorContext.js";import"../../../contexts/IndexContext.js";import"../../../contexts/RootStylesContext.js";import"../../../contexts/SelectedValueContext.js";const l=d("Tab");function i(e,t){switch(e){case"line":return o("data-[selected]:border-b-2 hover:border-b-2 border-transparent transition duration-100 -mb-px px-2 py-2","hover:border-tremor-content hover:text-tremor-content-emphasis text-tremor-content","[&:not([data-selected])]:dark:hover:border-dark-tremor-content-emphasis [&:not([data-selected])]:dark:hover:text-dark-tremor-content-emphasis [&:not([data-selected])]:dark:text-dark-tremor-content",t?a(t,r.border).selectBorderColor:["data-[selected]:border-tremor-brand data-[selected]:text-tremor-brand","data-[selected]:dark:border-dark-tremor-brand data-[selected]:dark:text-dark-tremor-brand"]);case"solid":return o("border-transparent border rounded-tremor-small px-2.5 py-1","data-[selected]:border-tremor-border data-[selected]:bg-tremor-background data-[selected]:shadow-tremor-input [&:not([data-selected])]:hover:text-tremor-content-emphasis data-[selected]:text-tremor-brand [&:not([data-selected])]:text-tremor-content","dark:data-[selected]:border-dark-tremor-border dark:data-[selected]:bg-dark-tremor-background dark:data-[selected]:shadow-dark-tremor-input dark:[&:not([data-selected])]:hover:text-dark-tremor-content-emphasis dark:data-[selected]:text-dark-tremor-brand dark:[&:not([data-selected])]:text-dark-tremor-content",t?a(t,r.text).selectTextColor:"text-tremor-content dark:text-dark-tremor-content")}}const b=n.forwardRef(((d,b)=>{const{icon:x,className:p,children:k}=d,u=e(d,["icon","className","children"]),h=s(c),f=s(m),j=x;return n.createElement(t,Object.assign({ref:b,className:o(l("root"),"flex whitespace-nowrap truncate max-w-xs outline-none data-focus-visible:ring text-tremor-default transition duration-100",i(h,f),p,f&&a(f,r.text).selectTextColor)},u),j?n.createElement(j,{className:o(l("icon"),"flex-none h-5 w-5",k?"mr-2":"")}):null,k?n.createElement("span",null,k):null)}));b.displayName="Tab";export{b as default};
