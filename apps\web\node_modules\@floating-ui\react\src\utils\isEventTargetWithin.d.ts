/**
 * Check whether the event.target is within the provided node. Uses event.composedPath if available for custom element support.
 *
 * @param event The event whose target/composedPath to check
 * @param node The node to check against
 * @returns Whether the event.target/composedPath is within the node.
 */
export declare function isEventTargetWithin(event: FocusEvent | MouseEvent, node: Node | null | undefined): boolean;
