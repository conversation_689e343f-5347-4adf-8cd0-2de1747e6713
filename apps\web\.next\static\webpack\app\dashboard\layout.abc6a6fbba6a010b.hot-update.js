"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiClient: function() { return /* binding */ apiClient; }\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _lib_mockApi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/mockApi */ \"(app-pages-browser)/./src/lib/mockApi.ts\");\n\n\n\n\nclass ApiClient {\n    setupInterceptors() {\n        // Request interceptor to add auth token\n        this.client.interceptors.request.use((config)=>{\n            const token = this.getToken();\n            if (token && !(0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.isTokenExpired)(token)) {\n                config.headers.Authorization = \"Bearer \".concat(token);\n            }\n            return config;\n        }, (error)=>{\n            return Promise.reject(error);\n        });\n        // Response interceptor for error handling\n        this.client.interceptors.response.use((response)=>{\n            return response;\n        }, (error)=>{\n            var _error_response, _error_response1;\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401) {\n                this.handleUnauthorized();\n            } else if (((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status) >= 500) {\n                sonner__WEBPACK_IMPORTED_MODULE_0__.toast.error(\"Server error. Please try again later.\");\n            } else if (error.code === \"NETWORK_ERROR\") {\n                sonner__WEBPACK_IMPORTED_MODULE_0__.toast.error(\"Network error. Please check your connection.\");\n            }\n            return Promise.reject(error);\n        });\n    }\n    getToken() {\n        if (true) {\n            return localStorage.getItem(\"auth_token\");\n        }\n        return null;\n    }\n    setToken(token) {\n        if (true) {\n            localStorage.setItem(\"auth_token\", token);\n        }\n    }\n    removeToken() {\n        if (true) {\n            localStorage.removeItem(\"auth_token\");\n            localStorage.removeItem(\"refresh_token\");\n        }\n    }\n    handleUnauthorized() {\n        this.removeToken();\n        if (true) {\n            window.location.href = \"/auth/login\";\n        }\n    }\n    // Generic request method\n    async request(config) {\n        try {\n            const response = await this.client.request(config);\n            return response.data;\n        } catch (error) {\n            var _error_response_data, _error_response;\n            const message = ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.getErrorMessage)(error);\n            throw new Error(message);\n        }\n    }\n    // Authentication methods\n    async login(credentials) {\n        console.log(\"API Client: Login called, useMockApi:\", this.useMockApi);\n        if (this.useMockApi) {\n            console.log(\"API Client: Using mock API for login\");\n            const result = await _lib_mockApi__WEBPACK_IMPORTED_MODULE_2__.mockApiClient.login(credentials);\n            this.setToken(result.token);\n            return result;\n        }\n        console.log(\"API Client: Using real API for login\");\n        const response = await this.request({\n            method: \"POST\",\n            url: \"/auth/login\",\n            data: credentials\n        });\n        if (response.success && response.data) {\n            this.setToken(response.data.token);\n            return response.data;\n        }\n        throw new Error(response.message || \"Login failed\");\n    }\n    async register(userData) {\n        if (this.useMockApi) {\n            const result = await _lib_mockApi__WEBPACK_IMPORTED_MODULE_2__.mockApiClient.register(userData);\n            this.setToken(result.token);\n            return result;\n        }\n        const response = await this.request({\n            method: \"POST\",\n            url: \"/auth/register\",\n            data: userData\n        });\n        if (response.success && response.data) {\n            this.setToken(response.data.token);\n            return response.data;\n        }\n        throw new Error(response.message || \"Registration failed\");\n    }\n    async logout() {\n        if (this.useMockApi) {\n            await _lib_mockApi__WEBPACK_IMPORTED_MODULE_2__.mockApiClient.logout();\n            this.removeToken();\n            return;\n        }\n        try {\n            await this.request({\n                method: \"POST\",\n                url: \"/auth/logout\"\n            });\n        } finally{\n            this.removeToken();\n        }\n    }\n    async getCurrentUser() {\n        if (this.useMockApi) {\n            return await _lib_mockApi__WEBPACK_IMPORTED_MODULE_2__.mockApiClient.getCurrentUser();\n        }\n        const response = await this.request({\n            method: \"GET\",\n            url: \"/auth/me\"\n        });\n        if (response.success && response.data) {\n            return response.data;\n        }\n        throw new Error(response.message || \"Failed to get user data\");\n    }\n    // User methods\n    async updateProfile(data) {\n        const response = await this.request({\n            method: \"PUT\",\n            url: \"/users/me\",\n            data\n        });\n        if (response.success && response.data) {\n            return response.data;\n        }\n        throw new Error(response.message || \"Failed to update profile\");\n    }\n    async getUserStats() {\n        const response = await this.request({\n            method: \"GET\",\n            url: \"/users/me/stats\"\n        });\n        if (response.success && response.data) {\n            return response.data;\n        }\n        throw new Error(response.message || \"Failed to get user stats\");\n    }\n    // Interview methods\n    async createInterviewSession(config) {\n        if (this.useMockApi) {\n            return await _lib_mockApi__WEBPACK_IMPORTED_MODULE_2__.mockApiClient.createInterviewSession(config);\n        }\n        const response = await this.request({\n            method: \"POST\",\n            url: \"/interviews/sessions\",\n            data: config\n        });\n        if (response.success && response.data) {\n            return response.data;\n        }\n        throw new Error(response.message || \"Failed to create interview session\");\n    }\n    async getInterviewSessions(params) {\n        if (this.useMockApi) {\n            return await _lib_mockApi__WEBPACK_IMPORTED_MODULE_2__.mockApiClient.getInterviewSessions();\n        }\n        const response = await this.request({\n            method: \"GET\",\n            url: \"/interviews/sessions\",\n            params\n        });\n        if (response.success && response.data) {\n            return response.data;\n        }\n        throw new Error(response.message || \"Failed to get interview sessions\");\n    }\n    async getInterviewSession(sessionId) {\n        const response = await this.request({\n            method: \"GET\",\n            url: \"/interviews/sessions/\".concat(sessionId)\n        });\n        if (response.success && response.data) {\n            return response.data;\n        }\n        throw new Error(response.message || \"Failed to get interview session\");\n    }\n    async startInterviewSession(sessionId) {\n        const response = await this.request({\n            method: \"PUT\",\n            url: \"/interviews/sessions/\".concat(sessionId, \"/start\")\n        });\n        if (response.success && response.data) {\n            return response.data;\n        }\n        throw new Error(response.message || \"Failed to start interview session\");\n    }\n    async submitAnswer(sessionId, data) {\n        const formData = new FormData();\n        formData.append(\"questionId\", data.questionId);\n        formData.append(\"duration\", data.duration.toString());\n        if (data.textResponse) {\n            formData.append(\"textResponse\", data.textResponse);\n        }\n        if (data.audioBlob) {\n            formData.append(\"audio\", data.audioBlob, \"answer.webm\");\n        }\n        if (data.videoBlob) {\n            formData.append(\"video\", data.videoBlob, \"answer.webm\");\n        }\n        const response = await this.request({\n            method: \"POST\",\n            url: \"/interviews/sessions/\".concat(sessionId, \"/answers\"),\n            data: formData,\n            headers: {\n                \"Content-Type\": \"multipart/form-data\"\n            }\n        });\n        if (response.success && response.data) {\n            return response.data;\n        }\n        throw new Error(response.message || \"Failed to submit answer\");\n    }\n    async completeInterviewSession(sessionId) {\n        const response = await this.request({\n            method: \"PUT\",\n            url: \"/interviews/sessions/\".concat(sessionId, \"/complete\")\n        });\n        if (response.success && response.data) {\n            return response.data;\n        }\n        throw new Error(response.message || \"Failed to complete interview session\");\n    }\n    async getSessionResults(sessionId) {\n        const response = await this.request({\n            method: \"GET\",\n            url: \"/interviews/sessions/\".concat(sessionId, \"/results\")\n        });\n        if (response.success && response.data) {\n            return response.data;\n        }\n        throw new Error(response.message || \"Failed to get session results\");\n    }\n    // Resume methods\n    async uploadResume(file) {\n        const formData = new FormData();\n        formData.append(\"resume\", file);\n        const response = await this.request({\n            method: \"POST\",\n            url: \"/resumes/upload\",\n            data: formData,\n            headers: {\n                \"Content-Type\": \"multipart/form-data\"\n            }\n        });\n        if (response.success && response.data) {\n            return response.data;\n        }\n        throw new Error(response.message || \"Failed to upload resume\");\n    }\n    async getResumes() {\n        const response = await this.request({\n            method: \"GET\",\n            url: \"/resumes\"\n        });\n        if (response.success && response.data) {\n            return response.data;\n        }\n        throw new Error(response.message || \"Failed to get resumes\");\n    }\n    async analyzeResume(resumeId) {\n        const response = await this.request({\n            method: \"POST\",\n            url: \"/resumes/\".concat(resumeId, \"/analyze\")\n        });\n        if (response.success && response.data) {\n            return response.data;\n        }\n        throw new Error(response.message || \"Failed to analyze resume\");\n    }\n    // Expert methods\n    async getExperts(params) {\n        const response = await this.request({\n            method: \"GET\",\n            url: \"/experts\",\n            params\n        });\n        if (response.success && response.data) {\n            return response.data;\n        }\n        throw new Error(response.message || \"Failed to get experts\");\n    }\n    async bookExpertSession(expertId, data) {\n        const response = await this.request({\n            method: \"POST\",\n            url: \"/experts/\".concat(expertId, \"/book\"),\n            data\n        });\n        if (response.success) {\n            return response.data;\n        }\n        throw new Error(response.message || \"Failed to book expert session\");\n    }\n    // Analytics methods\n    async getAnalytics(params) {\n        const response = await this.request({\n            method: \"GET\",\n            url: \"/analytics/user\",\n            params\n        });\n        if (response.success && response.data) {\n            return response.data;\n        }\n        throw new Error(response.message || \"Failed to get analytics\");\n    }\n    // AI methods\n    async generateQuestions(data) {\n        const response = await this.request({\n            method: \"POST\",\n            url: \"/ai/questions\",\n            data\n        });\n        if (response.success && response.data) {\n            return response.data;\n        }\n        throw new Error(response.message || \"Failed to generate questions\");\n    }\n    async analyzeAnswer(data) {\n        const response = await this.request({\n            method: \"POST\",\n            url: \"/ai/analyze-answer\",\n            data\n        });\n        if (response.success && response.data) {\n            return response.data;\n        }\n        throw new Error(response.message || \"Failed to analyze answer\");\n    }\n    async analyzeEmotion(data) {\n        const response = await this.request({\n            method: \"POST\",\n            url: \"/ai/analyze-emotion\",\n            data\n        });\n        if (response.success && response.data) {\n            return response.data;\n        }\n        throw new Error(response.message || \"Failed to analyze emotion\");\n    }\n    constructor(){\n        this.baseURL = \"http://localhost:3001\" || 0;\n        this.useMockApi =  true || 0;\n        // Debug logging (can be removed in production)\n        if (true) {\n            console.log(\"API Client: Using Mock API =\", this.useMockApi);\n        }\n        this.client = axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].create({\n            baseURL: \"\".concat(this.baseURL, \"/api\"),\n            timeout: 30000,\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        this.setupInterceptors();\n    }\n}\n// Create and export a singleton instance\nconst apiClient = new ApiClient();\n/* harmony default export */ __webpack_exports__[\"default\"] = (apiClient);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api.ts\n"));

/***/ })

});