import{__rest as m}from"tslib";import{tremorTwMerge as e}from"../../../lib/tremorTwMerge.js";import{makeClassName as s}from"../../../lib/utils.js";import t from"react";import{gridCols as r,gridColsSm as i,gridColsMd as n,gridColsLg as o}from"./styles.js";const a=s("Grid"),l=(m,e)=>m&&Object.keys(e).includes(String(m))?e[m]:"",c=t.forwardRef(((s,c)=>{const{numItems:d=1,numItemsSm:u,numItemsMd:f,numItemsLg:I,children:p,className:g}=s,b=m(s,["numItems","numItemsSm","numItemsMd","numItemsLg","children","className"]),j=l(d,r),N=l(u,i),y=l(f,n),M=l(I,o),S=e(j,N,y,M);return t.createElement("div",Object.assign({ref:c,className:e(a("root"),"grid",S,g)},b),p)}));c.displayName="Grid";export{c as default};
