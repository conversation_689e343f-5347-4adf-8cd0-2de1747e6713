import{__rest as e}from"tslib";import t from"react";import{tremorTwMerge as r}from"../../../lib/tremorTwMerge.js";import{makeClassName as o}from"../../../lib/utils.js";const a=o("TableHeaderCell"),l=t.forwardRef(((o,l)=>{const{children:s,className:m}=o,n=e(o,["children","className"]);return t.createElement(t.Fragment,null,t.createElement("th",Object.assign({ref:l,className:r(a("root"),"whitespace-nowrap text-left font-semibold top-0 px-4 py-3.5","text-tremor-content-strong","dark:text-dark-tremor-content-strong",m)},n),s))}));l.displayName="TableHeaderCell";export{l as default};
