import e from"react";import{BaseColors as r}from"../../../lib/constants.js";import{colorPalette as t}from"../../../lib/theme.js";import{tremorTwMerge as a}from"../../../lib/tremorTwMerge.js";import{getColorClassNames as o}from"../../../lib/utils.js";const m=({children:r})=>e.createElement("div",{className:a("rounded-tremor-default text-tremor-default border","bg-tremor-background shadow-tremor-dropdown border-tremor-border","dark:bg-dark-tremor-background dark:shadow-dark-tremor-dropdown dark:border-dark-tremor-border")},r),d=({value:r,name:m,color:d})=>e.createElement("div",{className:"flex items-center justify-between space-x-8"},e.createElement("div",{className:"flex items-center space-x-2"},e.createElement("span",{className:a("shrink-0 rounded-tremor-full border-2 h-3 w-3","border-tremor-background shadow-tremor-card","dark:border-dark-tremor-background dark:shadow-dark-tremor-card",o(d,t.background).bgColor)}),e.createElement("p",{className:a("text-right whitespace-nowrap","text-tremor-content","dark:text-dark-tremor-content")},m)),e.createElement("p",{className:a("font-medium tabular-nums text-right whitespace-nowrap","text-tremor-content-emphasis","dark:text-dark-tremor-content-emphasis")},r)),n=({active:t,payload:o,label:n,categoryColors:l,valueFormatter:s})=>{if(t&&o){const t=o.filter((e=>"none"!==e.type));return e.createElement(m,null,e.createElement("div",{className:a("border-tremor-border border-b px-4 py-2","dark:border-dark-tremor-border")},e.createElement("p",{className:a("font-medium","text-tremor-content-emphasis","dark:text-dark-tremor-content-emphasis")},n)),e.createElement("div",{className:a("px-4 py-2 space-y-1")},t.map((({value:t,name:a},o)=>{var m;return e.createElement(d,{key:`id-${o}`,value:s(t),name:a,color:null!==(m=l.get(a))&&void 0!==m?m:r.Blue})}))))}return null};export{m as ChartTooltipFrame,d as ChartTooltipRow,n as default};
