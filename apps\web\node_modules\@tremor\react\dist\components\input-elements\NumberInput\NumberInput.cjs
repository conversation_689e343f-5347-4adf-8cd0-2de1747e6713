'use client';
"use strict";var e=require("tslib"),t=require("react"),r=require("../../../assets/PlusIcon.cjs"),a=require("../../../assets/MinusIcon.cjs"),n=require("../../../lib/tremorTwMerge.cjs"),l=require("../../../lib/utils.cjs"),u=require("../BaseInput.cjs");function o(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var s=o(t);const d="flex mx-auto text-tremor-content-subtle dark:text-dark-tremor-content-subtle",c="cursor-pointer hover:text-tremor-content dark:hover:text-dark-tremor-content",i=s.default.forwardRef(((o,i)=>{const{onSubmit:p,enableStepper:b=!0,disabled:m,onValueChange:f,onChange:v}=o,k=e.__rest(o,["onSubmit","enableStepper","disabled","onValueChange","onChange"]),w=t.useRef(null),[g,y]=s.default.useState(!1),h=s.default.useCallback((()=>{y(!0)}),[]),x=s.default.useCallback((()=>{y(!1)}),[]),[C,D]=s.default.useState(!1),E=s.default.useCallback((()=>{D(!0)}),[]),N=s.default.useCallback((()=>{D(!1)}),[]);return s.default.createElement(u,Object.assign({type:"number",ref:l.mergeRefs([w,i]),disabled:m,makeInputClassName:l.makeClassName("NumberInput"),onKeyDown:e=>{var t;if("Enter"===e.key&&!e.ctrlKey&&!e.altKey&&!e.shiftKey){const e=null===(t=w.current)||void 0===t?void 0:t.value;null==p||p(parseFloat(null!=e?e:""))}"ArrowDown"===e.key&&h(),"ArrowUp"===e.key&&E()},onKeyUp:e=>{"ArrowDown"===e.key&&x(),"ArrowUp"===e.key&&N()},onChange:e=>{m||(null==f||f(parseFloat(e.target.value)),null==v||v(e))},stepper:b?s.default.createElement("div",{className:n.tremorTwMerge("flex justify-center align-middle")},s.default.createElement("div",{tabIndex:-1,onClick:e=>e.preventDefault(),onMouseDown:e=>e.preventDefault(),onTouchStart:e=>{e.cancelable&&e.preventDefault()},onMouseUp:()=>{var e,t;m||(null===(e=w.current)||void 0===e||e.stepDown(),null===(t=w.current)||void 0===t||t.dispatchEvent(new Event("input",{bubbles:!0})))},className:n.tremorTwMerge(!m&&c,d,"group py-[10px] px-2.5 border-l border-tremor-border dark:border-dark-tremor-border")},s.default.createElement(a,{"data-testid":"step-down",className:(g?"scale-95":"")+" h-4 w-4 duration-75 transition group-active:scale-95"})),s.default.createElement("div",{tabIndex:-1,onClick:e=>e.preventDefault(),onMouseDown:e=>e.preventDefault(),onTouchStart:e=>{e.cancelable&&e.preventDefault()},onMouseUp:()=>{var e,t;m||(null===(e=w.current)||void 0===e||e.stepUp(),null===(t=w.current)||void 0===t||t.dispatchEvent(new Event("input",{bubbles:!0})))},className:n.tremorTwMerge(!m&&c,d,"group py-[10px] px-2.5 border-l border-tremor-border dark:border-dark-tremor-border")},s.default.createElement(r,{"data-testid":"step-up",className:(C?"scale-95":"")+" h-4 w-4 duration-75 transition group-active:scale-95"}))):null},k))}));i.displayName="NumberInput",module.exports=i;
