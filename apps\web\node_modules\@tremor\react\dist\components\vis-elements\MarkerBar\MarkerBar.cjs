'use client';
"use strict";var e=require("tslib"),r=require("react"),t=require("../../util-elements/Tooltip/Tooltip.cjs"),a=require("../../../lib/theme.cjs"),l=require("../../../lib/tremorTwMerge.cjs"),o=require("../../../lib/utils.cjs");function s(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var n=s(r);const i=o.makeClassName("MarkerBar"),u=n.default.forwardRef(((r,s)=>{const{value:u,minValue:m,maxValue:d,markerTooltip:c,rangeTooltip:f,showAnimation:g=!1,color:b,className:p}=r,k=e.__rest(r,["value","minValue","maxValue","markerTooltip","rangeTooltip","showAnimation","color","className"]),{tooltipProps:v,getReferenceProps:T}=t.useTooltip(),{tooltipProps:w,getReferenceProps:j}=t.useTooltip();return n.default.createElement("div",Object.assign({ref:s,className:l.tremorTwMerge(i("root"),"relative flex items-center w-full rounded-tremor-full h-2","bg-tremor-background-subtle","dark:bg-dark-tremor-background-subtle",p)},k),void 0!==m&&void 0!==d?n.default.createElement(n.default.Fragment,null,n.default.createElement(t.default,Object.assign({text:f},w)),n.default.createElement("div",Object.assign({ref:w.refs.setReference,className:l.tremorTwMerge(i("rangeBar"),"absolute h-full rounded-tremor-full","bg-tremor-content-subtle","dark:bg-dark-tremor-content-subtle"),style:{left:`${m}%`,width:d-m+"%",transition:g?"all duration-300":""}},j))):null,n.default.createElement(t.default,Object.assign({text:c},v)),n.default.createElement("div",Object.assign({ref:v.refs.setReference,className:l.tremorTwMerge(i("markerWrapper"),"absolute right-1/2 -translate-x-1/2 w-5"),style:{left:`${u}%`,transition:g?"all 1s":""}},T),n.default.createElement("div",{className:l.tremorTwMerge(i("marker"),"ring-2 mx-auto rounded-tremor-full h-4 w-1","ring-tremor-brand-inverted","dark:ring-dark-tremor-brand-inverted",b?o.getColorClassNames(b,a.colorPalette.background).bgColor:"dark:bg-dark-tremor-brand bg-tremor-brand")})))}));u.displayName="MarkerBar",module.exports=u;
