"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("./constants.cjs"),r=require("./inputTypes.cjs");exports.defaultValueFormatter=e=>e.toString(),exports.getColorClassNames=function(e,t){const o=r.getIsBaseColor(e);if("white"===e||"black"===e||"transparent"===e||!t||!o){const r=(e=>e.includes("#")||e.includes("--")||e.includes("rgb"))(e)?`[${e}]`:e;return{bgColor:`bg-${r} dark:bg-${r}`,hoverBgColor:`hover:bg-${r} dark:hover:bg-${r}`,selectBgColor:`data-[selected]:bg-${r} dark:data-[selected]:bg-${r}`,textColor:`text-${r} dark:text-${r}`,selectTextColor:`data-[selected]:text-${r} dark:data-[selected]:text-${r}`,hoverTextColor:`hover:text-${r} dark:hover:text-${r}`,borderColor:`border-${r} dark:border-${r}`,selectBorderColor:`data-[selected]:border-${r} dark:data-[selected]:border-${r}`,hoverBorderColor:`hover:border-${r} dark:hover:border-${r}`,ringColor:`ring-${r} dark:ring-${r}`,strokeColor:`stroke-${r} dark:stroke-${r}`,fillColor:`fill-${r} dark:fill-${r}`}}return{bgColor:`bg-${e}-${t} dark:bg-${e}-${t}`,selectBgColor:`data-[selected]:bg-${e}-${t} dark:data-[selected]:bg-${e}-${t}`,hoverBgColor:`hover:bg-${e}-${t} dark:hover:bg-${e}-${t}`,textColor:`text-${e}-${t} dark:text-${e}-${t}`,selectTextColor:`data-[selected]:text-${e}-${t} dark:data-[selected]:text-${e}-${t}`,hoverTextColor:`hover:text-${e}-${t} dark:hover:text-${e}-${t}`,borderColor:`border-${e}-${t} dark:border-${e}-${t}`,selectBorderColor:`data-[selected]:border-${e}-${t} dark:data-[selected]:border-${e}-${t}`,hoverBorderColor:`hover:border-${e}-${t} dark:hover:border-${e}-${t}`,ringColor:`ring-${e}-${t} dark:ring-${e}-${t}`,strokeColor:`stroke-${e}-${t} dark:stroke-${e}-${t}`,fillColor:`fill-${e}-${t} dark:fill-${e}-${t}`}},exports.isValueInArray=(e,r)=>{for(let t=0;t<r.length;t++)if(r[t]===e)return!0;return!1},exports.makeClassName=function(e){return r=>`tremor-${e}-${r}`},exports.mapInputsToDeltaType=(r,t)=>{if(t||r===e.DeltaTypes.Unchanged)return r;switch(r){case e.DeltaTypes.Increase:return e.DeltaTypes.Decrease;case e.DeltaTypes.ModerateIncrease:return e.DeltaTypes.ModerateDecrease;case e.DeltaTypes.Decrease:return e.DeltaTypes.Increase;case e.DeltaTypes.ModerateDecrease:return e.DeltaTypes.ModerateIncrease}return""},exports.mergeRefs=function(e){return r=>{e.forEach((e=>{"function"==typeof e?e(r):null!=e&&(e.current=r)}))}},exports.sumNumericArray=e=>e.reduce(((e,r)=>e+r),0);
