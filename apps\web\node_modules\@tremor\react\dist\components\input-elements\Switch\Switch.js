'use client';
import{__rest as e}from"tslib";import{Switch as r}from"@headlessui/react";import o from"../../../hooks/useInternalState.js";import t,{useState as a}from"react";import{colorPalette as n}from"../../../lib/theme.js";import{tremorTwMerge as l}from"../../../lib/tremorTwMerge.js";import{mergeRefs as s,getColorClassNames as i,makeClassName as d}from"../../../lib/utils.js";import m,{useTooltip as c}from"../../util-elements/Tooltip/Tooltip.js";const u=d("Switch"),b=t.forwardRef(((d,b)=>{const{checked:g,defaultChecked:f=!1,onChange:p,color:h,name:k,error:w,errorMessage:x,disabled:C,required:j,tooltip:E,id:N}=d,v=e(d,["checked","defaultChecked","onChange","color","name","error","errorMessage","disabled","required","tooltip","id"]),y=(e=>({bgColor:e?i(e,n.background).bgColor:"bg-tremor-brand dark:bg-dark-tremor-brand",ringColor:e?i(e,n.ring).ringColor:"ring-tremor-brand-muted dark:ring-dark-tremor-brand-muted"}))(h),[S,M]=o(f,g),[q,R]=a(!1),{tooltipProps:T,getReferenceProps:O}=c(300);return t.createElement("div",{className:"flex flex-row items-center justify-start"},t.createElement(m,Object.assign({text:E},T)),t.createElement("div",Object.assign({ref:s([b,T.refs.setReference]),className:l(u("root"),"flex flex-row relative h-5")},v,O),t.createElement("input",{type:"checkbox",className:l(u("input"),"absolute w-5 h-5 cursor-pointer left-0 top-0 opacity-0"),name:k,required:j,checked:S,onChange:e=>{e.preventDefault()}}),t.createElement(r,{checked:S,onChange:e=>{M(e),null==p||p(e)},disabled:C,className:l(u("switch"),"w-10 h-5 group relative inline-flex shrink-0 cursor-pointer items-center justify-center rounded-tremor-full","focus:outline-none",C?"cursor-not-allowed":""),onFocus:()=>R(!0),onBlur:()=>R(!1),id:N},t.createElement("span",{className:l(u("sr-only"),"sr-only")},"Switch ",S?"on":"off"),t.createElement("span",{"aria-hidden":"true",className:l(u("background"),S?y.bgColor:"bg-tremor-border dark:bg-dark-tremor-border","pointer-events-none absolute mx-auto h-3 w-9 rounded-tremor-full transition-colors duration-100 ease-in-out")}),t.createElement("span",{"aria-hidden":"true",className:l(u("round"),S?l(y.bgColor,"translate-x-5 border-tremor-background dark:border-dark-tremor-background"):"translate-x-0 bg-tremor-border dark:bg-dark-tremor-border border-tremor-background dark:border-dark-tremor-background","pointer-events-none absolute left-0 inline-block h-5 w-5 transform rounded-tremor-full border-2 shadow-tremor-input duration-100 ease-in-out transition",q?l("ring-2",y.ringColor):"")}))),w&&x?t.createElement("p",{className:l(u("errorMessage"),"text-sm text-red-500 mt-1 ")},x):null)}));b.displayName="Switch";export{b as default};
