import{DeltaTypes as e}from"./constants.js";import{getIsBaseColor as r}from"./inputTypes.js";const o=(r,o)=>{if(o||r===e.Unchanged)return r;switch(r){case e.Increase:return e.Decrease;case e.ModerateIncrease:return e.ModerateDecrease;case e.Decrease:return e.Increase;case e.ModerateDecrease:return e.ModerateIncrease}return""},t=e=>e.toString(),d=e=>e.reduce(((e,r)=>e+r),0),$=(e,r)=>{for(let o=0;o<r.length;o++)if(r[o]===e)return!0;return!1};function a(e){return r=>{e.forEach((e=>{"function"==typeof e?e(r):null!=e&&(e.current=r)}))}}function l(e){return r=>`tremor-${e}-${r}`}function s(e,o){const t=r(e);if("white"===e||"black"===e||"transparent"===e||!o||!t){const r=(e=>e.includes("#")||e.includes("--")||e.includes("rgb"))(e)?`[${e}]`:e;return{bgColor:`bg-${r} dark:bg-${r}`,hoverBgColor:`hover:bg-${r} dark:hover:bg-${r}`,selectBgColor:`data-[selected]:bg-${r} dark:data-[selected]:bg-${r}`,textColor:`text-${r} dark:text-${r}`,selectTextColor:`data-[selected]:text-${r} dark:data-[selected]:text-${r}`,hoverTextColor:`hover:text-${r} dark:hover:text-${r}`,borderColor:`border-${r} dark:border-${r}`,selectBorderColor:`data-[selected]:border-${r} dark:data-[selected]:border-${r}`,hoverBorderColor:`hover:border-${r} dark:hover:border-${r}`,ringColor:`ring-${r} dark:ring-${r}`,strokeColor:`stroke-${r} dark:stroke-${r}`,fillColor:`fill-${r} dark:fill-${r}`}}return{bgColor:`bg-${e}-${o} dark:bg-${e}-${o}`,selectBgColor:`data-[selected]:bg-${e}-${o} dark:data-[selected]:bg-${e}-${o}`,hoverBgColor:`hover:bg-${e}-${o} dark:hover:bg-${e}-${o}`,textColor:`text-${e}-${o} dark:text-${e}-${o}`,selectTextColor:`data-[selected]:text-${e}-${o} dark:data-[selected]:text-${e}-${o}`,hoverTextColor:`hover:text-${e}-${o} dark:hover:text-${e}-${o}`,borderColor:`border-${e}-${o} dark:border-${e}-${o}`,selectBorderColor:`data-[selected]:border-${e}-${o} dark:data-[selected]:border-${e}-${o}`,hoverBorderColor:`hover:border-${e}-${o} dark:hover:border-${e}-${o}`,ringColor:`ring-${e}-${o} dark:ring-${e}-${o}`,strokeColor:`stroke-${e}-${o} dark:stroke-${e}-${o}`,fillColor:`fill-${e}-${o} dark:fill-${e}-${o}`}}export{t as defaultValueFormatter,s as getColorClassNames,$ as isValueInArray,l as makeClassName,o as mapInputsToDeltaType,a as mergeRefs,d as sumNumericArray};
