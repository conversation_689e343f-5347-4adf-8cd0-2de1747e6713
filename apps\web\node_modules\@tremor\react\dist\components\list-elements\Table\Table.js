import{__rest as e}from"tslib";import t from"react";import{tremorTwMerge as r}from"../../../lib/tremorTwMerge.js";import{makeClassName as a}from"../../../lib/utils.js";const o=a("Table"),l=t.forwardRef(((a,l)=>{const{children:m,className:s}=a,c=e(a,["children","className"]);return t.createElement("div",{className:r(o("root"),"overflow-auto",s)},t.createElement("table",Object.assign({ref:l,className:r(o("table"),"w-full text-tremor-default","text-tremor-content","dark:text-dark-tremor-content")},c),m))}));l.displayName="Table";export{l as default};
