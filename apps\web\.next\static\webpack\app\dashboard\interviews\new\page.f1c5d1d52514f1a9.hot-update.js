"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/interviews/new/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/interviews/new/page.tsx":
/*!***************************************************!*\
  !*** ./src/app/dashboard/interviews/new/page.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ NewInterviewPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _services_aiInterviewService__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/services/aiInterviewService */ \"(app-pages-browser)/./src/services/aiInterviewService.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Brain,Building,Code,FileText,Heart,Mic,Palette,Settings,TrendingUp,Users,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Brain,Building,Code,FileText,Heart,Mic,Palette,Settings,TrendingUp,Users,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Brain,Building,Code,FileText,Heart,Mic,Palette,Settings,TrendingUp,Users,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Brain,Building,Code,FileText,Heart,Mic,Palette,Settings,TrendingUp,Users,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Brain,Building,Code,FileText,Heart,Mic,Palette,Settings,TrendingUp,Users,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Brain,Building,Code,FileText,Heart,Mic,Palette,Settings,TrendingUp,Users,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Brain,Building,Code,FileText,Heart,Mic,Palette,Settings,TrendingUp,Users,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/video.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Brain,Building,Code,FileText,Heart,Mic,Palette,Settings,TrendingUp,Users,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Brain,Building,Code,FileText,Heart,Mic,Palette,Settings,TrendingUp,Users,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Brain,Building,Code,FileText,Heart,Mic,Palette,Settings,TrendingUp,Users,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Brain,Building,Code,FileText,Heart,Mic,Palette,Settings,TrendingUp,Users,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Brain,Building,Code,FileText,Heart,Mic,Palette,Settings,TrendingUp,Users,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Brain,Building,Code,FileText,Heart,Mic,Palette,Settings,TrendingUp,Users,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Brain,Building,Code,FileText,Heart,Mic,Palette,Settings,TrendingUp,Users,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction NewInterviewPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [isCreating, setIsCreating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [setup, setSetup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        jobTitle: \"\",\n        company: \"\",\n        industry: \"\",\n        difficulty: \"medium\",\n        duration: 30,\n        questionTypes: [\n            \"behavioral\",\n            \"technical\"\n        ],\n        interviewType: \"video\",\n        jobDescription: \"\",\n        customQuestions: []\n    });\n    const industries = [\n        {\n            id: \"technology\",\n            name: \"Technology\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            color: \"blue\"\n        },\n        {\n            id: \"finance\",\n            name: \"Finance\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            color: \"green\"\n        },\n        {\n            id: \"healthcare\",\n            name: \"Healthcare\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            color: \"red\"\n        },\n        {\n            id: \"consulting\",\n            name: \"Consulting\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            color: \"purple\"\n        },\n        {\n            id: \"design\",\n            name: \"Design\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            color: \"pink\"\n        },\n        {\n            id: \"other\",\n            name: \"Other\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n            color: \"gray\"\n        }\n    ];\n    const questionTypes = [\n        {\n            id: \"behavioral\",\n            name: \"Behavioral\",\n            description: \"Past experiences and situations\"\n        },\n        {\n            id: \"technical\",\n            name: \"Technical\",\n            description: \"Role-specific technical knowledge\"\n        },\n        {\n            id: \"situational\",\n            name: \"Situational\",\n            description: \"Hypothetical scenarios\"\n        },\n        {\n            id: \"company-specific\",\n            name: \"Company-Specific\",\n            description: \"Company culture and values\"\n        }\n    ];\n    const interviewTypes = [\n        {\n            id: \"video\",\n            name: \"Video Interview\",\n            description: \"Full video recording with AI analysis of verbal and non-verbal communication\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n            features: [\n                \"Video recording\",\n                \"Body language analysis\",\n                \"Eye contact tracking\",\n                \"Professional presence\"\n            ]\n        },\n        {\n            id: \"audio\",\n            name: \"Audio Interview\",\n            description: \"Audio-only recording focusing on verbal communication and content\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n            features: [\n                \"Audio recording\",\n                \"Speech analysis\",\n                \"Pace and clarity\",\n                \"Content quality\"\n            ]\n        },\n        {\n            id: \"text\",\n            name: \"Text Interview\",\n            description: \"Written responses with AI analysis of structure and content\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n            features: [\n                \"Written responses\",\n                \"Structure analysis\",\n                \"Grammar check\",\n                \"Content depth\"\n            ]\n        }\n    ];\n    const handleNext = ()=>{\n        if (currentStep < 4) {\n            setCurrentStep(currentStep + 1);\n        }\n    };\n    const handlePrevious = ()=>{\n        if (currentStep > 1) {\n            setCurrentStep(currentStep - 1);\n        }\n    };\n    const handleCreateInterview = async ()=>{\n        setIsCreating(true);\n        try {\n            // Generate questions based on setup\n            const questions = await _services_aiInterviewService__WEBPACK_IMPORTED_MODULE_9__.aiInterviewService.generateQuestions({\n                jobTitle: setup.jobTitle,\n                industry: setup.industry,\n                difficulty: setup.difficulty,\n                count: Math.floor(setup.duration / 5),\n                types: setup.questionTypes\n            });\n            // Store interview setup in sessionStorage\n            sessionStorage.setItem(\"interviewSetup\", JSON.stringify({\n                ...setup,\n                questions\n            }));\n            // Navigate to practice page\n            router.push(\"/dashboard/interviews/practice\");\n        } catch (error) {\n            console.error(\"Error creating interview:\", error);\n        } finally{\n            setIsCreating(false);\n        }\n    };\n    const updateSetup = (updates)=>{\n        setSetup((prev)=>({\n                ...prev,\n                ...updates\n            }));\n    };\n    const toggleQuestionType = (type)=>{\n        const currentTypes = setup.questionTypes;\n        if (currentTypes.includes(type)) {\n            updateSetup({\n                questionTypes: currentTypes.filter((t)=>t !== type)\n            });\n        } else {\n            updateSetup({\n                questionTypes: [\n                    ...currentTypes,\n                    type\n                ]\n            });\n        }\n    };\n    const getStepTitle = (step)=>{\n        switch(step){\n            case 1:\n                return \"Basic Information\";\n            case 2:\n                return \"Interview Type & Settings\";\n            case 3:\n                return \"Question Configuration\";\n            case 4:\n                return \"Review & Create\";\n            default:\n                return \"Setup\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-4xl mx-auto space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            onClick: ()=>router.back(),\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Back\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-foreground flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"h-8 w-8 text-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Create New Interview\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground mt-1\",\n                                    children: \"Set up a personalized AI-powered interview session\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                lineNumber: 168,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    1,\n                    2,\n                    3,\n                    4\n                ].map((step)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium \".concat(step <= currentStep ? \"bg-blue-600 text-white\" : \"bg-gray-200 text-gray-600\"),\n                                children: step\n                            }, void 0, false, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 13\n                            }, this),\n                            step < 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-1 mx-2 \".concat(step < currentStep ? \"bg-blue-600\" : \"bg-gray-200\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, step, true, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                lineNumber: 192,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                            className: \"flex items-center space-x-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Step \",\n                                    currentStep,\n                                    \": \",\n                                    getStepTitle(currentStep)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        className: \"space-y-6\",\n                        children: [\n                            currentStep === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"title\",\n                                                children: \"Interview Title\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"title\",\n                                                value: setup.title,\n                                                onChange: (e)=>updateSetup({\n                                                        title: e.target.value\n                                                    }),\n                                                placeholder: \"e.g., Software Engineer Interview Practice\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"jobTitle\",\n                                                        children: \"Job Title\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        id: \"jobTitle\",\n                                                        value: setup.jobTitle,\n                                                        onChange: (e)=>updateSetup({\n                                                                jobTitle: e.target.value\n                                                            }),\n                                                        placeholder: \"e.g., Senior Software Engineer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"company\",\n                                                        children: \"Company (Optional)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        id: \"company\",\n                                                        value: setup.company,\n                                                        onChange: (e)=>updateSetup({\n                                                                company: e.target.value\n                                                            }),\n                                                        placeholder: \"e.g., Google, Microsoft\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                children: \"Industry\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 md:grid-cols-3 gap-3\",\n                                                children: industries.map((industry)=>{\n                                                    const Icon = industry.icon;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4 border rounded-lg cursor-pointer transition-all \".concat(setup.industry === industry.id ? \"border-blue-500 bg-blue-50\" : \"border-gray-200 hover:border-gray-300\"),\n                                                        onClick: ()=>updateSetup({\n                                                                industry: industry.id\n                                                            }),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                    className: \"h-5 w-5 text-\".concat(industry.color, \"-600\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 269,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: industry.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 270,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, industry.id, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 23\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 13\n                            }, this),\n                            currentStep === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                children: \"Interview Type\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                children: interviewTypes.map((type)=>{\n                                                    const Icon = type.icon;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4 border rounded-lg cursor-pointer transition-all \".concat(setup.interviewType === type.id ? \"border-blue-500 bg-blue-50\" : \"border-gray-200 hover:border-gray-300\"),\n                                                        onClick: ()=>updateSetup({\n                                                                interviewType: type.id\n                                                            }),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                            className: \"h-5 w-5 text-blue-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 300,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: type.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 301,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 299,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: type.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 303,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-1\",\n                                                                    children: type.features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2 text-xs text-gray-500\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-1 h-1 bg-gray-400 rounded-full\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                                    lineNumber: 307,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: feature\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                                    lineNumber: 308,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, index, true, {\n                                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 306,\n                                                                            columnNumber: 31\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 304,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                            lineNumber: 298,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, type.id, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 23\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"duration\",\n                                                        children: \"Duration (minutes)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        id: \"duration\",\n                                                        value: setup.duration,\n                                                        onChange: (e)=>updateSetup({\n                                                                duration: Number(e.target.value)\n                                                            }),\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 15,\n                                                                children: \"15 minutes\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                lineNumber: 328,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 30,\n                                                                children: \"30 minutes\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 45,\n                                                                children: \"45 minutes\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                lineNumber: 330,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 60,\n                                                                children: \"60 minutes\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                lineNumber: 331,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"difficulty\",\n                                                        children: \"Difficulty Level\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        id: \"difficulty\",\n                                                        value: setup.difficulty,\n                                                        onChange: (e)=>updateSetup({\n                                                                difficulty: e.target.value\n                                                            }),\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"easy\",\n                                                                children: \"Easy\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                lineNumber: 342,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"medium\",\n                                                                children: \"Medium\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                lineNumber: 343,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"hard\",\n                                                                children: \"Hard\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                lineNumber: 344,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 13\n                            }, this),\n                            currentStep === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                children: \"Question Types\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-3\",\n                                                children: questionTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4 border rounded-lg cursor-pointer transition-all \".concat(setup.questionTypes.includes(type.id) ? \"border-blue-500 bg-blue-50\" : \"border-gray-200 hover:border-gray-300\"),\n                                                        onClick: ()=>toggleQuestionType(type.id),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"font-medium\",\n                                                                            children: type.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 369,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: type.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 370,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 368,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                setup.questionTypes.includes(type.id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                    variant: \"default\",\n                                                                    children: \"Selected\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 373,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, type.id, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                        lineNumber: 358,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"jobDescription\",\n                                                children: \"Job Description (Optional)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                id: \"jobDescription\",\n                                                value: setup.jobDescription,\n                                                onChange: (e)=>updateSetup({\n                                                        jobDescription: e.target.value\n                                                    }),\n                                                placeholder: \"Paste the job description here for more targeted questions...\",\n                                                rows: 4\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Adding a job description helps our AI generate more relevant questions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 13\n                            }, this),\n                            currentStep === 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-50 p-6 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-blue-900 mb-4\",\n                                                children: \"Interview Summary\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-blue-800\",\n                                                                children: \"Title:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                lineNumber: 404,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-2\",\n                                                                children: setup.title || \"Untitled Interview\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                lineNumber: 405,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                        lineNumber: 403,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-blue-800\",\n                                                                children: \"Job Title:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                lineNumber: 408,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-2\",\n                                                                children: setup.jobTitle\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                lineNumber: 409,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                        lineNumber: 407,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-blue-800\",\n                                                                children: \"Industry:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                lineNumber: 412,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-2 capitalize\",\n                                                                children: setup.industry\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                lineNumber: 413,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-blue-800\",\n                                                                children: \"Duration:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                lineNumber: 416,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-2\",\n                                                                children: [\n                                                                    setup.duration,\n                                                                    \" minutes\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                lineNumber: 417,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                        lineNumber: 415,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-blue-800\",\n                                                                children: \"Type:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                lineNumber: 420,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-2 capitalize\",\n                                                                children: setup.interviewType\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                lineNumber: 421,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                        lineNumber: 419,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-blue-800\",\n                                                                children: \"Difficulty:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                lineNumber: 424,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-2 capitalize\",\n                                                                children: setup.difficulty\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                lineNumber: 425,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                        lineNumber: 423,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium text-blue-800\",\n                                                        children: \"Question Types:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-2 mt-2\",\n                                                        children: setup.questionTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                variant: \"secondary\",\n                                                                className: \"capitalize\",\n                                                                children: type.replace(\"-\", \" \")\n                                                            }, type, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                lineNumber: 432,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                        lineNumber: 430,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4 p-4 bg-green-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"h-8 w-8 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                lineNumber: 441,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-green-900\",\n                                                        children: \"AI-Powered Features\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                        lineNumber: 443,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-green-700\",\n                                                        children: \"Your interview will include real-time AI analysis, personalized feedback, and performance scoring\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                lineNumber: 212,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        variant: \"outline\",\n                        onClick: handlePrevious,\n                        disabled: currentStep === 1,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                lineNumber: 461,\n                                columnNumber: 11\n                            }, this),\n                            \"Previous\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                        lineNumber: 456,\n                        columnNumber: 9\n                    }, this),\n                    currentStep < 4 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        onClick: handleNext,\n                        disabled: currentStep === 1 && (!setup.jobTitle || !setup.industry) || currentStep === 3 && setup.questionTypes.length === 0,\n                        children: [\n                            \"Next\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                className: \"ml-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                lineNumber: 474,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                        lineNumber: 466,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        onClick: handleCreateInterview,\n                        disabled: isCreating || !setup.jobTitle || !setup.industry,\n                        className: \"flex items-center space-x-2\",\n                        children: isCreating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                    className: \"mr-2 h-4 w-4 animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                    lineNumber: 484,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Creating Interview...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                    lineNumber: 485,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    className: \"mr-2 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                    lineNumber: 489,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Start Interview\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                    lineNumber: 490,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                        lineNumber: 477,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                lineNumber: 455,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n        lineNumber: 166,\n        columnNumber: 5\n    }, this);\n}\n_s(NewInterviewPage, \"tyB/KZ4bdaElduFTKLwGo/eK0eQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = NewInterviewPage;\nvar _c;\n$RefreshReg$(_c, \"NewInterviewPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/interviews/new/page.tsx\n"));

/***/ })

});