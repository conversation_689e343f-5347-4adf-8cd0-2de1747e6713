'use client';
"use strict";var e=require("tslib"),t=require("react"),a=require("recharts"),l=require("../common/ChartLegend.cjs"),r=require("./ScatterChartTooltip.cjs"),o=require("../common/NoData.cjs"),i=require("../common/utils.cjs"),n=require("../../../lib/constants.cjs"),s=require("../../../lib/theme.cjs"),u=require("../../../lib/tremorTwMerge.cjs"),d=require("../../../lib/utils.cjs");function c(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var m=c(t);const f=m.default.forwardRef(((c,f)=>{const{data:v=[],x:y,y:p,size:g,category:h,colors:x=s.themeColorRange,showOpacity:b=!1,sizeRange:k=[1,1e3],valueFormatter:w={x:d.defaultValueFormatter,y:d.defaultValueFormatter,size:d.defaultValueFormatter},startEndOnly:C=!1,showXAxis:A=!0,showYAxis:E=!0,yAxisWidth:L=56,intervalType:T="equidistantPreserveStart",animationDuration:V=900,showAnimation:D=!1,showTooltip:q=!0,showLegend:N=!0,showGridLines:j=!0,autoMinXValue:S=!1,minXValue:O,maxXValue:X,autoMinYValue:Y=!1,minYValue:M,maxYValue:F,allowDecimals:G=!0,noDataText:z,onValueChange:R,customTooltip:B,rotateLabelX:P,className:K,enableLegendSlider:W=!1,tickGap:_=5,xAxisLabel:H,yAxisLabel:Z}=c,$=e.__rest(c,["data","x","y","size","category","colors","showOpacity","sizeRange","valueFormatter","startEndOnly","showXAxis","showYAxis","yAxisWidth","intervalType","animationDuration","showAnimation","showTooltip","showLegend","showGridLines","autoMinXValue","minXValue","maxXValue","autoMinYValue","minYValue","maxYValue","allowDecimals","noDataText","onValueChange","customTooltip","rotateLabelX","className","enableLegendSlider","tickGap","xAxisLabel","yAxisLabel"]),I=B,[J,Q]=t.useState(60),[U,ee]=m.default.useState(void 0),[te,ae]=t.useState(void 0),le=!!R;function re(e,t,a){a.stopPropagation(),le&&(i.deepEqual(U,e.node)?(ae(void 0),ee(void 0),null==R||R(null)):(ee(e.node),ae(e.payload[h]),null==R||R(Object.assign({eventType:"bubble",categoryClicked:e.payload[h]},e.payload))))}const oe=i.constructCategories(v,h),ie=i.constructCategoryColors(oe,x),ne=i.getYAxisDomain(S,O,X),se=i.getYAxisDomain(Y,M,F);return m.default.createElement("div",Object.assign({ref:f,className:u.tremorTwMerge("w-full h-80",K)},$),m.default.createElement(a.ResponsiveContainer,{className:"h-full w-full"},(null==v?void 0:v.length)?m.default.createElement(a.ScatterChart,{onClick:le&&(te||U)?()=>{ee(void 0),ae(void 0),null==R||R(null)}:void 0,margin:{bottom:H?20:void 0,left:20,right:20,top:5}},j?m.default.createElement(a.CartesianGrid,{className:u.tremorTwMerge("stroke-1","stroke-tremor-border","dark:stroke-dark-tremor-border"),horizontal:!0,vertical:!0}):null,y?m.default.createElement(a.XAxis,{hide:!A,dataKey:y,interval:C?"preserveStartEnd":T,tick:{transform:"translate(0, 6)"},ticks:C?[v[0][y],v[v.length-1][y]]:void 0,type:"number",name:y,fill:"",stroke:"",className:u.tremorTwMerge("text-tremor-label","fill-tremor-content","dark:fill-dark-tremor-content"),tickLine:!1,tickFormatter:w.x,axisLine:!1,minTickGap:_,domain:ne,allowDataOverflow:!0,angle:null==P?void 0:P.angle,dy:null==P?void 0:P.verticalShift,height:null==P?void 0:P.xAxisHeight},H&&m.default.createElement(a.Label,{position:"insideBottom",offset:-20,className:"fill-tremor-content-emphasis text-tremor-default font-medium dark:fill-dark-tremor-content-emphasis"},H)):null,p?m.default.createElement(a.YAxis,{width:L,hide:!E,axisLine:!1,tickLine:!1,dataKey:p,type:"number",name:p,domain:se,tick:{transform:"translate(-3, 0)"},tickFormatter:w.y,fill:"",stroke:"",className:u.tremorTwMerge("text-tremor-label","fill-tremor-content","dark:fill-dark-tremor-content"),allowDecimals:G,allowDataOverflow:!0},Z&&m.default.createElement(a.Label,{position:"insideLeft",style:{textAnchor:"middle"},angle:-90,offset:-15,className:"fill-tremor-content-emphasis text-tremor-default font-medium dark:fill-dark-tremor-content-emphasis"},Z)):null,m.default.createElement(a.Tooltip,{wrapperStyle:{outline:"none"},isAnimationActive:!1,cursor:{stroke:"#d1d5db",strokeWidth:1},content:q?({active:e,payload:t,label:a})=>{var l,o;const i=h?null===(o=null===(l=null==t?void 0:t[0])||void 0===l?void 0:l.payload)||void 0===o?void 0:o[h]:a;return I?m.default.createElement(I,{payload:null==t?void 0:t.map((e=>{var t;return Object.assign(Object.assign({},e),{color:null!==(t=ie.get(i))&&void 0!==t?t:n.BaseColors.Gray})})),active:e,label:i}):m.default.createElement(r.default,{active:e,payload:t,label:i,valueFormatter:w,axis:{x:y,y:p,size:g},category:h,categoryColors:ie})}:m.default.createElement(m.default.Fragment,null)}),g?m.default.createElement(a.ZAxis,{dataKey:g,type:"number",range:k,name:g}):null,oe.map((e=>{var t,l;return m.default.createElement(a.Scatter,{className:u.tremorTwMerge(d.getColorClassNames(null!==(t=ie.get(e))&&void 0!==t?t:n.BaseColors.Gray,s.colorPalette.text).fillColor,b?d.getColorClassNames(null!==(l=ie.get(e))&&void 0!==l?l:n.BaseColors.Gray,s.colorPalette.text).strokeColor:"",R?"cursor-pointer":""),fill:`url(#${ie.get(e)})`,fillOpacity:b?.7:1,key:e,name:e,data:h?v.filter((t=>t[h]===e)):v,isAnimationActive:D,animationDuration:V,shape:e=>((e,t,l)=>{const{cx:r,cy:o,width:n,node:s,fillOpacity:u,name:d}=e;return m.default.createElement(a.Dot,{cx:r,cy:o,r:n/2,opacity:t||l&&l!==d?i.deepEqual(t,s)?u:.3:u})})(e,U,te),onClick:re})})),N?m.default.createElement(a.Legend,{verticalAlign:"top",height:J,content:({payload:e})=>l({payload:e},ie,Q,te,le?e=>{return t=e,void(le&&(t!==te||U?(ae(t),null==R||R({eventType:"category",categoryClicked:t})):(ae(void 0),null==R||R(null)),ee(void 0)));var t}:void 0,W)}):null):m.default.createElement(o,{noDataText:z})))}));f.displayName="ScatterChart",module.exports=f;
