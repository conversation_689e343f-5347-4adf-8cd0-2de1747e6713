import{__rest as t}from"tslib";import{colorPalette as e}from"../../../lib/theme.js";import{tremorTwMerge as r}from"../../../lib/tremorTwMerge.js";import{getColorClassNames as o}from"../../../lib/utils.js";import m from"react";const s=m.forwardRef(((s,a)=>{const{color:i,children:l,className:c}=s,n=t(s,["color","children","className"]);return m.createElement("p",Object.assign({ref:a,className:r(i?o(i,e.lightText).textColor:"text-tremor-content-emphasis dark:text-dark-tremor-content-emphasis",c)},n),l)}));s.displayName="Subtitle";export{s as default};
