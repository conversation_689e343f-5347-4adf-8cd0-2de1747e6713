"use client";import{useFocusRing as Ae}from"@react-aria/focus";import{useHover as he}from"@react-aria/interactions";import A,{Fragment as xe,createContext as ae,createRef as De,useCallback as se,useContext as pe,useEffect as _e,useMemo as k,useReducer as Ie,useRef as ue,useState as Ce}from"react";import{flushSync as U}from"react-dom";import{useActivePress as Fe}from'../../hooks/use-active-press.js';import{useByComparator as Me}from'../../hooks/use-by-comparator.js';import{useControllable as Be}from'../../hooks/use-controllable.js';import{useDefaultValue as we}from'../../hooks/use-default-value.js';import{useDidElementMove as ke}from'../../hooks/use-did-element-move.js';import{useDisposables as Oe}from'../../hooks/use-disposables.js';import{useElementSize as Ue}from'../../hooks/use-element-size.js';import{useEvent as T}from'../../hooks/use-event.js';import{useId as de}from'../../hooks/use-id.js';import{useInertOthers as Ne}from'../../hooks/use-inert-others.js';import{useIsoMorphicEffect as ce}from'../../hooks/use-iso-morphic-effect.js';import{useLatestValue as He}from'../../hooks/use-latest-value.js';import{useOnDisappear as Ge}from'../../hooks/use-on-disappear.js';import{useOutsideClick as Ve}from'../../hooks/use-outside-click.js';import{useOwnerDocument as Ke}from'../../hooks/use-owner.js';import{useResolveButtonType as je}from'../../hooks/use-resolve-button-type.js';import{useScrollLock as ze}from'../../hooks/use-scroll-lock.js';import{useSyncRefs as j}from'../../hooks/use-sync-refs.js';import{useTextValue as We}from'../../hooks/use-text-value.js';import{useTrackedPointer as Qe}from'../../hooks/use-tracked-pointer.js';import{transitionDataAttributes as Xe,useTransition as Je}from'../../hooks/use-transition.js';import{useDisabled as $e}from'../../internal/disabled.js';import{FloatingProvider as qe,useFloatingPanel as Ye,useFloatingPanelProps as Ze,useFloatingReference as et,useFloatingReferenceProps as tt,useResolvedAnchor as ot}from'../../internal/floating.js';import{FormFields as nt}from'../../internal/form-fields.js';import{useFrozenData as it}from'../../internal/frozen.js';import{useProvidedId as rt}from'../../internal/id.js';import{OpenClosedProvider as lt,State as Y,useOpenClosed as at}from'../../internal/open-closed.js';import{isDisabledReactIssue7711 as st}from'../../utils/bugs.js';import{Focus as v,calculateActiveIndex as fe}from'../../utils/calculate-active-index.js';import{disposables as pt}from'../../utils/disposables.js';import{Focus as ye,FocusableMode as ut,focusFrom as dt,isFocusableElement as ct,sortByDomNode as ft}from'../../utils/focus-management.js';import{attemptSubmit as bt}from'../../utils/form.js';import{match as V}from'../../utils/match.js';import{getOwnerDocument as Tt}from'../../utils/owner.js';import{RenderFeatures as ve,forwardRefWithAs as z,mergeProps as ge,useRender as W}from'../../utils/render.js';import{useDescribedBy as mt}from'../description/description.js';import{Keys as E}from'../keyboard.js';import{Label as xt,useLabelledBy as Ot,useLabels as yt}from'../label/label.js';import{Portal as vt}from'../portal/portal.js';var gt=(o=>(o[o.Open=0]="Open",o[o.Closed=1]="Closed",o))(gt||{}),Lt=(o=>(o[o.Single=0]="Single",o[o.Multi=1]="Multi",o))(Lt||{}),St=(o=>(o[o.Pointer=0]="Pointer",o[o.Other=1]="Other",o))(St||{}),Et=(n=>(n[n.OpenListbox=0]="OpenListbox",n[n.CloseListbox=1]="CloseListbox",n[n.GoToOption=2]="GoToOption",n[n.Search=3]="Search",n[n.ClearSearch=4]="ClearSearch",n[n.RegisterOption=5]="RegisterOption",n[n.UnregisterOption=6]="UnregisterOption",n[n.SetButtonElement=7]="SetButtonElement",n[n.SetOptionsElement=8]="SetOptionsElement",n))(Et||{});function be(e,i=o=>o){let o=e.activeOptionIndex!==null?e.options[e.activeOptionIndex]:null,r=ft(i(e.options.slice()),m=>m.dataRef.current.domRef.current),a=o?r.indexOf(o):null;return a===-1&&(a=null),{options:r,activeOptionIndex:a}}let Pt={[1](e){return e.dataRef.current.disabled||e.listboxState===1?e:{...e,activeOptionIndex:null,listboxState:1,__demoMode:!1}},[0](e){if(e.dataRef.current.disabled||e.listboxState===0)return e;let i=e.activeOptionIndex,{isSelected:o}=e.dataRef.current,r=e.options.findIndex(a=>o(a.dataRef.current.value));return r!==-1&&(i=r),{...e,listboxState:0,activeOptionIndex:i,__demoMode:!1}},[2](e,i){var m,x,d,p,n;if(e.dataRef.current.disabled||e.listboxState===1)return e;let o={...e,searchQuery:"",activationTrigger:(m=i.trigger)!=null?m:1,__demoMode:!1};if(i.focus===v.Nothing)return{...o,activeOptionIndex:null};if(i.focus===v.Specific)return{...o,activeOptionIndex:e.options.findIndex(u=>u.id===i.id)};if(i.focus===v.Previous){let u=e.activeOptionIndex;if(u!==null){let P=e.options[u].dataRef.current.domRef,t=fe(i,{resolveItems:()=>e.options,resolveActiveIndex:()=>e.activeOptionIndex,resolveId:s=>s.id,resolveDisabled:s=>s.dataRef.current.disabled});if(t!==null){let s=e.options[t].dataRef.current.domRef;if(((x=P.current)==null?void 0:x.previousElementSibling)===s.current||((d=s.current)==null?void 0:d.previousElementSibling)===null)return{...o,activeOptionIndex:t}}}}else if(i.focus===v.Next){let u=e.activeOptionIndex;if(u!==null){let P=e.options[u].dataRef.current.domRef,t=fe(i,{resolveItems:()=>e.options,resolveActiveIndex:()=>e.activeOptionIndex,resolveId:s=>s.id,resolveDisabled:s=>s.dataRef.current.disabled});if(t!==null){let s=e.options[t].dataRef.current.domRef;if(((p=P.current)==null?void 0:p.nextElementSibling)===s.current||((n=s.current)==null?void 0:n.nextElementSibling)===null)return{...o,activeOptionIndex:t}}}}let r=be(e),a=fe(i,{resolveItems:()=>r.options,resolveActiveIndex:()=>r.activeOptionIndex,resolveId:u=>u.id,resolveDisabled:u=>u.dataRef.current.disabled});return{...o,...r,activeOptionIndex:a}},[3]:(e,i)=>{if(e.dataRef.current.disabled||e.listboxState===1)return e;let r=e.searchQuery!==""?0:1,a=e.searchQuery+i.value.toLowerCase(),x=(e.activeOptionIndex!==null?e.options.slice(e.activeOptionIndex+r).concat(e.options.slice(0,e.activeOptionIndex+r)):e.options).find(p=>{var n;return!p.dataRef.current.disabled&&((n=p.dataRef.current.textValue)==null?void 0:n.startsWith(a))}),d=x?e.options.indexOf(x):-1;return d===-1||d===e.activeOptionIndex?{...e,searchQuery:a}:{...e,searchQuery:a,activeOptionIndex:d,activationTrigger:1}},[4](e){return e.dataRef.current.disabled||e.listboxState===1||e.searchQuery===""?e:{...e,searchQuery:""}},[5]:(e,i)=>{let o={id:i.id,dataRef:i.dataRef},r=be(e,a=>[...a,o]);return e.activeOptionIndex===null&&e.dataRef.current.isSelected(i.dataRef.current.value)&&(r.activeOptionIndex=r.options.indexOf(o)),{...e,...r}},[6]:(e,i)=>{let o=be(e,r=>{let a=r.findIndex(m=>m.id===i.id);return a!==-1&&r.splice(a,1),r});return{...e,...o,activationTrigger:1}},[7]:(e,i)=>e.buttonElement===i.element?e:{...e,buttonElement:i.element},[8]:(e,i)=>e.optionsElement===i.element?e:{...e,optionsElement:i.element}},Te=ae(null);Te.displayName="ListboxActionsContext";function Z(e){let i=pe(Te);if(i===null){let o=new Error(`<${e} /> is missing a parent <Listbox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(o,Z),o}return i}let ee=ae(null);ee.displayName="ListboxDataContext";function Q(e){let i=pe(ee);if(i===null){let o=new Error(`<${e} /> is missing a parent <Listbox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(o,Q),o}return i}function Rt(e,i){return V(i.type,Pt,e,i)}let At=xe;function ht(e,i){var me;let o=$e(),{value:r,defaultValue:a,form:m,name:x,onChange:d,by:p,invalid:n=!1,disabled:u=o||!1,horizontal:P=!1,multiple:t=!1,__demoMode:s=!1,...F}=e;const M=P?"horizontal":"vertical";let h=j(i),D=we(a),[O=t?[]:void 0,g]=Be(r,d,D),[R,y]=Ie(Rt,{dataRef:De(),listboxState:s?0:1,options:[],searchQuery:"",activeOptionIndex:null,activationTrigger:1,optionsVisible:!1,buttonElement:null,optionsElement:null,__demoMode:s}),B=ue({static:!1,hold:!1}),w=ue(new Map),_=Me(p),b=se(f=>V(c.mode,{[1]:()=>O.some(S=>_(S,f)),[0]:()=>_(O,f)}),[O]),c=k(()=>({...R,value:O,disabled:u,invalid:n,mode:t?1:0,orientation:M,compare:_,isSelected:b,optionsPropsRef:B,listRef:w}),[O,u,n,t,R,w]);ce(()=>{R.dataRef.current=c},[c]);let N=c.listboxState===0;Ve(N,[c.buttonElement,c.optionsElement],(f,S)=>{var C;y({type:1}),ct(S,ut.Loose)||(f.preventDefault(),(C=c.buttonElement)==null||C.focus())});let L=k(()=>({open:c.listboxState===0,disabled:u,invalid:n,value:O}),[c,u,O,n]),H=T(f=>{let S=c.options.find(C=>C.id===f);S&&K(S.dataRef.current.value)}),te=T(()=>{if(c.activeOptionIndex!==null){let{dataRef:f,id:S}=c.options[c.activeOptionIndex];K(f.current.value),y({type:2,focus:v.Specific,id:S})}}),oe=T(()=>y({type:0})),X=T(()=>y({type:1})),J=Oe(),ne=T((f,S,C)=>{J.dispose(),J.microTask(()=>f===v.Specific?y({type:2,focus:v.Specific,id:S,trigger:C}):y({type:2,focus:f,trigger:C}))}),ie=T((f,S)=>(y({type:5,id:f,dataRef:S}),()=>y({type:6,id:f}))),K=T(f=>V(c.mode,{[0](){return g==null?void 0:g(f)},[1](){let S=c.value.slice(),C=S.findIndex(Re=>_(Re,f));return C===-1?S.push(f):S.splice(C,1),g==null?void 0:g(S)}})),re=T(f=>y({type:3,value:f})),$=T(()=>y({type:4})),q=T(f=>{y({type:7,element:f})}),l=T(f=>{y({type:8,element:f})}),I=k(()=>({onChange:K,registerOption:ie,goToOption:ne,closeListbox:X,openListbox:oe,selectActiveOption:te,selectOption:H,search:re,clearSearch:$,setButtonElement:q,setOptionsElement:l}),[]),[G,le]=yt({inherit:!0}),Se={ref:h},Ee=se(()=>{if(D!==void 0)return g==null?void 0:g(D)},[g,D]),Pe=W();return A.createElement(le,{value:G,props:{htmlFor:(me=c.buttonElement)==null?void 0:me.id},slot:{open:c.listboxState===0,disabled:u}},A.createElement(qe,null,A.createElement(Te.Provider,{value:I},A.createElement(ee.Provider,{value:c},A.createElement(lt,{value:V(c.listboxState,{[0]:Y.Open,[1]:Y.Closed})},x!=null&&O!=null&&A.createElement(nt,{disabled:u,data:{[x]:O},form:m,onReset:Ee}),Pe({ourProps:Se,theirProps:F,slot:L,defaultTag:At,name:"Listbox"}))))))}let Dt="button";function _t(e,i){var N;let o=Q("Listbox.Button"),r=Z("Listbox.Button"),a=de(),m=rt(),{id:x=m||`headlessui-listbox-button-${a}`,disabled:d=o.disabled||!1,autoFocus:p=!1,...n}=e,u=j(i,et(),r.setButtonElement),P=tt(),t=T(L=>{switch(L.key){case E.Enter:bt(L.currentTarget);break;case E.Space:case E.ArrowDown:L.preventDefault(),U(()=>r.openListbox()),o.value||r.goToOption(v.First);break;case E.ArrowUp:L.preventDefault(),U(()=>r.openListbox()),o.value||r.goToOption(v.Last);break}}),s=T(L=>{switch(L.key){case E.Space:L.preventDefault();break}}),F=T(L=>{var H;if(st(L.currentTarget))return L.preventDefault();o.listboxState===0?(U(()=>r.closeListbox()),(H=o.buttonElement)==null||H.focus({preventScroll:!0})):(L.preventDefault(),r.openListbox())}),M=T(L=>L.preventDefault()),h=Ot([x]),D=mt(),{isFocusVisible:O,focusProps:g}=Ae({autoFocus:p}),{isHovered:R,hoverProps:y}=he({isDisabled:d}),{pressed:B,pressProps:w}=Fe({disabled:d}),_=k(()=>({open:o.listboxState===0,active:B||o.listboxState===0,disabled:d,invalid:o.invalid,value:o.value,hover:R,focus:O,autofocus:p}),[o.listboxState,o.value,d,R,O,B,o.invalid,p]),b=ge(P(),{ref:u,id:x,type:je(e,o.buttonElement),"aria-haspopup":"listbox","aria-controls":(N=o.optionsElement)==null?void 0:N.id,"aria-expanded":o.listboxState===0,"aria-labelledby":h,"aria-describedby":D,disabled:d||void 0,autoFocus:p,onKeyDown:t,onKeyUp:s,onKeyPress:M,onClick:F},g,y,w);return W()({ourProps:b,theirProps:n,slot:_,defaultTag:Dt,name:"Listbox.Button"})}let Le=ae(!1),It="div",Ct=ve.RenderStrategy|ve.Static;function Ft(e,i){var $,q;let o=de(),{id:r=`headlessui-listbox-options-${o}`,anchor:a,portal:m=!1,modal:x=!0,transition:d=!1,...p}=e,n=ot(a),[u,P]=Ce(null);n&&(m=!0);let t=Q("Listbox.Options"),s=Z("Listbox.Options"),F=Ke(t.optionsElement),M=at(),[h,D]=Je(d,u,M!==null?(M&Y.Open)===Y.Open:t.listboxState===0);Ge(h,t.buttonElement,s.closeListbox);let O=t.__demoMode?!1:x&&t.listboxState===0;ze(O,F);let g=t.__demoMode?!1:x&&t.listboxState===0;Ne(g,{allowed:se(()=>[t.buttonElement,t.optionsElement],[t.buttonElement,t.optionsElement])});let R=t.listboxState!==0,B=ke(R,t.buttonElement)?!1:h,w=h&&t.listboxState===1,_=it(w,t.value),b=T(l=>t.compare(_,l)),c=k(()=>{var I;if(n==null||!((I=n==null?void 0:n.to)!=null&&I.includes("selection")))return null;let l=t.options.findIndex(G=>b(G.dataRef.current.value));return l===-1&&(l=0),l},[n,t.options]),N=(()=>{if(n==null)return;if(c===null)return{...n,inner:void 0};let l=Array.from(t.listRef.current.values());return{...n,inner:{listRef:{current:l},index:c}}})(),[L,H]=Ye(N),te=Ze(),oe=j(i,n?L:null,s.setOptionsElement,P),X=Oe();_e(()=>{var I;let l=t.optionsElement;l&&t.listboxState===0&&l!==((I=Tt(l))==null?void 0:I.activeElement)&&(l==null||l.focus({preventScroll:!0}))},[t.listboxState,t.optionsElement]);let J=T(l=>{var I,G;switch(X.dispose(),l.key){case E.Space:if(t.searchQuery!=="")return l.preventDefault(),l.stopPropagation(),s.search(l.key);case E.Enter:if(l.preventDefault(),l.stopPropagation(),t.activeOptionIndex!==null){let{dataRef:le}=t.options[t.activeOptionIndex];s.onChange(le.current.value)}t.mode===0&&(U(()=>s.closeListbox()),(I=t.buttonElement)==null||I.focus({preventScroll:!0}));break;case V(t.orientation,{vertical:E.ArrowDown,horizontal:E.ArrowRight}):return l.preventDefault(),l.stopPropagation(),s.goToOption(v.Next);case V(t.orientation,{vertical:E.ArrowUp,horizontal:E.ArrowLeft}):return l.preventDefault(),l.stopPropagation(),s.goToOption(v.Previous);case E.Home:case E.PageUp:return l.preventDefault(),l.stopPropagation(),s.goToOption(v.First);case E.End:case E.PageDown:return l.preventDefault(),l.stopPropagation(),s.goToOption(v.Last);case E.Escape:l.preventDefault(),l.stopPropagation(),U(()=>s.closeListbox()),(G=t.buttonElement)==null||G.focus({preventScroll:!0});return;case E.Tab:l.preventDefault(),l.stopPropagation(),U(()=>s.closeListbox()),dt(t.buttonElement,l.shiftKey?ye.Previous:ye.Next);break;default:l.key.length===1&&(s.search(l.key),X.setTimeout(()=>s.clearSearch(),350));break}}),ne=($=t.buttonElement)==null?void 0:$.id,ie=k(()=>({open:t.listboxState===0}),[t.listboxState]),K=ge(n?te():{},{id:r,ref:oe,"aria-activedescendant":t.activeOptionIndex===null||(q=t.options[t.activeOptionIndex])==null?void 0:q.id,"aria-multiselectable":t.mode===1?!0:void 0,"aria-labelledby":ne,"aria-orientation":t.orientation,onKeyDown:J,role:"listbox",tabIndex:t.listboxState===0?0:void 0,style:{...p.style,...H,"--button-width":Ue(t.buttonElement,!0).width},...Xe(D)}),re=W();return A.createElement(vt,{enabled:m?e.static||h:!1},A.createElement(ee.Provider,{value:t.mode===1?t:{...t,isSelected:b}},re({ourProps:K,theirProps:p,slot:ie,defaultTag:It,features:Ct,visible:B,name:"Listbox.Options"})))}let Mt="div";function Bt(e,i){let o=de(),{id:r=`headlessui-listbox-option-${o}`,disabled:a=!1,value:m,...x}=e,d=pe(Le)===!0,p=Q("Listbox.Option"),n=Z("Listbox.Option"),u=p.activeOptionIndex!==null?p.options[p.activeOptionIndex].id===r:!1,P=p.isSelected(m),t=ue(null),s=We(t),F=He({disabled:a,value:m,domRef:t,get textValue(){return s()}}),M=j(i,t,b=>{b?p.listRef.current.set(r,b):p.listRef.current.delete(r)});ce(()=>{if(!p.__demoMode&&p.listboxState===0&&u&&p.activationTrigger!==0)return pt().requestAnimationFrame(()=>{var b,c;(c=(b=t.current)==null?void 0:b.scrollIntoView)==null||c.call(b,{block:"nearest"})})},[t,u,p.__demoMode,p.listboxState,p.activationTrigger,p.activeOptionIndex]),ce(()=>{if(!d)return n.registerOption(r,F)},[F,r,d]);let h=T(b=>{var c;if(a)return b.preventDefault();n.onChange(m),p.mode===0&&(U(()=>n.closeListbox()),(c=p.buttonElement)==null||c.focus({preventScroll:!0}))}),D=T(()=>{if(a)return n.goToOption(v.Nothing);n.goToOption(v.Specific,r)}),O=Qe(),g=T(b=>{O.update(b),!a&&(u||n.goToOption(v.Specific,r,0))}),R=T(b=>{O.wasMoved(b)&&(a||u||n.goToOption(v.Specific,r,0))}),y=T(b=>{O.wasMoved(b)&&(a||u&&n.goToOption(v.Nothing))}),B=k(()=>({active:u,focus:u,selected:P,disabled:a,selectedOption:P&&d}),[u,P,a,d]),w=d?{}:{id:r,ref:M,role:"option",tabIndex:a===!0?void 0:-1,"aria-disabled":a===!0?!0:void 0,"aria-selected":P,disabled:void 0,onClick:h,onFocus:D,onPointerEnter:g,onMouseEnter:g,onPointerMove:R,onMouseMove:R,onPointerLeave:y,onMouseLeave:y},_=W();return!P&&d?null:_({ourProps:w,theirProps:x,slot:B,defaultTag:Mt,name:"Listbox.Option"})}let wt=xe;function kt(e,i){let{options:o,placeholder:r,...a}=e,x={ref:j(i)},d=Q("ListboxSelectedOption"),p=k(()=>({}),[]),n=d.value===void 0||d.value===null||d.mode===1&&Array.isArray(d.value)&&d.value.length===0,u=W();return A.createElement(Le.Provider,{value:!0},u({ourProps:x,theirProps:{...a,children:A.createElement(A.Fragment,null,r&&n?r:o)},slot:p,defaultTag:wt,name:"ListboxSelectedOption"}))}let Ut=z(ht),Nt=z(_t),Ht=xt,Gt=z(Ft),Vt=z(Bt),Kt=z(kt),Mo=Object.assign(Ut,{Button:Nt,Label:Ht,Options:Gt,Option:Vt,SelectedOption:Kt});export{Mo as Listbox,Nt as ListboxButton,Ht as ListboxLabel,Vt as ListboxOption,Gt as ListboxOptions,Kt as ListboxSelectedOption};
