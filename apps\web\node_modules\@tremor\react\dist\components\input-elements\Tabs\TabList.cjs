'use client';
"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("tslib"),r=require("react"),t=require("../../../contexts/BaseColorContext.cjs");require("../../../contexts/IndexContext.cjs"),require("../../../contexts/RootStylesContext.cjs"),require("../../../contexts/SelectedValueContext.cjs");var a=require("@headlessui/react"),o=require("../../../lib/tremorTwMerge.cjs"),s=require("../../../lib/utils.cjs");function l(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var i=l(r);const c=s.makeClassName("TabList"),n=r.createContext("line"),u={line:o.tremorTwMerge("flex border-b space-x-4","border-tremor-border","dark:border-dark-tremor-border"),solid:o.tremorTwMerge("inline-flex p-0.5 rounded-tremor-default space-x-1.5","bg-tremor-background-subtle","dark:bg-dark-tremor-background-subtle")},d=i.default.forwardRef(((r,s)=>{const{color:l,variant:d="line",children:b,className:x}=r,f=e.__rest(r,["color","variant","children","className"]);return i.default.createElement(a.Tab.List,Object.assign({ref:s,className:o.tremorTwMerge(c("root"),"justify-start overflow-x-clip",u[d],x)},f),i.default.createElement(n.Provider,{value:d},i.default.createElement(t.Provider,{value:l},b)))}));d.displayName="TabList",exports.TabVariantContext=n,exports.default=d;
