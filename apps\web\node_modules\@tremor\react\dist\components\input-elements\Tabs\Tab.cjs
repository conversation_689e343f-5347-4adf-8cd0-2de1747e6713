'use client';
"use strict";var e=require("tslib"),t=require("@headlessui/react"),r=require("../../../lib/theme.cjs"),a=require("../../../lib/tremorTwMerge.cjs"),o=require("../../../lib/utils.cjs"),d=require("react"),n=require("./TabList.cjs"),s=require("../../../contexts/BaseColorContext.cjs");function c(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}require("../../../contexts/IndexContext.cjs"),require("../../../contexts/RootStylesContext.cjs"),require("../../../contexts/SelectedValueContext.cjs");var l=c(d);const m=o.makeClassName("Tab");function i(e,t){switch(e){case"line":return a.tremorTwMerge("data-[selected]:border-b-2 hover:border-b-2 border-transparent transition duration-100 -mb-px px-2 py-2","hover:border-tremor-content hover:text-tremor-content-emphasis text-tremor-content","[&:not([data-selected])]:dark:hover:border-dark-tremor-content-emphasis [&:not([data-selected])]:dark:hover:text-dark-tremor-content-emphasis [&:not([data-selected])]:dark:text-dark-tremor-content",t?o.getColorClassNames(t,r.colorPalette.border).selectBorderColor:["data-[selected]:border-tremor-brand data-[selected]:text-tremor-brand","data-[selected]:dark:border-dark-tremor-brand data-[selected]:dark:text-dark-tremor-brand"]);case"solid":return a.tremorTwMerge("border-transparent border rounded-tremor-small px-2.5 py-1","data-[selected]:border-tremor-border data-[selected]:bg-tremor-background data-[selected]:shadow-tremor-input [&:not([data-selected])]:hover:text-tremor-content-emphasis data-[selected]:text-tremor-brand [&:not([data-selected])]:text-tremor-content","dark:data-[selected]:border-dark-tremor-border dark:data-[selected]:bg-dark-tremor-background dark:data-[selected]:shadow-dark-tremor-input dark:[&:not([data-selected])]:hover:text-dark-tremor-content-emphasis dark:data-[selected]:text-dark-tremor-brand dark:[&:not([data-selected])]:text-dark-tremor-content",t?o.getColorClassNames(t,r.colorPalette.text).selectTextColor:"text-tremor-content dark:text-dark-tremor-content")}}const u=l.default.forwardRef(((c,u)=>{const{icon:b,className:x,children:k}=c,h=e.__rest(c,["icon","className","children"]),p=d.useContext(n.TabVariantContext),C=d.useContext(s),f=b;return l.default.createElement(t.Tab,Object.assign({ref:u,className:a.tremorTwMerge(m("root"),"flex whitespace-nowrap truncate max-w-xs outline-none data-focus-visible:ring text-tremor-default transition duration-100",i(p,C),x,C&&o.getColorClassNames(C,r.colorPalette.text).selectTextColor)},h),f?l.default.createElement(f,{className:a.tremorTwMerge(m("icon"),"flex-none h-5 w-5",k?"mr-2":"")}):null,k?l.default.createElement("span",null,k):null)}));u.displayName="Tab",module.exports=u;
