'use client'

import { useState, useEffect, useRef } from 'react'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { aiInterviewService, InterviewQuestion, InterviewResponse, ResponseAnalysis } from '@/services/aiInterviewService'
import {
  Play,
  Pause,
  Square,
  SkipForward,
  ArrowLeft,
  Video,
  VideoOff,
  Mic,
  MicOff,
  Clock,
  Brain,
  Target,
  CheckCircle,
  AlertCircle,
  Lightbulb,
  TrendingUp,
  Award,
  RefreshCw
} from 'lucide-react'

interface InterviewState {
  isActive: boolean
  currentQuestionIndex: number
  isRecording: boolean
  isVideoEnabled: boolean
  isAudioEnabled: boolean
  startTime?: Date
  responses: InterviewResponse[]
}

export default function InterviewPracticePage() {
  const router = useRouter()
  const videoRef = useRef<HTMLVideoElement>(null)
  const mediaRecorderRef = useRef<MediaRecorder | null>(null)
  const [stream, setStream] = useState<MediaStream | null>(null)
  
  const [interviewState, setInterviewState] = useState<InterviewState>({
    isActive: false,
    currentQuestionIndex: 0,
    isRecording: false,
    isVideoEnabled: true,
    isAudioEnabled: true,
    responses: []
  })

  const [questions, setQuestions] = useState<InterviewQuestion[]>([])
  const [currentAnalysis, setCurrentAnalysis] = useState<ResponseAnalysis | null>(null)
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [sessionScore, setSessionScore] = useState(0)
  const [timeElapsed, setTimeElapsed] = useState(0)

  useEffect(() => {
    loadQuestions()
    return () => {
      if (stream) {
        stream.getTracks().forEach(track => track.stop())
      }
    }
  }, [])

  useEffect(() => {
    let interval: NodeJS.Timeout
    if (interviewState.isActive && interviewState.startTime) {
      interval = setInterval(() => {
        setTimeElapsed(Math.floor((Date.now() - interviewState.startTime!.getTime()) / 1000))
      }, 1000)
    }
    return () => clearInterval(interval)
  }, [interviewState.isActive, interviewState.startTime])

  const loadQuestions = async () => {
    try {
      const generatedQuestions = await aiInterviewService.generateQuestions({
        jobTitle: 'Software Engineer',
        industry: 'Technology',
        difficulty: 'medium',
        count: 5,
        types: ['behavioral', 'technical', 'situational']
      })
      setQuestions(generatedQuestions)
    } catch (error) {
      console.error('Error loading questions:', error)
    }
  }

  const initializeMedia = async () => {
    try {
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: interviewState.isVideoEnabled,
        audio: interviewState.isAudioEnabled
      })
      
      setStream(mediaStream)
      
      if (videoRef.current && interviewState.isVideoEnabled) {
        videoRef.current.srcObject = mediaStream
      }
      
      return mediaStream
    } catch (error) {
      console.error('Error accessing media devices:', error)
      throw error
    }
  }

  const startInterview = async () => {
    try {
      const mediaStream = await initializeMedia()
      
      setInterviewState(prev => ({
        ...prev,
        isActive: true,
        startTime: new Date()
      }))
    } catch (error) {
      console.error('Error starting interview:', error)
    }
  }

  const startRecording = async () => {
    if (!stream) return

    try {
      const mediaRecorder = new MediaRecorder(stream)
      const chunks: Blob[] = []

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunks.push(event.data)
        }
      }

      mediaRecorder.onstop = async () => {
        const blob = new Blob(chunks, { type: 'video/webm' })
        const audioUrl = URL.createObjectURL(blob)
        
        // Simulate response analysis
        await analyzeResponse(audioUrl)
      }

      mediaRecorder.start()
      mediaRecorderRef.current = mediaRecorder

      setInterviewState(prev => ({ ...prev, isRecording: true }))
    } catch (error) {
      console.error('Error starting recording:', error)
    }
  }

  const stopRecording = () => {
    if (mediaRecorderRef.current && interviewState.isRecording) {
      mediaRecorderRef.current.stop()
      setInterviewState(prev => ({ ...prev, isRecording: false }))
    }
  }

  const analyzeResponse = async (audioUrl: string) => {
    if (questions.length === 0) return

    setIsAnalyzing(true)
    try {
      const currentQuestion = questions[interviewState.currentQuestionIndex]
      const mockResponse = "I believe this is a great opportunity to demonstrate my problem-solving skills. In my previous role, I encountered a similar situation where I had to work with a team to deliver a critical project. I took the initiative to organize daily standups and implemented a new tracking system that improved our efficiency by 25%. The result was that we delivered the project two weeks ahead of schedule and received positive feedback from stakeholders."

      const analysis = await aiInterviewService.analyzeResponse({
        question: currentQuestion,
        response: mockResponse,
        audioUrl,
        duration: 120
      })

      const newResponse: InterviewResponse = {
        questionId: currentQuestion.id,
        response: mockResponse,
        duration: 120,
        audioUrl,
        analysis
      }

      setInterviewState(prev => ({
        ...prev,
        responses: [...prev.responses, newResponse]
      }))

      setCurrentAnalysis(analysis)
      
      // Update session score
      const allResponses = [...interviewState.responses, newResponse]
      const avgScore = allResponses.reduce((sum, r) => sum + (r.analysis?.score || 0), 0) / allResponses.length
      setSessionScore(avgScore)

    } catch (error) {
      console.error('Error analyzing response:', error)
    } finally {
      setIsAnalyzing(false)
    }
  }

  const nextQuestion = () => {
    if (interviewState.currentQuestionIndex < questions.length - 1) {
      setInterviewState(prev => ({
        ...prev,
        currentQuestionIndex: prev.currentQuestionIndex + 1
      }))
      setCurrentAnalysis(null)
    } else {
      finishInterview()
    }
  }

  const finishInterview = async () => {
    if (stream) {
      stream.getTracks().forEach(track => track.stop())
    }

    // Generate final feedback
    try {
      const feedback = await aiInterviewService.generateFeedback(interviewState.responses)
      // Store results in sessionStorage for the results page
      sessionStorage.setItem('interviewResults', JSON.stringify({
        score: sessionScore,
        feedback,
        responses: interviewState.responses,
        duration: timeElapsed
      }))
      router.push('/dashboard/interviews/results')
    } catch (error) {
      console.error('Error generating feedback:', error)
      router.push('/dashboard/interviews')
    }
  }

  const toggleVideo = () => {
    setInterviewState(prev => ({ ...prev, isVideoEnabled: !prev.isVideoEnabled }))
    if (stream) {
      const videoTrack = stream.getVideoTracks()[0]
      if (videoTrack) {
        videoTrack.enabled = !interviewState.isVideoEnabled
      }
    }
  }

  const toggleAudio = () => {
    setInterviewState(prev => ({ ...prev, isAudioEnabled: !prev.isAudioEnabled }))
    if (stream) {
      const audioTrack = stream.getAudioTracks()[0]
      if (audioTrack) {
        audioTrack.enabled = !interviewState.isAudioEnabled
      }
    }
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-emerald-600 dark:text-emerald-400'
    if (score >= 70) return 'text-amber-600 dark:text-amber-400'
    return 'text-red-600 dark:text-red-400'
  }

  const currentQuestion = questions[interviewState.currentQuestionIndex]

  return (
    <div className="max-w-6xl mx-auto space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.back()}
            className="flex items-center space-x-2"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Back</span>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-foreground flex items-center space-x-2">
              <Brain className="h-8 w-8 text-primary" />
              <span>AI Interview Practice</span>
            </h1>
            <p className="text-muted-foreground mt-1">
              Practice with real-time AI feedback and analysis
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Clock className="h-4 w-4 text-muted-foreground" />
            <span className="font-mono text-lg text-foreground">{formatTime(timeElapsed)}</span>
          </div>
          {sessionScore > 0 && (
            <div className="flex items-center space-x-2">
              <Target className="h-4 w-4 text-primary" />
              <span className={`font-bold ${getScoreColor(sessionScore)}`}>
                {sessionScore.toFixed(0)}%
              </span>
            </div>
          )}
        </div>
      </div>

      {/* Progress Bar */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium">
              Question {interviewState.currentQuestionIndex + 1} of {questions.length}
            </span>
            <Badge variant="outline">
              {Math.round(((interviewState.currentQuestionIndex + 1) / questions.length) * 100)}% Complete
            </Badge>
          </div>
          <Progress value={((interviewState.currentQuestionIndex + 1) / questions.length) * 100} className="h-2" />
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Interview Area */}
        <div className="lg:col-span-2 space-y-6">
          {/* Video/Audio Area */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Video className="h-5 w-5 text-blue-600" />
                <span>Interview Session</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Video Display */}
              <div className="relative bg-muted rounded-lg overflow-hidden aspect-video">
                {interviewState.isVideoEnabled ? (
                  <video
                    ref={videoRef}
                    autoPlay
                    muted
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full">
                    <VideoOff className="h-16 w-16 text-muted-foreground" />
                  </div>
                )}
                
                {/* Recording Indicator */}
                {interviewState.isRecording && (
                  <div className="absolute top-4 left-4 flex items-center space-x-2 bg-red-600 text-white px-3 py-1 rounded-full">
                    <div className="w-2 h-2 bg-white rounded-full animate-pulse" />
                    <span className="text-sm font-medium">Recording</span>
                  </div>
                )}

                {/* Controls Overlay */}
                <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex items-center space-x-4">
                  <Button
                    size="sm"
                    variant={interviewState.isVideoEnabled ? "default" : "secondary"}
                    onClick={toggleVideo}
                  >
                    {interviewState.isVideoEnabled ? <Video className="h-4 w-4" /> : <VideoOff className="h-4 w-4" />}
                  </Button>
                  <Button
                    size="sm"
                    variant={interviewState.isAudioEnabled ? "default" : "secondary"}
                    onClick={toggleAudio}
                  >
                    {interviewState.isAudioEnabled ? <Mic className="h-4 w-4" /> : <MicOff className="h-4 w-4" />}
                  </Button>
                </div>
              </div>

              {/* Interview Controls */}
              <div className="flex items-center justify-center space-x-4">
                {!interviewState.isActive ? (
                  <Button onClick={startInterview} size="lg" className="flex items-center space-x-2">
                    <Play className="h-5 w-5" />
                    <span>Start Interview</span>
                  </Button>
                ) : (
                  <>
                    {!interviewState.isRecording ? (
                      <Button onClick={startRecording} size="lg" className="flex items-center space-x-2">
                        <Play className="h-5 w-5" />
                        <span>Start Recording</span>
                      </Button>
                    ) : (
                      <Button onClick={stopRecording} size="lg" variant="destructive" className="flex items-center space-x-2">
                        <Square className="h-5 w-5" />
                        <span>Stop Recording</span>
                      </Button>
                    )}
                    <Button onClick={nextQuestion} variant="outline" size="lg" className="flex items-center space-x-2">
                      <SkipForward className="h-5 w-5" />
                      <span>Next Question</span>
                    </Button>
                  </>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Current Question */}
          {currentQuestion && (
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center space-x-2">
                    <span>Interview Question</span>
                  </CardTitle>
                  <Badge variant="outline">{currentQuestion.type}</Badge>
                </div>
                <CardDescription>
                  Category: {currentQuestion.category} • Expected time: {currentQuestion.expectedDuration}s
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-lg font-medium text-gray-900 p-4 bg-blue-50 rounded-lg">
                  {currentQuestion.question}
                </div>
                
                {currentQuestion.tips && (
                  <div className="space-y-2">
                    <h4 className="font-medium text-sm flex items-center space-x-2">
                      <Lightbulb className="h-4 w-4 text-yellow-600" />
                      <span>Tips for answering:</span>
                    </h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      {currentQuestion.tips.map((tip, index) => (
                        <li key={index} className="flex items-start space-x-2">
                          <span className="text-blue-600">•</span>
                          <span>{tip}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>

        {/* AI Analysis Sidebar */}
        <div className="lg:col-span-1">
          <div className="sticky top-8 space-y-6">
            {/* Real-time Analysis */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Brain className="h-5 w-5 text-purple-600" />
                  <span>AI Analysis</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {isAnalyzing ? (
                  <div className="flex items-center justify-center py-8">
                    <RefreshCw className="h-8 w-8 animate-spin text-blue-600" />
                    <span className="ml-2">Analyzing response...</span>
                  </div>
                ) : currentAnalysis ? (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">Response Score</span>
                      <span className={`text-lg font-bold ${getScoreColor(currentAnalysis.score)}`}>
                        {currentAnalysis.score}%
                      </span>
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Clarity</span>
                        <span>{currentAnalysis.clarity}%</span>
                      </div>
                      <Progress value={currentAnalysis.clarity} className="h-1" />
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Structure</span>
                        <span>{currentAnalysis.structure}%</span>
                      </div>
                      <Progress value={currentAnalysis.structure} className="h-1" />
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Relevance</span>
                        <span>{currentAnalysis.relevance}%</span>
                      </div>
                      <Progress value={currentAnalysis.relevance} className="h-1" />
                    </div>

                    {currentAnalysis.strengths.length > 0 && (
                      <div>
                        <h4 className="font-medium text-sm text-green-600 mb-2">Strengths:</h4>
                        <ul className="text-xs text-gray-600 space-y-1">
                          {currentAnalysis.strengths.map((strength, index) => (
                            <li key={index} className="flex items-start space-x-1">
                              <CheckCircle className="h-3 w-3 text-green-600 mt-0.5" />
                              <span>{strength}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {currentAnalysis.improvements.length > 0 && (
                      <div>
                        <h4 className="font-medium text-sm text-orange-600 mb-2">Improvements:</h4>
                        <ul className="text-xs text-gray-600 space-y-1">
                          {currentAnalysis.improvements.map((improvement, index) => (
                            <li key={index} className="flex items-start space-x-1">
                              <AlertCircle className="h-3 w-3 text-orange-600 mt-0.5" />
                              <span>{improvement}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <Brain className="h-12 w-12 mx-auto mb-2 text-gray-400" />
                    <p className="text-sm">Start recording to get AI feedback</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Session Progress */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <TrendingUp className="h-5 w-5 text-green-600" />
                  <span>Session Progress</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center">
                  <div className={`text-2xl font-bold ${getScoreColor(sessionScore)}`}>
                    {sessionScore.toFixed(0)}%
                  </div>
                  <div className="text-sm text-gray-600">Average Score</div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Questions Completed</span>
                    <span>{interviewState.responses.length}/{questions.length}</span>
                  </div>
                  <Progress value={(interviewState.responses.length / questions.length) * 100} className="h-2" />
                </div>

                <div className="text-center">
                  <div className="text-lg font-bold text-blue-600">{formatTime(timeElapsed)}</div>
                  <div className="text-sm text-gray-600">Time Elapsed</div>
                </div>
              </CardContent>
            </Card>

            {/* Quick Tips */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Award className="h-5 w-5 text-yellow-600" />
                  <span>Quick Tips</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 text-sm">
                  <div className="flex items-start space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                    <span>Maintain eye contact with the camera</span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                    <span>Use the STAR method for behavioral questions</span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                    <span>Speak clearly and at a moderate pace</span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                    <span>Include specific examples and results</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
