import{useFloating as e,autoUpdate as t,offset as o,flip as r,shift as s,useHover as a,useFocus as i,useDismiss as n,useRole as l,useInteractions as p}from"@floating-ui/react";import{tremorTwMerge as m}from"../../../lib/tremorTwMerge.js";import g,{useState as c}from"react";const f=m=>{const[g,f]=c(!1),[d,x]=c(),{x:u,y:y,refs:b,strategy:h,context:w}=e({open:g,onOpenChange:e=>{if(e&&m){const t=setTimeout((()=>{f(e)}),m);x(t)}else clearTimeout(d),f(e)},placement:"top",whileElementsMounted:t,middleware:[o(5),r({fallbackAxisSideDirection:"start"}),s()]}),P=a(w,{move:!1}),k=i(w),F=n(w),T=l(w,{role:"tooltip"}),{getReferenceProps:j,getFloatingProps:v}=p([P,k,F,T]);return{tooltipProps:{open:g,x:u,y:y,refs:b,strategy:h,getFloatingProps:v},getReferenceProps:j}},d=({text:e,open:t,x:o,y:r,refs:s,strategy:a,getFloatingProps:i})=>t&&e?g.createElement("div",Object.assign({className:m("max-w-xs text-sm z-20 rounded-tremor-default opacity-100 px-2.5 py-1","text-white bg-tremor-background-emphasis","dark:text-tremor-content-emphasis dark:bg-white"),ref:s.setFloating,style:{position:a,top:null!=r?r:0,left:null!=o?o:0}},i()),e):null;d.displayName="Tooltip";export{d as default,f as useTooltip};
