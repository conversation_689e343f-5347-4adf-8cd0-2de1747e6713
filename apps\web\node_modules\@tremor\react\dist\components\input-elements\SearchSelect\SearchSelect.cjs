'use client';
"use strict";var e=require("tslib"),r=require("../../../hooks/useInternalState.cjs"),t=require("react"),a=require("@headlessui/react"),l=require("../../../assets/ArrowDownHeadIcon.cjs"),o=require("../../../assets/XCircleIcon.cjs"),n=require("../../../lib/tremorTwMerge.cjs"),d=require("../../../lib/utils.cjs"),s=require("../selectUtils.cjs");function u(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var c=u(t);const m=d.makeClassName("SearchSelect"),i=d.makeClassName("SearchSelect"),f=c.default.forwardRef(((d,u)=>{const{defaultValue:f="",searchValue:b,onSearchValueChange:p,value:h,onValueChange:g,placeholder:w="Select...",disabled:k=!1,icon:v,enableClear:x=!0,name:C,required:N,error:y=!1,errorMessage:E,children:M,className:T,id:V,autoComplete:q="off"}=d,S=e.__rest(d,["defaultValue","searchValue","onSearchValueChange","value","onValueChange","placeholder","disabled","icon","enableClear","name","required","error","errorMessage","children","className","id","autoComplete"]),j=t.useRef(null),[I,F]=r("",b),[D,O]=r(f,h),z=v,{reactElementChildren:A,valueToNameMapping:B}=t.useMemo((()=>{const e=c.default.Children.toArray(M).filter(t.isValidElement);return{reactElementChildren:e,valueToNameMapping:s.constructValueToNameMapping(e)}}),[M]),R=t.useMemo((()=>s.getFilteredOptions(null!=I?I:"",A)),[I,A]);return c.default.createElement("div",{className:n.tremorTwMerge("w-full min-w-[10rem] text-tremor-default",T)},c.default.createElement("div",{className:"relative"},c.default.createElement("select",{title:"search-select-hidden",required:N,className:n.tremorTwMerge("h-full w-full absolute left-0 top-0 -z-10 opacity-0"),value:D,onChange:e=>{e.preventDefault()},name:C,disabled:k,id:V,onFocus:()=>{const e=j.current;e&&e.focus()}},c.default.createElement("option",{className:"hidden",value:"",disabled:!0,hidden:!0},w),R.map((e=>{const r=e.props.value,t=e.props.children;return c.default.createElement("option",{className:"hidden",key:r,value:r},t)}))),c.default.createElement(a.Combobox,Object.assign({as:"div",ref:u,defaultValue:D,value:D,onChange:e=>{null==g||g(e),O(e)},disabled:k,id:V},S),(({value:e})=>c.default.createElement(c.default.Fragment,null,c.default.createElement(a.ComboboxButton,{className:"w-full"},z&&c.default.createElement("span",{className:n.tremorTwMerge("absolute inset-y-0 left-0 flex items-center ml-px pl-2.5")},c.default.createElement(z,{className:n.tremorTwMerge(m("Icon"),"flex-none h-5 w-5","text-tremor-content-subtle","dark:text-dark-tremor-content-subtle")})),c.default.createElement(a.ComboboxInput,{ref:j,className:n.tremorTwMerge("w-full outline-none text-left whitespace-nowrap truncate rounded-tremor-default focus:ring-2 transition duration-100 text-tremor-default pr-14 border py-2","border-tremor-border shadow-tremor-input focus:border-tremor-brand-subtle focus:ring-tremor-brand-muted","dark:border-dark-tremor-border dark:shadow-dark-tremor-input dark:focus:border-dark-tremor-brand-subtle dark:focus:ring-dark-tremor-brand-muted",z?"pl-10":"pl-3",k?"placeholder:text-tremor-content-subtle dark:placeholder:text-tremor-content-subtle":"placeholder:text-tremor-content dark:placeholder:text-tremor-content",s.getSelectButtonColors(s.hasValue(e),k,y)),placeholder:w,onChange:e=>{null==p||p(e.target.value),F(e.target.value)},displayValue:e=>{var r;return null!==(r=B.get(e))&&void 0!==r?r:""},autoComplete:q}),c.default.createElement("div",{className:n.tremorTwMerge("absolute inset-y-0 right-0 flex items-center pr-2.5")},c.default.createElement(l,{className:n.tremorTwMerge(m("arrowDownIcon"),"flex-none h-5 w-5","!text-tremor-content-subtle","!dark:text-dark-tremor-content-subtle")}))),x&&D?c.default.createElement("button",{type:"button",className:n.tremorTwMerge("absolute inset-y-0 right-0 flex items-center mr-8"),onClick:e=>{e.preventDefault(),O(""),F(""),null==g||g(""),null==p||p("")}},c.default.createElement(o,{className:n.tremorTwMerge(i("clearIcon"),"flex-none h-4 w-4","text-tremor-content-subtle","dark:text-dark-tremor-content-subtle")})):null,R.length>0&&c.default.createElement(a.Transition,{enter:"transition ease duration-100 transform",enterFrom:"opacity-0 -translate-y-4",enterTo:"opacity-100 translate-y-0",leave:"transition ease duration-100 transform",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 -translate-y-4"},c.default.createElement(a.ComboboxOptions,{anchor:"bottom start",className:n.tremorTwMerge("z-10 divide-y w-[var(--button-width)] overflow-y-auto outline-none rounded-tremor-default text-tremor-default max-h-[228px] border [--anchor-gap:4px]","bg-tremor-background border-tremor-border divide-tremor-border shadow-tremor-dropdown","dark:bg-dark-tremor-background dark:border-dark-tremor-border dark:divide-dark-tremor-border dark:shadow-dark-tremor-dropdown")},R)))))),y&&E?c.default.createElement("p",{className:n.tremorTwMerge("errorMessage","text-sm text-rose-500 mt-1")},E):null)}));f.displayName="SearchSelect",module.exports=f;
