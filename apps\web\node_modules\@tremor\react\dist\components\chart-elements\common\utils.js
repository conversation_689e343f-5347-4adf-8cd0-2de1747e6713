const t=(t,n)=>{const e=new Map;return t.forEach(((t,r)=>{e.set(t,n[r%n.length])})),e},n=(t,n,e)=>[t?"auto":null!=n?n:0,null!=e?e:"auto"],e=(t,n)=>{if(!n)return[];const e=new Set;return t.forEach((t=>{e.add(t[n])})),Array.from(e)};function r(t,n){if(t===n)return!0;if("object"!=typeof t||"object"!=typeof n||null===t||null===n)return!1;const e=Object.keys(t),o=Object.keys(n);if(e.length!==o.length)return!1;for(const u of e)if(!o.includes(u)||!r(t[u],n[u]))return!1;return!0}function o(t,n){const e=[];for(const r of t)if(Object.prototype.hasOwnProperty.call(r,n)&&(e.push(r[n]),e.length>1))return!1;return!0}export{e as constructCategories,t as constructCategoryColors,r as deepEqual,n as getYAxisDomain,o as hasOnlyOneValueForThisKey};
