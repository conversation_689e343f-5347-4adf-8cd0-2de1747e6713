"use strict";var e=require("tslib"),r=require("../../../lib/tremorTwMerge.cjs");function t(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var a=t(require("react"));const i=a.default.forwardRef(((t,i)=>{const{children:s,className:c}=t,l=e.__rest(t,["children","className"]);return a.default.createElement("i",Object.assign({ref:i,className:r.tremorTwMerge("italic text-inherit",c)},l),s)}));i.displayName="Italic",module.exports=i;
