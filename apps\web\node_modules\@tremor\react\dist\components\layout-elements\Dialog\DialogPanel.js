import{__rest as e}from"tslib";import t from"react";import{TransitionChild as r,DialogPanel as o}from"@headlessui/react";import{tremorTwMerge as a}from"../../../lib/tremorTwMerge.js";import{makeClassName as n}from"../../../lib/utils.js";import"../../../contexts/BaseColorContext.js";import"../../../contexts/IndexContext.js";import l from"../../../contexts/RootStylesContext.js";import"../../../contexts/SelectedValueContext.js";const s=n("dialog"),i=t.forwardRef(((n,i)=>{var m;const{children:c,className:d}=n,p=e(n,["children","className"]),f=null!==(m=t.useContext(l))&&void 0!==m?m:a("rounded-tremor-default p-6");return t.createElement(r,{enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95"},t.createElement(o,Object.assign({ref:i,className:a(s("panel"),"w-full max-w-lg overflow-visible text-left ring-1 shadow-tremor transition-all transform","bg-tremor-background  text-tremor-content ring-tremor-ring","dark:bg-dark-tremor-background dark:text-dark-tremor-content dark:ring-dark-tremor-ring",f,d)},p),c))}));i.displayName="DialogPanel";export{i as default};
