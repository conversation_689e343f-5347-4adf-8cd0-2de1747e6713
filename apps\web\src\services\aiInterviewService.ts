import { apiClient } from '@/lib/api'

export interface InterviewQuestion {
  id: string
  question: string
  type: 'behavioral' | 'technical' | 'situational' | 'company-specific'
  difficulty: 'easy' | 'medium' | 'hard'
  category: string
  expectedDuration: number // in seconds
  followUpQuestions?: string[]
  tips?: string[]
  sampleAnswer?: string
}

export interface InterviewSession {
  id: string
  title: string
  jobTitle: string
  company?: string
  industry: string
  duration: number
  questions: InterviewQuestion[]
  status: 'scheduled' | 'in-progress' | 'completed' | 'cancelled'
  createdAt: Date
  completedAt?: Date
  overallScore?: number
  feedback?: string
}

export interface InterviewResponse {
  questionId: string
  response: string
  duration: number
  audioUrl?: string
  videoUrl?: string
  transcript?: string
  analysis?: ResponseAnalysis
}

export interface ResponseAnalysis {
  score: number
  strengths: string[]
  improvements: string[]
  keywordMatch: number
  clarity: number
  confidence: number
  structure: number
  relevance: number
  suggestions: string[]
}

export interface InterviewFeedback {
  overallScore: number
  categoryScores: {
    communication: number
    technical: number
    behavioral: number
    cultural: number
  }
  strengths: string[]
  improvements: string[]
  recommendations: string[]
  nextSteps: string[]
}

export interface AICoachSuggestion {
  type: 'preparation' | 'practice' | 'improvement' | 'strategy'
  title: string
  description: string
  priority: 'high' | 'medium' | 'low'
  actionItems: string[]
  estimatedTime: number
}

class AIInterviewService {
  // Generate interview questions based on job role and company
  async generateQuestions(params: {
    jobTitle: string
    industry: string
    company?: string
    difficulty?: 'easy' | 'medium' | 'hard'
    count?: number
    types?: string[]
  }): Promise<InterviewQuestion[]> {
    try {
      // Mock AI-generated questions - replace with actual AI API call
      const questionTemplates = {
        'software-engineer': [
          {
            question: "Tell me about a challenging technical problem you solved recently.",
            type: 'technical' as const,
            category: 'Problem Solving',
            tips: ['Use the STAR method', 'Focus on your specific contribution', 'Explain your thought process']
          },
          {
            question: "How do you handle code reviews and feedback from senior developers?",
            type: 'behavioral' as const,
            category: 'Collaboration',
            tips: ['Show openness to feedback', 'Demonstrate learning mindset', 'Give specific examples']
          },
          {
            question: "Describe your experience with agile development methodologies.",
            type: 'technical' as const,
            category: 'Process',
            tips: ['Mention specific frameworks', 'Discuss team collaboration', 'Share measurable outcomes']
          }
        ],
        'product-manager': [
          {
            question: "How would you prioritize features for a product with limited resources?",
            type: 'situational' as const,
            category: 'Strategy',
            tips: ['Mention frameworks like RICE', 'Consider user impact', 'Discuss stakeholder alignment']
          },
          {
            question: "Tell me about a time you had to make a difficult product decision.",
            type: 'behavioral' as const,
            category: 'Decision Making',
            tips: ['Use data to support decisions', 'Show stakeholder management', 'Explain the outcome']
          }
        ],
        'data-scientist': [
          {
            question: "Walk me through your approach to a machine learning project from start to finish.",
            type: 'technical' as const,
            category: 'Methodology',
            tips: ['Cover data collection to deployment', 'Mention validation techniques', 'Discuss business impact']
          },
          {
            question: "How do you communicate complex data insights to non-technical stakeholders?",
            type: 'behavioral' as const,
            category: 'Communication',
            tips: ['Use visualization examples', 'Simplify technical concepts', 'Focus on business value']
          }
        ]
      }

      const key = params.jobTitle.toLowerCase().replace(/\s+/g, '-')
      const templates = questionTemplates[key as keyof typeof questionTemplates] || questionTemplates['software-engineer']
      
      return templates.map((template, index) => ({
        id: `q-${index + 1}`,
        question: template.question,
        type: template.type,
        difficulty: params.difficulty || 'medium',
        category: template.category,
        expectedDuration: 120, // 2 minutes
        tips: template.tips,
        followUpQuestions: [
          "Can you elaborate on that?",
          "What would you do differently next time?",
          "How did this experience change your approach?"
        ]
      }))
    } catch (error) {
      console.error('Error generating questions:', error)
      return []
    }
  }

  // Analyze interview response using AI
  async analyzeResponse(params: {
    question: InterviewQuestion
    response: string
    audioUrl?: string
    duration: number
  }): Promise<ResponseAnalysis> {
    try {
      // Mock AI analysis - replace with actual AI API call
      const response = params.response.toLowerCase()
      
      // Simple scoring algorithm - replace with sophisticated AI
      let score = 60 // Base score
      
      // Check for STAR method structure
      if (response.includes('situation') || response.includes('task') || 
          response.includes('action') || response.includes('result')) {
        score += 15
      }
      
      // Check for specific examples
      if (response.includes('example') || response.includes('specifically') || 
          response.includes('for instance')) {
        score += 10
      }
      
      // Check for quantifiable results
      if (/\d+%|\$\d+|\d+\s*(users|customers|projects)/.test(response)) {
        score += 15
      }
      
      // Check response length (optimal 60-180 seconds)
      if (params.duration >= 60 && params.duration <= 180) {
        score += 10
      } else if (params.duration < 30) {
        score -= 10
      }

      const analysis: ResponseAnalysis = {
        score: Math.min(score, 100),
        strengths: this.generateStrengths(response, score),
        improvements: this.generateImprovements(response, score),
        keywordMatch: this.calculateKeywordMatch(params.question, response),
        clarity: this.assessClarity(response),
        confidence: this.assessConfidence(response),
        structure: this.assessStructure(response),
        relevance: this.assessRelevance(params.question, response),
        suggestions: this.generateSuggestions(params.question, response, score)
      }

      return analysis
    } catch (error) {
      console.error('Error analyzing response:', error)
      return {
        score: 50,
        strengths: [],
        improvements: ['Unable to analyze response'],
        keywordMatch: 0,
        clarity: 50,
        confidence: 50,
        structure: 50,
        relevance: 50,
        suggestions: []
      }
    }
  }

  // Generate personalized coaching suggestions
  async generateCoachingSuggestions(params: {
    jobTitle: string
    industry: string
    recentPerformance: number[]
    weakAreas: string[]
  }): Promise<AICoachSuggestion[]> {
    try {
      const suggestions: AICoachSuggestion[] = []

      // Performance-based suggestions
      const avgScore = params.recentPerformance.reduce((a, b) => a + b, 0) / params.recentPerformance.length

      if (avgScore < 70) {
        suggestions.push({
          type: 'preparation',
          title: 'Intensive Interview Preparation',
          description: 'Focus on fundamental interview skills and common question patterns',
          priority: 'high',
          actionItems: [
            'Practice STAR method responses',
            'Record yourself answering questions',
            'Research common interview questions for your role',
            'Prepare 5-7 strong examples from your experience'
          ],
          estimatedTime: 120 // 2 hours
        })
      }

      // Weak area specific suggestions
      if (params.weakAreas.includes('technical')) {
        suggestions.push({
          type: 'practice',
          title: 'Technical Skills Enhancement',
          description: 'Strengthen your technical interview performance',
          priority: 'high',
          actionItems: [
            'Practice coding problems on LeetCode/HackerRank',
            'Review system design concepts',
            'Prepare technical project explanations',
            'Practice whiteboarding exercises'
          ],
          estimatedTime: 180 // 3 hours
        })
      }

      if (params.weakAreas.includes('behavioral')) {
        suggestions.push({
          type: 'improvement',
          title: 'Behavioral Interview Mastery',
          description: 'Improve your storytelling and behavioral responses',
          priority: 'medium',
          actionItems: [
            'Develop compelling personal stories',
            'Practice emotional intelligence scenarios',
            'Work on active listening skills',
            'Prepare leadership and teamwork examples'
          ],
          estimatedTime: 90 // 1.5 hours
        })
      }

      return suggestions
    } catch (error) {
      console.error('Error generating coaching suggestions:', error)
      return []
    }
  }

  // Generate comprehensive interview feedback
  async generateFeedback(responses: InterviewResponse[]): Promise<InterviewFeedback> {
    try {
      const analyses = responses.map(r => r.analysis).filter(Boolean) as ResponseAnalysis[]
      
      if (analyses.length === 0) {
        throw new Error('No response analyses available')
      }

      const overallScore = analyses.reduce((sum, a) => sum + a.score, 0) / analyses.length

      const categoryScores = {
        communication: analyses.reduce((sum, a) => sum + a.clarity, 0) / analyses.length,
        technical: analyses.reduce((sum, a) => sum + a.relevance, 0) / analyses.length,
        behavioral: analyses.reduce((sum, a) => sum + a.structure, 0) / analyses.length,
        cultural: analyses.reduce((sum, a) => sum + a.confidence, 0) / analyses.length
      }

      const allStrengths = analyses.flatMap(a => a.strengths)
      const allImprovements = analyses.flatMap(a => a.improvements)

      return {
        overallScore,
        categoryScores,
        strengths: [...new Set(allStrengths)].slice(0, 5),
        improvements: [...new Set(allImprovements)].slice(0, 5),
        recommendations: this.generateRecommendations(overallScore, categoryScores),
        nextSteps: this.generateNextSteps(overallScore, categoryScores)
      }
    } catch (error) {
      console.error('Error generating feedback:', error)
      return {
        overallScore: 50,
        categoryScores: { communication: 50, technical: 50, behavioral: 50, cultural: 50 },
        strengths: [],
        improvements: [],
        recommendations: [],
        nextSteps: []
      }
    }
  }

  // Helper methods
  private generateStrengths(response: string, score: number): string[] {
    const strengths = []
    if (response.length > 100) strengths.push('Provided detailed response')
    if (response.includes('team') || response.includes('collaborate')) strengths.push('Demonstrated teamwork')
    if (/\d+%|\$\d+/.test(response)) strengths.push('Included quantifiable results')
    if (score > 80) strengths.push('Well-structured answer')
    return strengths
  }

  private generateImprovements(response: string, score: number): string[] {
    const improvements = []
    if (response.length < 50) improvements.push('Provide more detailed examples')
    if (score < 70) improvements.push('Use the STAR method for better structure')
    if (!response.includes('result') && !response.includes('outcome')) {
      improvements.push('Include specific outcomes and results')
    }
    return improvements
  }

  private calculateKeywordMatch(question: InterviewQuestion, response: string): number {
    const questionWords = question.question.toLowerCase().split(' ')
    const responseWords = response.toLowerCase().split(' ')
    const matches = questionWords.filter(word => responseWords.includes(word))
    return (matches.length / questionWords.length) * 100
  }

  private assessClarity(response: string): number {
    // Simple clarity assessment - replace with NLP
    const sentences = response.split(/[.!?]+/).filter(s => s.trim().length > 0)
    const avgSentenceLength = response.length / sentences.length
    return avgSentenceLength > 20 && avgSentenceLength < 100 ? 80 : 60
  }

  private assessConfidence(response: string): number {
    // Simple confidence assessment
    const uncertainWords = ['maybe', 'perhaps', 'i think', 'probably', 'might']
    const uncertainCount = uncertainWords.filter(word => response.toLowerCase().includes(word)).length
    return Math.max(40, 90 - (uncertainCount * 10))
  }

  private assessStructure(response: string): number {
    // Check for logical structure
    const structureWords = ['first', 'then', 'next', 'finally', 'because', 'therefore']
    const structureCount = structureWords.filter(word => response.toLowerCase().includes(word)).length
    return Math.min(90, 50 + (structureCount * 10))
  }

  private assessRelevance(question: InterviewQuestion, response: string): number {
    // Simple relevance check
    const questionKeywords = question.category.toLowerCase().split(' ')
    const responseText = response.toLowerCase()
    const relevantCount = questionKeywords.filter(keyword => responseText.includes(keyword)).length
    return Math.min(90, 40 + (relevantCount * 20))
  }

  private generateSuggestions(question: InterviewQuestion, response: string, score: number): string[] {
    const suggestions = []
    
    if (score < 70) {
      suggestions.push('Practice the STAR method for more structured responses')
    }
    
    if (response.length < 100) {
      suggestions.push('Provide more specific examples and details')
    }
    
    if (!response.includes('result')) {
      suggestions.push('Always conclude with the results or outcomes of your actions')
    }
    
    return suggestions
  }

  private generateRecommendations(overallScore: number, categoryScores: any): string[] {
    const recommendations = []
    
    if (overallScore < 70) {
      recommendations.push('Focus on fundamental interview preparation')
      recommendations.push('Practice with mock interviews regularly')
    }
    
    if (categoryScores.communication < 70) {
      recommendations.push('Work on clear and concise communication')
    }
    
    if (categoryScores.technical < 70) {
      recommendations.push('Strengthen technical knowledge and examples')
    }
    
    return recommendations
  }

  private generateNextSteps(overallScore: number, categoryScores: any): string[] {
    const nextSteps = []
    
    if (overallScore >= 80) {
      nextSteps.push('You\'re ready for real interviews!')
      nextSteps.push('Focus on company-specific preparation')
    } else {
      nextSteps.push('Continue practicing with mock interviews')
      nextSteps.push('Work on identified improvement areas')
    }
    
    return nextSteps
  }
}

export const aiInterviewService = new AIInterviewService()
