!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react"),require("react-dom"),require("@floating-ui/react-dom")):"function"==typeof define&&define.amd?define(["exports","react","react-dom","@floating-ui/react-dom"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).FloatingUIReactDOM={},e.<PERSON><PERSON>,e.ReactDOM,e.FloatingUIReactDOM)}(this,(function(e,t,n,r){"use strict";function o(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(n){if("default"!==n){var r=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,r.get?r:{enumerable:!0,get:function(){return e[n]}})}})),t.default=e,Object.freeze(t)}var u=o(t),i="undefined"!=typeof document?t.useLayoutEffect:t.useEffect;let c=!1,l=0;const s=()=>"floating-ui-"+l++;const a=u["useId".toString()]||function(){const[e,t]=u.useState((()=>c?s():void 0));return i((()=>{null==e&&t(s())}),[]),u.useEffect((()=>{c||(c=!0)}),[]),e};function f(){const e=new Map;return{emit(t,n){var r;null==(r=e.get(t))||r.forEach((e=>e(n)))},on(t,n){e.set(t,[...e.get(t)||[],n])},off(t,n){e.set(t,(e.get(t)||[]).filter((e=>e!==n)))}}}const d=u.createContext(null),m=u.createContext(null),p=()=>{var e;return(null==(e=u.useContext(d))?void 0:e.id)||null},v=()=>u.useContext(m);function g(e){return(null==e?void 0:e.ownerDocument)||document}function b(){const e=navigator.userAgentData;return null!=e&&e.platform?e.platform:navigator.platform}function y(){const e=navigator.userAgentData;return e&&Array.isArray(e.brands)?e.brands.map((e=>{let{brand:t,version:n}=e;return t+"/"+n})).join(" "):navigator.userAgent}function h(e){return g(e).defaultView||window}function w(e){return!!e&&e instanceof h(e).Element}function E(e){return!!e&&e instanceof h(e).HTMLElement}function R(e){if(0===e.mozInputSource&&e.isTrusted)return!0;const t=/Android/i;return(t.test(b())||t.test(y()))&&e.pointerType?"click"===e.type&&1===e.buttons:0===e.detail&&!e.pointerType}function x(e){return 0===e.width&&0===e.height||1===e.width&&1===e.height&&0===e.pressure&&0===e.detail&&"mouse"!==e.pointerType||e.width<1&&e.height<1&&0===e.pressure&&0===e.detail}function I(){return/apple/i.test(navigator.vendor)}function O(){return b().toLowerCase().startsWith("mac")&&!navigator.maxTouchPoints}function k(e,t){const n=["mouse","pen"];return t||n.push("",void 0),n.includes(e)}function T(e){const n=t.useRef(e);return i((()=>{n.current=e})),n}const S="data-floating-ui-safe-polygon";function P(e,t,n){return n&&!k(n)?0:"number"==typeof e?e:null==e?void 0:e[t]}const C=u.createContext({delay:0,initialDelay:0,timeoutMs:0,currentId:null,setCurrentId:()=>{},setState:()=>{},isInstantPhase:!1}),M=()=>u.useContext(C);function A(){return A=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},A.apply(this,arguments)}var L=new WeakMap,F=new WeakMap,D={},N=0,j=function(e,t,n){void 0===t&&(t=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body}(e)),void 0===n&&(n="data-aria-hidden");var r=Array.isArray(e)?e:[e];D[n]||(D[n]=new WeakMap);var o=D[n],u=[],i=new Set,c=function(e){e&&!i.has(e)&&(i.add(e),c(e.parentNode))};r.forEach(c);var l=function(e){!e||r.indexOf(e)>=0||Array.prototype.forEach.call(e.children,(function(e){if(i.has(e))l(e);else{var t=e.getAttribute("aria-hidden"),r=null!==t&&"false"!==t,c=(L.get(e)||0)+1,s=(o.get(e)||0)+1;L.set(e,c),o.set(e,s),u.push(e),1===c&&r&&F.set(e,!0),1===s&&e.setAttribute(n,"true"),r||e.setAttribute("aria-hidden","true")}}))};return l(t),i.clear(),N++,function(){u.forEach((function(e){var t=L.get(e)-1,r=o.get(e)-1;L.set(e,t),o.set(e,r),t||(F.has(e)||e.removeAttribute("aria-hidden"),F.delete(e)),r||e.removeAttribute(n)})),--N||(L=new WeakMap,L=new WeakMap,F=new WeakMap,D={})}},K=["input","select","textarea","a[href]","button","[tabindex]:not(slot)","audio[controls]","video[controls]",'[contenteditable]:not([contenteditable="false"])',"details>summary:first-of-type","details"].join(","),_="undefined"==typeof Element,B=_?function(){}:Element.prototype.matches||Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector,H=!_&&Element.prototype.getRootNode?function(e){return e.getRootNode()}:function(e){return e.ownerDocument},W=function e(t,n,r){for(var o=[],u=Array.from(t);u.length;){var i=u.shift();if("SLOT"===i.tagName){var c=i.assignedElements(),l=e(c.length?c:i.children,!0,r);r.flatten?o.push.apply(o,l):o.push({scopeParent:i,candidates:l})}else{B.call(i,K)&&r.filter(i)&&(n||!t.includes(i))&&o.push(i);var s=i.shadowRoot||"function"==typeof r.getShadowRoot&&r.getShadowRoot(i),a=!r.shadowRootFilter||r.shadowRootFilter(i);if(s&&a){var f=e(!0===s?i.children:s.children,!0,r);r.flatten?o.push.apply(o,f):o.push({scopeParent:i,candidates:f})}else u.unshift.apply(u,i.children)}}return o},q=function(e,t){return e.tabIndex<0&&(t||/^(AUDIO|VIDEO|DETAILS)$/.test(e.tagName)||e.isContentEditable)&&isNaN(parseInt(e.getAttribute("tabindex"),10))?0:e.tabIndex},U=function(e,t){return e.tabIndex===t.tabIndex?e.documentOrder-t.documentOrder:e.tabIndex-t.tabIndex},V=function(e){return"INPUT"===e.tagName},z=function(e){return function(e){return V(e)&&"radio"===e.type}(e)&&!function(e){if(!e.name)return!0;var t,n=e.form||H(e),r=function(e){return n.querySelectorAll('input[type="radio"][name="'+e+'"]')};if("undefined"!=typeof window&&void 0!==window.CSS&&"function"==typeof window.CSS.escape)t=r(window.CSS.escape(e.name));else try{t=r(e.name)}catch(e){return console.error("Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s",e.message),!1}var o=function(e,t){for(var n=0;n<e.length;n++)if(e[n].checked&&e[n].form===t)return e[n]}(t,e.form);return!o||o===e}(e)},X=function(e){var t=e.getBoundingClientRect(),n=t.width,r=t.height;return 0===n&&0===r},Y=function(e,t){var n=t.displayCheck,r=t.getShadowRoot;if("hidden"===getComputedStyle(e).visibility)return!0;var o=B.call(e,"details>summary:first-of-type")?e.parentElement:e;if(B.call(o,"details:not([open]) *"))return!0;if(n&&"full"!==n&&"legacy-full"!==n){if("non-zero-area"===n)return X(e)}else{if("function"==typeof r){for(var u=e;e;){var i=e.parentElement,c=H(e);if(i&&!i.shadowRoot&&!0===r(i))return X(e);e=e.assignedSlot?e.assignedSlot:i||c===e.ownerDocument?i:c.host}e=u}if(function(e){for(var t,n=H(e).host,r=!!(null!==(t=n)&&void 0!==t&&t.ownerDocument.contains(n)||e.ownerDocument.contains(e));!r&&n;){var o;r=!(null===(o=n=H(n).host)||void 0===o||!o.ownerDocument.contains(n))}return r}(e))return!e.getClientRects().length;if("legacy-full"!==n)return!0}return!1},G=function(e,t){return!(t.disabled||function(e){return V(e)&&"hidden"===e.type}(t)||Y(t,e)||function(e){return"DETAILS"===e.tagName&&Array.prototype.slice.apply(e.children).some((function(e){return"SUMMARY"===e.tagName}))}(t)||function(e){if(/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(e.tagName))for(var t=e.parentElement;t;){if("FIELDSET"===t.tagName&&t.disabled){for(var n=0;n<t.children.length;n++){var r=t.children.item(n);if("LEGEND"===r.tagName)return!!B.call(t,"fieldset[disabled] *")||!r.contains(e)}return!0}t=t.parentElement}return!1}(t))},Z=function(e,t){return!(z(t)||q(t)<0||!G(e,t))},$=function(e){var t=parseInt(e.getAttribute("tabindex"),10);return!!(isNaN(t)||t>=0)},J=function e(t){var n=[],r=[];return t.forEach((function(t,o){var u=!!t.scopeParent,i=u?t.scopeParent:t,c=q(i,u),l=u?e(t.candidates):i;0===c?u?n.push.apply(n,l):n.push(i):r.push({documentOrder:o,tabIndex:c,item:t,isScope:u,content:l})})),r.sort(U).reduce((function(e,t){return t.isScope?e.push.apply(e,t.content):e.push(t.content),e}),[]).concat(n)},Q=function(e,t){var n;return n=(t=t||{}).getShadowRoot?W([e],t.includeContainer,{filter:Z.bind(null,t),flatten:!1,getShadowRoot:t.getShadowRoot,shadowRootFilter:$}):function(e,t,n){var r=Array.prototype.slice.apply(e.querySelectorAll(K));return t&&B.call(e,K)&&r.unshift(e),r.filter(n)}(e,t.includeContainer,Z.bind(null,t)),J(n)};function ee(e){let t=e.activeElement;for(;null!=(null==(n=t)||null==(r=n.shadowRoot)?void 0:r.activeElement);){var n,r;t=t.shadowRoot.activeElement}return t}function te(e,t){if(!e||!t)return!1;const n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&(r=n,"undefined"!=typeof ShadowRoot&&(r instanceof h(r).ShadowRoot||r instanceof ShadowRoot))){let n=t;do{if(n&&e===n)return!0;n=n.parentNode||n.host}while(n)}var r;return!1}let ne=0;function re(e,t){void 0===t&&(t={});const{preventScroll:n=!1,cancelPrevious:r=!0,sync:o=!1}=t;r&&cancelAnimationFrame(ne);const u=()=>null==e?void 0:e.focus({preventScroll:n});o?u():ne=requestAnimationFrame(u)}function oe(e,t){let n=e.filter((e=>{var n;return e.parentId===t&&(null==(n=e.context)?void 0:n.open)}))||[],r=n;for(;r.length;)r=e.filter((e=>{var t;return null==(t=r)?void 0:t.some((t=>{var n;return e.parentId===t.id&&(null==(n=e.context)?void 0:n.open)}))}))||[],n=n.concat(r);return n}function ue(e){return"composedPath"in e?e.composedPath()[0]:e.target}function ie(e){return E(e)&&e.matches("input:not([type='hidden']):not([disabled]),[contenteditable]:not([contenteditable='false']),textarea:not([disabled])")}function ce(e){e.preventDefault(),e.stopPropagation()}const le=()=>({getShadowRoot:!0,displayCheck:"function"==typeof ResizeObserver&&ResizeObserver.toString().includes("[native code]")?"full":"none"});function se(e,t){const n=Q(e,le());"prev"===t&&n.reverse();const r=n.indexOf(ee(g(e)));return n.slice(r+1)[0]}function ae(){return se(document.body,"next")}function fe(){return se(document.body,"prev")}function de(e,t){const n=t||e.currentTarget,r=e.relatedTarget;return!r||!te(n,r)}function me(e){Q(e,le()).forEach((e=>{e.dataset.tabindex=e.getAttribute("tabindex")||"",e.setAttribute("tabindex","-1")}))}function pe(e){e.querySelectorAll("[data-tabindex]").forEach((e=>{const t=e.dataset.tabindex;delete e.dataset.tabindex,t?e.setAttribute("tabindex",t):e.removeAttribute("tabindex")}))}const ve=u["useInsertionEffect".toString()]||(e=>e());function ge(e){const t=u.useRef((()=>{}));return ve((()=>{t.current=e})),u.useCallback((function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return null==t.current?void 0:t.current(...n)}),[])}const be={border:0,clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"fixed",whiteSpace:"nowrap",width:"1px",top:0,left:0};let ye,he;function we(e){"Tab"===e.key&&(ye=e.target,clearTimeout(he))}const Ee=u.forwardRef((function(e,t){const n=ge(e.onFocus),[r,o]=u.useState();return i((()=>(I()&&o("button"),document.addEventListener("keydown",we),()=>{document.removeEventListener("keydown",we)})),[]),u.createElement("span",A({},e,{ref:t,tabIndex:0,role:r,"aria-hidden":!r||void 0,"data-floating-ui-focus-guard":"",style:be,onFocus:e=>{I()&&O()&&!function(e){const t=ye===e.relatedTarget;return ye=e.relatedTarget,clearTimeout(he),t}(e)?(e.persist(),he=window.setTimeout((()=>{n(e)}),50)):n(e)}}))})),Re=u.createContext(null),xe=function(e){let{id:t,enabled:n=!0}=void 0===e?{}:e;const[r,o]=u.useState(null),c=a(),l=Ie();return i((()=>{if(!n)return;const e=t?document.getElementById(t):null;if(!e){const e=document.createElement("div");""!==t&&(e.id=t||c),e.setAttribute("data-floating-ui-portal",""),o(e);const n=(null==l?void 0:l.portalNode)||document.body;return n.appendChild(e),()=>{n.removeChild(e)}}e.setAttribute("data-floating-ui-portal",""),o(e)}),[t,l,c,n]),r},Ie=()=>u.useContext(Re),Oe=u.forwardRef((function(e,t){return u.createElement("button",A({},e,{type:"button",ref:t,tabIndex:-1,style:be}))}));const ke="data-floating-ui-scroll-lock",Te=u.forwardRef((function(e,t){let{lockScroll:n=!1,...r}=e;return i((()=>{var e,t;if(!n)return;if(document.body.hasAttribute(ke))return;document.body.setAttribute(ke,"");const r=Math.round(document.documentElement.getBoundingClientRect().left)+document.documentElement.scrollLeft?"paddingLeft":"paddingRight",o=window.innerWidth-document.documentElement.clientWidth;if(!/iP(hone|ad|od)|iOS/.test(b()))return Object.assign(document.body.style,{overflow:"hidden",[r]:o+"px"}),()=>{document.body.removeAttribute(ke),Object.assign(document.body.style,{overflow:"",[r]:""})};const u=(null==(e=window.visualViewport)?void 0:e.offsetLeft)||0,i=(null==(t=window.visualViewport)?void 0:t.offsetTop)||0,c=window.pageXOffset,l=window.pageYOffset;return Object.assign(document.body.style,{position:"fixed",overflow:"hidden",top:-(l-Math.floor(i))+"px",left:-(c-Math.floor(u))+"px",right:"0",[r]:o+"px"}),()=>{Object.assign(document.body.style,{position:"",overflow:"",top:"",left:"",right:"",[r]:""}),document.body.removeAttribute(ke),window.scrollTo(c,l)}}),[n]),u.createElement("div",A({ref:t},r,{style:{position:"fixed",overflow:"auto",top:0,right:0,bottom:0,left:0,...r.style}}))}));function Se(e){return E(e.target)&&"BUTTON"===e.target.tagName}function Pe(e){return ie(e)}function Ce(e,t){if(null==t)return!1;if("composedPath"in e)return e.composedPath().includes(t);const n=e;return null!=n.target&&t.contains(n.target)}const Me={pointerdown:"onPointerDown",mousedown:"onMouseDown",click:"onClick"},Ae={pointerdown:"onPointerDownCapture",mousedown:"onMouseDownCapture",click:"onClickCapture"};let Le=!1;const Fe="ArrowUp",De="ArrowDown",Ne="ArrowLeft",je="ArrowRight";function Ke(e,t,n){return Math.floor(e/t)!==n}function _e(e,t){return t<0||t>=e.current.length}function Be(e,t){let{startingIndex:n=-1,decrement:r=!1,disabledIndices:o,amount:u=1}=void 0===t?{}:t;const i=e.current;let c=n;do{var l,s;c+=r?-u:u}while(c>=0&&c<=i.length-1&&(o?o.includes(c):null==i[c]||(null==(l=i[c])?void 0:l.hasAttribute("disabled"))||"true"===(null==(s=i[c])?void 0:s.getAttribute("aria-disabled"))));return c}function He(e,t,n){switch(e){case"vertical":return t;case"horizontal":return n;default:return t||n}}function We(e,t){return He(t,e===Fe||e===De,e===Ne||e===je)}function qe(e,t,n){return He(t,e===De,n?e===Ne:e===je)||"Enter"===e||" "==e||""===e}function Ue(e,t){return Be(e,{disabledIndices:t})}function Ve(e,t){return Be(e,{decrement:!0,startingIndex:e.current.length,disabledIndices:t})}const ze=e=>e.replace(/[A-Z]+(?![a-z])|[A-Z]/g,((e,t)=>(t?"-":"")+e.toLowerCase()));function Xe(e,t){let{open:n,elements:{floating:r}}=e,{duration:o=250}=void 0===t?{}:t;const c=("number"==typeof o?o:o.close)||0,[l,s]=u.useState(!1),[a,f]=u.useState("unmounted"),d=function(e,t){const[n,r]=u.useState(e);return e&&!n&&r(!0),u.useEffect((()=>{if(!e){const e=setTimeout((()=>r(!1)),t);return()=>clearTimeout(e)}}),[e,t]),n}(n,c);return i((()=>{l&&!d&&f("unmounted")}),[l,d]),i((()=>{if(r){if(n){f("initial");const e=requestAnimationFrame((()=>{f("open")}));return()=>{cancelAnimationFrame(e)}}s(!0),f("close")}}),[n,r]),{isMounted:d,status:a}}function Ye(e,t){return{...e,rects:{...e.rects,floating:{...e.rects.floating,height:t}}}}function Ge(e,t,n){const r=new Map;return{..."floating"===n&&{tabIndex:-1},...e,...t.map((e=>e?e[n]:null)).concat(e).reduce(((e,t)=>t?(Object.entries(t).forEach((t=>{let[n,o]=t;var u;0===n.indexOf("on")?(r.has(n)||r.set(n,[]),"function"==typeof o&&(null==(u=r.get(n))||u.push(o),e[n]=function(){for(var e,t=arguments.length,o=new Array(t),u=0;u<t;u++)o[u]=arguments[u];null==(e=r.get(n))||e.forEach((e=>e(...o)))})):e[n]=o})),e):e),{})}}Object.defineProperty(e,"arrow",{enumerable:!0,get:function(){return r.arrow}}),Object.defineProperty(e,"autoPlacement",{enumerable:!0,get:function(){return r.autoPlacement}}),Object.defineProperty(e,"autoUpdate",{enumerable:!0,get:function(){return r.autoUpdate}}),Object.defineProperty(e,"computePosition",{enumerable:!0,get:function(){return r.computePosition}}),Object.defineProperty(e,"detectOverflow",{enumerable:!0,get:function(){return r.detectOverflow}}),Object.defineProperty(e,"flip",{enumerable:!0,get:function(){return r.flip}}),Object.defineProperty(e,"getOverflowAncestors",{enumerable:!0,get:function(){return r.getOverflowAncestors}}),Object.defineProperty(e,"hide",{enumerable:!0,get:function(){return r.hide}}),Object.defineProperty(e,"inline",{enumerable:!0,get:function(){return r.inline}}),Object.defineProperty(e,"limitShift",{enumerable:!0,get:function(){return r.limitShift}}),Object.defineProperty(e,"offset",{enumerable:!0,get:function(){return r.offset}}),Object.defineProperty(e,"platform",{enumerable:!0,get:function(){return r.platform}}),Object.defineProperty(e,"shift",{enumerable:!0,get:function(){return r.shift}}),Object.defineProperty(e,"size",{enumerable:!0,get:function(){return r.size}}),e.FloatingDelayGroup=e=>{let{children:t,delay:n,timeoutMs:r=0}=e;const[o,c]=u.useReducer(((e,t)=>({...e,...t})),{delay:n,timeoutMs:r,initialDelay:n,currentId:null,isInstantPhase:!1}),l=u.useRef(null),s=u.useCallback((e=>{c({currentId:e})}),[]);return i((()=>{o.currentId?null===l.current?l.current=o.currentId:c({isInstantPhase:!0}):(c({isInstantPhase:!1}),l.current=null)}),[o.currentId]),u.createElement(C.Provider,{value:u.useMemo((()=>({...o,setState:c,setCurrentId:s})),[o,c,s])},t)},e.FloatingFocusManager=function(e){let{context:t,children:n,order:r=["content"],guards:o=!0,initialFocus:c=0,returnFocus:l=!0,modal:s=!0,visuallyHiddenDismiss:a=!1,closeOnFocusOut:f=!0}=e;const{refs:d,nodeId:m,onOpenChange:p,events:b,dataRef:y,elements:{domReference:h,floating:w}}=t,R=T(r),x=v(),I=Ie(),[O,k]=u.useState(null),S="number"==typeof c&&c<0,P=u.useRef(null),C=u.useRef(null),M=u.useRef(!1),A=u.useRef(null),L=u.useRef(!1),F=null!=I,D=h&&"combobox"===h.getAttribute("role")&&ie(h),N=u.useCallback((function(e){return void 0===e&&(e=w),e?Q(e,le()):[]}),[w]),K=u.useCallback((e=>{const t=N(e);return R.current.map((e=>h&&"reference"===e?h:w&&"floating"===e?w:t)).filter(Boolean).flat()}),[h,w,R,N]);u.useEffect((()=>{if(!s)return;function e(e){if("Tab"===e.key){0!==N().length||D||ce(e);const t=K(),n=ue(e);"reference"===R.current[0]&&n===h&&(ce(e),e.shiftKey?re(t[t.length-1]):re(t[1])),"floating"===R.current[1]&&n===w&&e.shiftKey&&(ce(e),re(t[0]))}}const t=g(w);return t.addEventListener("keydown",e),()=>{t.removeEventListener("keydown",e)}}),[h,w,s,R,d,D,N,K]),u.useEffect((()=>{if(f)return w&&E(h)?(h.addEventListener("focusout",t),h.addEventListener("pointerdown",e),!s&&w.addEventListener("focusout",t),()=>{h.removeEventListener("focusout",t),h.removeEventListener("pointerdown",e),!s&&w.removeEventListener("focusout",t)}):void 0;function e(){L.current=!0,setTimeout((()=>{L.current=!1}))}function t(e){const t=e.relatedTarget,n=!(te(h,t)||te(w,t)||te(t,w)||te(null==I?void 0:I.portalNode,t)||null!=t&&t.hasAttribute("data-floating-ui-focus-guard")||x&&(oe(x.nodesRef.current,m).find((e=>{var n,r;return te(null==(n=e.context)?void 0:n.elements.floating,t)||te(null==(r=e.context)?void 0:r.elements.domReference,t)}))||function(e,t){var n;let r=[],o=null==(n=e.find((e=>e.id===t)))?void 0:n.parentId;for(;o;){const t=e.find((e=>e.id===o));o=null==t?void 0:t.parentId,t&&(r=r.concat(t))}return r}(x.nodesRef.current,m).find((e=>{var n,r;return(null==(n=e.context)?void 0:n.elements.floating)===t||(null==(r=e.context)?void 0:r.elements.domReference)===t}))));t&&n&&!L.current&&t!==A.current&&(M.current=!0,setTimeout((()=>p(!1))))}}),[h,w,s,m,x,I,p,f]),u.useEffect((()=>{var e;const t=Array.from((null==I||null==(e=I.portalNode)?void 0:e.querySelectorAll("[data-floating-ui-portal]"))||[]);if(w&&s){const e=[w,...t,...[P.current,C.current].filter(Boolean)],n=j(R.current.includes("reference")||D?e.concat(h||[]):e);return()=>{n()}}}),[h,w,s,R,I,D]),u.useEffect((()=>{if(s&&!o&&w){const e=[],t=le(),n=Q(g(w).body,t),r=K(),o=n.filter((e=>!r.includes(e)));return o.forEach(((t,n)=>{e[n]=t.getAttribute("tabindex"),t.setAttribute("tabindex","-1")})),()=>{o.forEach(((t,n)=>{const r=e[n];null==r?t.removeAttribute("tabindex"):t.setAttribute("tabindex",r)}))}}}),[w,s,o,K]),i((()=>{if(!w)return;const e=g(w);let t=l,n=!1;const r=ee(e),o=y.current;A.current=r;const u=K(w),i=("number"==typeof c?u[c]:c.current)||w;function s(e){if("escapeKey"===e.type&&d.domReference.current&&(A.current=d.domReference.current),["referencePress","escapeKey"].includes(e.type))return;const r=e.data.returnFocus;"object"==typeof r?(t=!0,n=r.preventScroll):t=r}return!S&&re(i,{preventScroll:i===w}),b.on("dismiss",s),()=>{var r;(b.off("dismiss",s),te(w,ee(e))&&d.domReference.current&&(A.current=d.domReference.current),t&&E(A.current)&&!M.current)&&(!d.domReference.current||L.current?re(A.current,{cancelPrevious:!1,preventScroll:n}):(o.__syncReturnFocus=!0,null==(r=A.current)||r.focus({preventScroll:n}),setTimeout((()=>{delete o.__syncReturnFocus}))))}}),[w,K,c,l,y,d,b,S]),i((()=>{if(I)return I.setFocusManagerState({...t,modal:s,closeOnFocusOut:f}),()=>{I.setFocusManagerState(null)}}),[I,s,f,t]),i((()=>{if(!S&&w&&(e(),"function"==typeof MutationObserver)){const t=new MutationObserver(e);return t.observe(w,{childList:!0,subtree:!0}),()=>{t.disconnect()}}function e(){k(N().length)}}),[w,N,S,d]);const _=o&&(F||s)&&!D;function B(e){return a&&s?u.createElement(Oe,{ref:"start"===e?P:C,onClick:()=>p(!1)},"string"==typeof a?a:"Dismiss"):null}return u.createElement(u.Fragment,null,_&&u.createElement(Ee,{"data-type":"inside",ref:null==I?void 0:I.beforeInsideRef,onFocus:e=>{if(s){const e=K();re("reference"===r[0]?e[0]:e[e.length-1])}else if(null!=I&&I.preserveTabOrder&&I.portalNode)if(M.current=!1,de(e,I.portalNode)){const e=ae()||h;null==e||e.focus()}else{var t;null==(t=I.beforeOutsideRef.current)||t.focus()}}}),D?null:B("start"),u.cloneElement(n,0===O||r.includes("floating")?{tabIndex:0}:{}),B("end"),_&&u.createElement(Ee,{"data-type":"inside",ref:null==I?void 0:I.afterInsideRef,onFocus:e=>{if(s)re(K()[0]);else if(null!=I&&I.preserveTabOrder&&I.portalNode)if(M.current=!0,de(e,I.portalNode)){const e=fe()||h;null==e||e.focus()}else{var t;null==(t=I.afterOutsideRef.current)||t.focus()}}}))},e.FloatingNode=e=>{let{children:t,id:n}=e;const r=p();return u.createElement(d.Provider,{value:u.useMemo((()=>({id:n,parentId:r})),[n,r])},t)},e.FloatingOverlay=Te,e.FloatingPortal=e=>{let{children:t,id:r,root:o=null,preserveTabOrder:i=!0}=e;const c=xe({id:r,enabled:!o}),[l,s]=u.useState(null),a=u.useRef(null),f=u.useRef(null),d=u.useRef(null),m=u.useRef(null),p=!!l&&!l.modal&&!(!o&&!c)&&i;return u.useEffect((()=>{if(c&&i&&(null==l||!l.modal))return c.addEventListener("focusin",e,!0),c.addEventListener("focusout",e,!0),()=>{c.removeEventListener("focusin",e,!0),c.removeEventListener("focusout",e,!0)};function e(e){if(c&&de(e)){("focusin"===e.type?pe:me)(c)}}}),[c,i,null==l?void 0:l.modal]),u.createElement(Re.Provider,{value:u.useMemo((()=>({preserveTabOrder:i,beforeOutsideRef:a,afterOutsideRef:f,beforeInsideRef:d,afterInsideRef:m,portalNode:c,setFocusManagerState:s})),[i,c])},p&&c&&u.createElement(Ee,{"data-type":"outside",ref:a,onFocus:e=>{if(de(e,c)){var t;null==(t=d.current)||t.focus()}else{const e=fe()||(null==l?void 0:l.refs.domReference.current);null==e||e.focus()}}}),p&&c&&u.createElement("span",{"aria-owns":c.id,style:be}),o?n.createPortal(t,o):c?n.createPortal(t,c):null,p&&c&&u.createElement(Ee,{"data-type":"outside",ref:f,onFocus:e=>{if(de(e,c)){var t;null==(t=m.current)||t.focus()}else{const e=ae()||(null==l?void 0:l.refs.domReference.current);null==e||e.focus(),(null==l?void 0:l.closeOnFocusOut)&&(null==l||l.onOpenChange(!1))}}}))},e.FloatingTree=e=>{let{children:t}=e;const n=u.useRef([]),r=u.useCallback((e=>{n.current=[...n.current,e]}),[]),o=u.useCallback((e=>{n.current=n.current.filter((t=>t!==e))}),[]),i=u.useState((()=>f()))[0];return u.createElement(m.Provider,{value:u.useMemo((()=>({nodesRef:n,addNode:r,removeNode:o,events:i})),[n,r,o,i])},t)},e.inner=e=>({name:"inner",options:e,async fn(t){const{listRef:o,overflowRef:u,onFallbackChange:i,offset:c=0,index:l=0,minItemsVisible:s=4,referenceOverflowThreshold:a=0,scrollRef:f,...d}=e,{rects:m,elements:{floating:p}}=t,v=o.current[l];if(!v)return{};const g={...t,...await r.offset(-v.offsetTop-m.reference.height/2-v.offsetHeight/2-c).fn(t)},b=(null==f?void 0:f.current)||p,y=await r.detectOverflow(Ye(g,b.scrollHeight),d),h=await r.detectOverflow(g,{...d,elementContext:"reference"}),w=Math.max(0,y.top),E=g.y+w,R=Math.max(0,b.scrollHeight-w-Math.max(0,y.bottom));return b.style.maxHeight=R+"px",b.scrollTop=w,i&&(b.offsetHeight<v.offsetHeight*Math.min(s,o.current.length-1)-1||h.top>=-a||h.bottom>=-a?n.flushSync((()=>i(!0))):n.flushSync((()=>i(!1)))),u&&(u.current=await r.detectOverflow(Ye({...g,y:E},b.offsetHeight),d)),{y:E}}}),e.safePolygon=function(e){let t,{restMs:n=0,buffer:r=.5,blockPointerEvents:o=!1}=void 0===e?{}:e,u=!1,i=!1;const c=e=>{let{x:o,y:c,placement:l,elements:s,onClose:a,nodeId:f,tree:d}=e;return function(e){function m(){clearTimeout(t),a()}if(clearTimeout(t),!s.domReference||!s.floating||null==l||null==o||null==c)return;const{clientX:p,clientY:v}=e,g=[p,v],b=ue(e),y="mouseleave"===e.type,h=te(s.floating,b),E=te(s.domReference,b),R=s.domReference.getBoundingClientRect(),x=s.floating.getBoundingClientRect(),I=l.split("-")[0],O=o>x.right-x.width/2,k=c>x.bottom-x.height/2,T=function(e,t){return e[0]>=t.x&&e[0]<=t.x+t.width&&e[1]>=t.y&&e[1]<=t.y+t.height}(g,R);if(h&&(i=!0,!y))return;if(E&&(i=!1),E&&!y)return void(i=!0);if(y&&w(e.relatedTarget)&&te(s.floating,e.relatedTarget))return;if(d&&oe(d.nodesRef.current,f).some((e=>{let{context:t}=e;return null==t?void 0:t.open})))return;if("top"===I&&c>=R.bottom-1||"bottom"===I&&c<=R.top+1||"left"===I&&o>=R.right-1||"right"===I&&o<=R.left+1)return m();let S=[];switch(I){case"top":S=[[x.left,R.top+1],[x.left,x.bottom-1],[x.right,x.bottom-1],[x.right,R.top+1]],u=p>=x.left&&p<=x.right&&v>=x.top&&v<=R.top+1;break;case"bottom":S=[[x.left,x.top+1],[x.left,R.bottom-1],[x.right,R.bottom-1],[x.right,x.top+1]],u=p>=x.left&&p<=x.right&&v>=R.bottom-1&&v<=x.bottom;break;case"left":S=[[x.right-1,x.bottom],[x.right-1,x.top],[R.left+1,x.top],[R.left+1,x.bottom]],u=p>=x.left&&p<=R.left+1&&v>=x.top&&v<=x.bottom;break;case"right":S=[[R.right-1,x.bottom],[R.right-1,x.top],[x.left+1,x.top],[x.left+1,x.bottom]],u=p>=R.right-1&&p<=x.right&&v>=x.top&&v<=x.bottom}const P=u?S:function(e){let[t,n]=e;const o=x.width>R.width,u=x.height>R.height;switch(I){case"top":return[[o?t+r/2:O?t+4*r:t-4*r,n+r+1],[o?t-r/2:O?t+4*r:t-4*r,n+r+1],...[[x.left,O||o?x.bottom-r:x.top],[x.right,O?o?x.bottom-r:x.top:x.bottom-r]]];case"bottom":return[[o?t+r/2:O?t+4*r:t-4*r,n-r],[o?t-r/2:O?t+4*r:t-4*r,n-r],...[[x.left,O||o?x.top+r:x.bottom],[x.right,O?o?x.top+r:x.bottom:x.top+r]]];case"left":{const e=[t+r+1,u?n+r/2:k?n+4*r:n-4*r],o=[t+r+1,u?n-r/2:k?n+4*r:n-4*r];return[...[[k||u?x.right-r:x.left,x.top],[k?u?x.right-r:x.left:x.right-r,x.bottom]],e,o]}case"right":return[[t-r,u?n+r/2:k?n+4*r:n-4*r],[t-r,u?n-r/2:k?n+4*r:n-4*r],...[[k||u?x.left+r:x.right,x.top],[k?u?x.left+r:x.right:x.left+r,x.bottom]]]}}([o,c]);return u?void 0:i&&!T?m():void(!function(e,t){const[n,r]=e;let o=!1;const u=t.length;for(let e=0,i=u-1;e<u;i=e++){const[u,c]=t[e]||[0,0],[l,s]=t[i]||[0,0];c>=r!=s>=r&&n<=(l-u)*(r-c)/(s-c)+u&&(o=!o)}return o}([p,v],P)?m():n&&!i&&(t=setTimeout(m,n)))}};return c.__options={blockPointerEvents:o},c},e.useClick=function(e,t){let{open:n,onOpenChange:r,dataRef:o,elements:{domReference:i}}=e,{enabled:c=!0,event:l="click",toggle:s=!0,ignoreMouse:a=!1,keyboardHandlers:f=!0}=void 0===t?{}:t;const d=u.useRef();return u.useMemo((()=>c?{reference:{onPointerDown(e){d.current=e.pointerType},onMouseDown(e){0===e.button&&(k(d.current,!0)&&a||"click"!==l&&(n?!s||o.current.openEvent&&"mousedown"!==o.current.openEvent.type||r(!1):(e.preventDefault(),r(!0)),o.current.openEvent=e.nativeEvent))},onClick(e){o.current.__syncReturnFocus||("mousedown"===l&&d.current?d.current=void 0:k(d.current,!0)&&a||(n?!s||o.current.openEvent&&"click"!==o.current.openEvent.type||r(!1):r(!0),o.current.openEvent=e.nativeEvent))},onKeyDown(e){d.current=void 0,f&&(Se(e)||(" "!==e.key||Pe(i)||e.preventDefault(),"Enter"===e.key&&(n?s&&r(!1):r(!0))))},onKeyUp(e){f&&(Se(e)||Pe(i)||" "===e.key&&(n?s&&r(!1):r(!0)))}}}:{}),[c,o,l,a,f,i,s,n,r])},e.useDelayGroup=(e,t)=>{let{open:n,onOpenChange:r}=e,{id:o}=t;const{currentId:i,setCurrentId:c,initialDelay:l,setState:s,timeoutMs:a}=M();u.useEffect((()=>{i&&(s({delay:{open:1,close:P(l,"close")}}),i!==o&&r(!1))}),[o,r,s,i,l]),u.useEffect((()=>{function e(){r(!1),s({delay:l,currentId:null})}if(!n&&i===o){if(a){const t=window.setTimeout(e,a);return()=>{clearTimeout(t)}}e()}}),[n,s,i,o,r,l,a]),u.useEffect((()=>{n&&c(o)}),[n,c,o])},e.useDelayGroupContext=M,e.useDismiss=function(e,t){let{open:n,onOpenChange:o,events:i,nodeId:c,elements:{reference:l,domReference:s,floating:a},dataRef:f}=e,{enabled:d=!0,escapeKey:m=!0,outsidePress:b=!0,outsidePressEvent:y="pointerdown",referencePress:h=!1,referencePressEvent:I="pointerdown",ancestorScroll:O=!1,bubbles:k=!0}=void 0===t?{}:t;const T=v(),S=null!=p(),P=ge("function"==typeof b?b:()=>!1),C="function"==typeof b?P:b,M=u.useRef(!1),{escapeKeyBubbles:A,outsidePressBubbles:L}=function(e){var t,n;return void 0===e&&(e=!0),{escapeKeyBubbles:"boolean"==typeof e?e:null==(t=e.escapeKey)||t,outsidePressBubbles:"boolean"==typeof e?e:null==(n=e.outsidePress)||n}}(k);return u.useEffect((()=>{if(!n||!d)return;function e(e){if("Escape"===e.key){const e=T?oe(T.nodesRef.current,c):[];if(e.length>0){let t=!0;if(e.forEach((e=>{var n;null==(n=e.context)||!n.open||e.context.dataRef.current.__escapeKeyBubbles||(t=!1)})),!t)return}i.emit("dismiss",{type:"escapeKey",data:{returnFocus:{preventScroll:!1}}}),o(!1)}}function t(e){const t=M.current;if(M.current=!1,t)return;if("function"==typeof C&&!C(e))return;const n=ue(e);if(E(n)&&a){const t=a.ownerDocument.defaultView||window,r=n.scrollWidth>n.clientWidth,o=n.scrollHeight>n.clientHeight;let u=o&&e.offsetX>n.clientWidth;if(o){"rtl"===t.getComputedStyle(n).direction&&(u=e.offsetX<=n.offsetWidth-n.clientWidth)}if(u||r&&e.offsetY>n.clientHeight)return}const r=T&&oe(T.nodesRef.current,c).some((t=>{var n;return Ce(e,null==(n=t.context)?void 0:n.elements.floating)}));if(Ce(e,a)||Ce(e,s)||r)return;const u=T?oe(T.nodesRef.current,c):[];if(u.length>0){let e=!0;if(u.forEach((t=>{var n;null==(n=t.context)||!n.open||t.context.dataRef.current.__outsidePressBubbles||(e=!1)})),!e)return}i.emit("dismiss",{type:"outsidePress",data:{returnFocus:S?{preventScroll:!0}:R(e)||x(e)}}),o(!1)}function u(){o(!1)}f.current.__escapeKeyBubbles=A,f.current.__outsidePressBubbles=L;const p=g(a);m&&p.addEventListener("keydown",e),C&&p.addEventListener(y,t);let v=[];return O&&(w(s)&&(v=r.getOverflowAncestors(s)),w(a)&&(v=v.concat(r.getOverflowAncestors(a))),!w(l)&&l&&l.contextElement&&(v=v.concat(r.getOverflowAncestors(l.contextElement)))),v=v.filter((e=>{var t;return e!==(null==(t=p.defaultView)?void 0:t.visualViewport)})),v.forEach((e=>{e.addEventListener("scroll",u,{passive:!0})})),()=>{m&&p.removeEventListener("keydown",e),C&&p.removeEventListener(y,t),v.forEach((e=>{e.removeEventListener("scroll",u)}))}}),[f,a,s,l,m,C,y,i,T,c,n,o,O,d,A,L,S]),u.useEffect((()=>{M.current=!1}),[C,y]),u.useMemo((()=>d?{reference:{[Me[I]]:()=>{h&&(i.emit("dismiss",{type:"referencePress",data:{returnFocus:!1}}),o(!1))}},floating:{[Ae[y]]:()=>{M.current=!0}}}:{}),[d,i,h,y,I,o])},e.useFloating=function(e){void 0===e&&(e={});const{open:t=!1,onOpenChange:n,nodeId:o}=e,c=r.useFloating(e),l=v(),s=u.useRef(null),a=u.useRef({}),d=u.useState((()=>f()))[0],[m,p]=u.useState(null),g=u.useCallback((e=>{const t=w(e)?{getBoundingClientRect:()=>e.getBoundingClientRect(),contextElement:e}:e;c.refs.setReference(t)}),[c.refs]),b=u.useCallback((e=>{(w(e)||null===e)&&(s.current=e,p(e)),(w(c.refs.reference.current)||null===c.refs.reference.current||null!==e&&!w(e))&&c.refs.setReference(e)}),[c.refs]),y=u.useMemo((()=>({...c.refs,setReference:b,setPositionReference:g,domReference:s})),[c.refs,b,g]),h=u.useMemo((()=>({...c.elements,domReference:m})),[c.elements,m]),E=ge(n),R=u.useMemo((()=>({...c,refs:y,elements:h,dataRef:a,nodeId:o,events:d,open:t,onOpenChange:E})),[c,o,d,t,E,y,h]);return i((()=>{const e=null==l?void 0:l.nodesRef.current.find((e=>e.id===o));e&&(e.context=R)})),u.useMemo((()=>({...c,context:R,refs:y,reference:b,positionReference:g})),[c,y,R,b,g])},e.useFloatingNodeId=e=>{const t=a(),n=v(),r=p(),o=e||r;return i((()=>{const e={id:t,parentId:o};return null==n||n.addNode(e),()=>{null==n||n.removeNode(e)}}),[n,t,o]),t},e.useFloatingParentNodeId=p,e.useFloatingPortalNode=xe,e.useFloatingTree=v,e.useFocus=function(e,t){let{open:n,onOpenChange:r,dataRef:o,events:i,refs:c,elements:{floating:l,domReference:s}}=e,{enabled:a=!0,keyboardOnly:f=!0}=void 0===t?{}:t;const d=u.useRef(""),m=u.useRef(!1),p=u.useRef();return u.useEffect((()=>{if(!a)return;const e=g(l).defaultView||window;function t(){!n&&E(s)&&s===ee(g(s))&&(m.current=!0)}return e.addEventListener("blur",t),()=>{e.removeEventListener("blur",t)}}),[l,s,n,a]),u.useEffect((()=>{if(a)return i.on("dismiss",e),()=>{i.off("dismiss",e)};function e(e){"referencePress"!==e.type&&"escapeKey"!==e.type||(m.current=!0)}}),[i,a]),u.useEffect((()=>()=>{clearTimeout(p.current)}),[]),u.useMemo((()=>a?{reference:{onPointerDown(e){let{pointerType:t}=e;d.current=t,m.current=!(!t||!f)},onMouseLeave(){m.current=!1},onFocus(e){var t;m.current||"focus"===e.type&&"mousedown"===(null==(t=o.current.openEvent)?void 0:t.type)&&o.current.openEvent&&Ce(o.current.openEvent,s)||(o.current.openEvent=e.nativeEvent,r(!0))},onBlur(e){m.current=!1;const t=e.relatedTarget,n=w(t)&&t.hasAttribute("data-floating-ui-focus-guard")&&"outside"===t.getAttribute("data-type");p.current=setTimeout((()=>{te(c.floating.current,t)||te(s,t)||n||r(!1)}))}}}:{}),[a,f,s,c,o,r])},e.useHover=function(e,t){let{enabled:n=!0,delay:r=0,handleClose:o=null,mouseOnly:c=!1,restMs:l=0,move:s=!0}=void 0===t?{}:t;const{open:a,onOpenChange:f,dataRef:d,events:m,elements:{domReference:b,floating:y},refs:h}=e,E=v(),R=p(),x=T(o),I=T(r),O=u.useRef(),C=u.useRef(),M=u.useRef(),A=u.useRef(),L=u.useRef(!0),F=u.useRef(!1),D=u.useRef((()=>{})),N=u.useCallback((()=>{var e;const t=null==(e=d.current.openEvent)?void 0:e.type;return(null==t?void 0:t.includes("mouse"))&&"mousedown"!==t}),[d]);u.useEffect((()=>{if(n)return m.on("dismiss",e),()=>{m.off("dismiss",e)};function e(){clearTimeout(C.current),clearTimeout(A.current),L.current=!0}}),[n,m]),u.useEffect((()=>{if(!n||!x.current||!a)return;function e(){N()&&f(!1)}const t=g(y).documentElement;return t.addEventListener("mouseleave",e),()=>{t.removeEventListener("mouseleave",e)}}),[y,a,f,n,x,d,N]);const j=u.useCallback((function(e){void 0===e&&(e=!0);const t=P(I.current,"close",O.current);t&&!M.current?(clearTimeout(C.current),C.current=setTimeout((()=>f(!1)),t)):e&&(clearTimeout(C.current),f(!1))}),[I,f]),K=u.useCallback((()=>{D.current(),M.current=void 0}),[]),_=u.useCallback((()=>{if(F.current){const e=g(h.floating.current).body;e.style.pointerEvents="",e.removeAttribute(S),F.current=!1}}),[h]);return u.useEffect((()=>{if(n&&w(b)){const e=b;return a&&e.addEventListener("mouseleave",u),null==y||y.addEventListener("mouseleave",u),s&&e.addEventListener("mousemove",r,{once:!0}),e.addEventListener("mouseenter",r),e.addEventListener("mouseleave",o),()=>{a&&e.removeEventListener("mouseleave",u),null==y||y.removeEventListener("mouseleave",u),s&&e.removeEventListener("mousemove",r),e.removeEventListener("mouseenter",r),e.removeEventListener("mouseleave",o)}}function t(){return!!d.current.openEvent&&["click","mousedown"].includes(d.current.openEvent.type)}function r(e){if(clearTimeout(C.current),L.current=!1,c&&!k(O.current)||l>0&&0===P(I.current,"open"))return;d.current.openEvent=e;const t=P(I.current,"open",O.current);t?C.current=setTimeout((()=>{f(!0)}),t):f(!0)}function o(n){if(t())return;D.current();const r=g(y);if(clearTimeout(A.current),x.current){a||clearTimeout(C.current),M.current=x.current({...e,tree:E,x:n.clientX,y:n.clientY,onClose(){_(),K(),j()}});const t=M.current;return r.addEventListener("mousemove",t),void(D.current=()=>{r.removeEventListener("mousemove",t)})}j()}function u(n){t()||null==x.current||x.current({...e,tree:E,x:n.clientX,y:n.clientY,onClose(){_(),K(),j()}})(n)}}),[b,y,n,e,c,l,s,j,K,_,f,a,E,I,x,d]),i((()=>{var e;if(n&&a&&null!=(e=x.current)&&e.__options.blockPointerEvents&&N()){const e=g(y).body;if(e.setAttribute(S,""),e.style.pointerEvents="none",F.current=!0,w(b)&&y){var t,r;const e=b,n=null==E||null==(t=E.nodesRef.current.find((e=>e.id===R)))||null==(r=t.context)?void 0:r.elements.floating;return n&&(n.style.pointerEvents=""),e.style.pointerEvents="auto",y.style.pointerEvents="auto",()=>{e.style.pointerEvents="",y.style.pointerEvents=""}}}}),[n,a,R,y,b,E,x,d,N]),i((()=>{a||(O.current=void 0,K(),_())}),[a,K,_]),u.useEffect((()=>()=>{K(),clearTimeout(C.current),clearTimeout(A.current),_()}),[n,K,_]),u.useMemo((()=>{if(!n)return{};function e(e){O.current=e.pointerType}return{reference:{onPointerDown:e,onPointerEnter:e,onMouseMove(){a||0===l||(clearTimeout(A.current),A.current=setTimeout((()=>{L.current||f(!0)}),l))}},floating:{onMouseEnter(){clearTimeout(C.current)},onMouseLeave(){m.emit("dismiss",{type:"mouseLeave",data:{returnFocus:!1}}),j(!1)}}}}),[m,n,l,a,f,j])},e.useId=a,e.useInnerOffset=(e,t)=>{let{open:r,elements:o}=e,{enabled:i=!0,overflowRef:c,scrollRef:l,onChange:s}=t;const a=ge(s),f=u.useRef(!1),d=u.useRef(null),m=u.useRef(null);return u.useEffect((()=>{if(!i)return;function e(e){if(e.ctrlKey||!t||null==c.current)return;const r=e.deltaY,o=c.current.top>=-.5,u=c.current.bottom>=-.5,i=t.scrollHeight-t.clientHeight,l=r<0?-1:1,s=r<0?"max":"min";t.scrollHeight<=t.clientHeight||(!o&&r>0||!u&&r<0?(e.preventDefault(),n.flushSync((()=>{a((e=>e+Math[s](r,i*l)))}))):/firefox/i.test(y())&&(t.scrollTop+=r))}const t=(null==l?void 0:l.current)||o.floating;return r&&t?(t.addEventListener("wheel",e),requestAnimationFrame((()=>{d.current=t.scrollTop,null!=c.current&&(m.current={...c.current})})),()=>{d.current=null,m.current=null,t.removeEventListener("wheel",e)}):void 0}),[i,r,o.floating,c,l,a]),u.useMemo((()=>i?{floating:{onKeyDown(){f.current=!0},onWheel(){f.current=!1},onPointerMove(){f.current=!1},onScroll(){const e=(null==l?void 0:l.current)||o.floating;if(c.current&&e&&f.current){if(null!==d.current){const t=e.scrollTop-d.current;(c.current.bottom<-.5&&t<-1||c.current.top<-.5&&t>1)&&n.flushSync((()=>a((e=>e+t))))}requestAnimationFrame((()=>{d.current=e.scrollTop}))}}}}:{}),[i,c,o.floating,l,a])},e.useInteractions=function(e){void 0===e&&(e=[]);const t=e,n=u.useCallback((t=>Ge(t,e,"reference")),t),r=u.useCallback((t=>Ge(t,e,"floating")),t),o=u.useCallback((t=>Ge(t,e,"item")),e.map((e=>null==e?void 0:e.item)));return u.useMemo((()=>({getReferenceProps:n,getFloatingProps:r,getItemProps:o})),[n,r,o])},e.useListNavigation=function(e,t){let{open:r,onOpenChange:o,refs:c,elements:{domReference:l}}=e,{listRef:s,activeIndex:a,onNavigate:f=(()=>{}),enabled:d=!0,selectedIndex:m=null,allowEscape:b=!1,loop:y=!1,nested:h=!1,rtl:w=!1,virtual:k=!1,focusItemOnOpen:S="auto",focusItemOnHover:P=!0,openOnArrowKeyDown:C=!0,disabledIndices:M,orientation:A="vertical",cols:L=1,scrollItemIntoView:F=!0}=void 0===t?{listRef:{current:[]},activeIndex:null,onNavigate:()=>{}}:t;const D=p(),N=v(),j=ge(f),K=u.useRef(S),_=u.useRef(null!=m?m:-1),B=u.useRef(null),H=u.useRef(!0),W=u.useRef(j),q=u.useRef(r),U=u.useRef(!1),V=u.useRef(!1),z=T(M),X=T(r),Y=T(F),[G,Z]=u.useState(),$=u.useCallback((function(e,t,n){void 0===n&&(n=!1);const r=e.current[t.current];k?Z(null==r?void 0:r.id):re(r,{preventScroll:!0,sync:!(!O()||!I())&&(Le||U.current)}),requestAnimationFrame((()=>{const e=Y.current;e&&r&&(n||!H.current)&&(null==r.scrollIntoView||r.scrollIntoView("boolean"==typeof e?{block:"nearest",inline:"nearest"}:e))}))}),[k,Y]);i((()=>{document.createElement("div").focus({get preventScroll(){return Le=!0,!1}})}),[]),i((()=>{d&&(r?K.current&&null!=m&&(V.current=!0,j(m)):q.current&&(_.current=-1,W.current(null)))}),[d,r,m,j]),i((()=>{if(d&&r)if(null==a){if(U.current=!1,null!=m)return;q.current&&(_.current=-1,$(s,_)),!q.current&&K.current&&(null!=B.current||!0===K.current&&null==B.current)&&(_.current=null==B.current||qe(B.current,A,w)||h?Ue(s,z.current):Ve(s,z.current),j(_.current))}else _e(s,a)||(_.current=a,$(s,_,V.current),V.current=!1)}),[d,r,a,m,h,s,A,w,j,$,z]),i((()=>{if(d&&q.current&&!r){var e,t;const n=null==N||null==(e=N.nodesRef.current.find((e=>e.id===D)))||null==(t=e.context)?void 0:t.elements.floating;n&&!te(n,ee(g(n)))&&n.focus({preventScroll:!0})}}),[d,r,N,D]),i((()=>{B.current=null,W.current=j,q.current=r}));const J=null!=a,Q=u.useMemo((()=>{function e(e){if(!r)return;const t=s.current.indexOf(e);-1!==t&&j(t)}return{onFocus(t){let{currentTarget:n}=t;e(n)},onClick:e=>{let{currentTarget:t}=e;return t.focus({preventScroll:!0})},...P&&{onMouseMove(t){let{currentTarget:n}=t;e(n)},onPointerLeave(){var e;H.current&&(_.current=-1,$(s,_),n.flushSync((()=>j(null))),k||null==(e=c.floating.current)||e.focus({preventScroll:!0}))}}}}),[r,c,$,P,s,j,k]);return u.useMemo((()=>{if(!d)return{};const e=z.current;function t(t){if(H.current=!1,U.current=!0,!X.current&&t.currentTarget===c.floating.current)return;if(h&&function(e,t,n){return He(t,n?e===je:e===Ne,e===Fe)}(t.key,A,w))return ce(t),o(!1),void(E(l)&&l.focus());const n=_.current,u=Ue(s,e),i=Ve(s,e);if("Home"===t.key&&(_.current=u,j(_.current)),"End"===t.key&&(_.current=i,j(_.current)),L>1){const n=_.current;if(t.key===Fe){if(ce(t),-1===n)_.current=i;else if(_.current=Be(s,{startingIndex:n,amount:L,decrement:!0,disabledIndices:e}),y&&(n-L<u||_.current<0)){const e=n%L,t=i%L,r=i-(t-e);_.current=t===e?i:t>e?r:r-L}_e(s,_.current)&&(_.current=n),j(_.current)}if(t.key===De&&(ce(t),-1===n?_.current=u:(_.current=Be(s,{startingIndex:n,amount:L,disabledIndices:e}),y&&n+L>i&&(_.current=Be(s,{startingIndex:n%L-L,amount:L,disabledIndices:e}))),_e(s,_.current)&&(_.current=n),j(_.current)),"both"===A){const r=Math.floor(n/L);t.key===je&&(ce(t),n%L!=L-1?(_.current=Be(s,{startingIndex:n,disabledIndices:e}),y&&Ke(_.current,L,r)&&(_.current=Be(s,{startingIndex:n-n%L-1,disabledIndices:e}))):y&&(_.current=Be(s,{startingIndex:n-n%L-1,disabledIndices:e})),Ke(_.current,L,r)&&(_.current=n)),t.key===Ne&&(ce(t),n%L!=0?(_.current=Be(s,{startingIndex:n,disabledIndices:e,decrement:!0}),y&&Ke(_.current,L,r)&&(_.current=Be(s,{startingIndex:n+(L-n%L),decrement:!0,disabledIndices:e}))):y&&(_.current=Be(s,{startingIndex:n+(L-n%L),decrement:!0,disabledIndices:e})),Ke(_.current,L,r)&&(_.current=n));const o=Math.floor(i/L)===r;return _e(s,_.current)&&(_.current=y&&o?t.key===Ne?i:Be(s,{startingIndex:n-n%L-1,disabledIndices:e}):n),void j(_.current)}}if(We(t.key,A)){if(ce(t),r&&!k&&ee(t.currentTarget.ownerDocument)===t.currentTarget)return _.current=qe(t.key,A,w)?u:i,void j(_.current);qe(t.key,A,w)?_.current=y?n>=i?b&&n!==s.current.length?-1:u:Be(s,{startingIndex:n,disabledIndices:e}):Math.min(i,Be(s,{startingIndex:n,disabledIndices:e})):_.current=y?n<=u?b&&-1!==n?s.current.length:i:Be(s,{startingIndex:n,decrement:!0,disabledIndices:e}):Math.max(u,Be(s,{startingIndex:n,decrement:!0,disabledIndices:e})),_e(s,_.current)?j(null):j(_.current)}}function n(e){"auto"===S&&R(e.nativeEvent)&&(K.current=!0)}const u=k&&r&&J&&{"aria-activedescendant":G};return{reference:{...u,onKeyDown(n){H.current=!1;const u=0===n.key.indexOf("Arrow");if(k&&r)return t(n);if(!r&&!C&&u)return;(u||"Enter"===n.key||" "===n.key||""===n.key)&&(B.current=n.key),h?function(e,t,n){return He(t,n?e===Ne:e===je,e===De)}(n.key,A,w)&&(ce(n),r?(_.current=Ue(s,e),j(_.current)):o(!0)):We(n.key,A)&&(null!=m&&(_.current=m),ce(n),!r&&C?o(!0):t(n),r&&j(_.current))},onFocus(){r&&j(null)},onPointerDown:function(e){K.current=S,"auto"===S&&x(e.nativeEvent)&&(K.current=!0)},onMouseDown:n,onClick:n},floating:{"aria-orientation":"both"===A?void 0:A,...u,onKeyDown:t,onPointerMove(){H.current=!0}},item:Q}}),[l,c,G,z,X,s,d,A,w,k,r,J,h,m,C,b,L,y,S,j,o,Q])},e.useMergeRefs=function(e){return u.useMemo((()=>e.every((e=>null==e))?null:t=>{e.forEach((e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)}))}),e)},e.useRole=function(e,t){let{open:n}=e,{enabled:r=!0,role:o="dialog"}=void 0===t?{}:t;const i=a(),c=a();return u.useMemo((()=>{const e={id:i,role:o};return r?"tooltip"===o?{reference:{"aria-describedby":n?i:void 0},floating:e}:{reference:{"aria-expanded":n?"true":"false","aria-haspopup":"alertdialog"===o?"dialog":o,"aria-controls":n?i:void 0,..."listbox"===o&&{role:"combobox"},..."menu"===o&&{id:c}},floating:{...e,..."menu"===o&&{"aria-labelledby":c}}}:{}}),[r,o,n,i,c])},e.useTransitionStatus=Xe,e.useTransitionStyles=function(e,t){let{initial:n={opacity:0},open:r,close:o,common:c,duration:l=250}=void 0===t?{}:t;const s=e.placement,a=s.split("-")[0],[f,d]=u.useState({}),{isMounted:m,status:p}=Xe(e,{duration:l}),v=T(n),g=T(r),b=T(o),y=T(c),h="number"==typeof l,w=(h?l:l.open)||0,E=(h?l:l.close)||0;return i((()=>{const e={side:a,placement:s},t=v.current,n=b.current,r=g.current,o=y.current,u="function"==typeof t?t(e):t,i="function"==typeof n?n(e):n,c="function"==typeof o?o(e):o,l=("function"==typeof r?r(e):r)||Object.keys(u).reduce(((e,t)=>(e[t]="",e)),{});if("initial"!==p&&"unmounted"!==p||d((e=>({transitionProperty:e.transitionProperty,...c,...u}))),"open"===p&&d({transitionProperty:Object.keys(l).map(ze).join(","),transitionDuration:w+"ms",...c,...l}),"close"===p){const e=i||u;d({transitionProperty:Object.keys(e).map(ze).join(","),transitionDuration:E+"ms",...c,...e})}}),[a,s,E,b,v,g,y,w,p]),{isMounted:m,styles:f}},e.useTypeahead=function(e,t){var n;let{open:r,dataRef:o,refs:c}=e,{listRef:l,activeIndex:s,onMatch:a=(()=>{}),enabled:f=!0,findMatch:d=null,resetMs:m=1e3,ignoreKeys:p=[],selectedIndex:v=null}=void 0===t?{listRef:{current:[]},activeIndex:null}:t;const b=u.useRef(),y=u.useRef(""),h=u.useRef(null!=(n=null!=v?v:s)?n:-1),E=u.useRef(null),R=ge(a),x=T(d),I=T(p);return i((()=>{r&&(clearTimeout(b.current),E.current=null,y.current="")}),[r]),i((()=>{var e;r&&""===y.current&&(h.current=null!=(e=null!=v?v:s)?e:-1)}),[r,v,s]),u.useMemo((()=>{if(!f)return{};function e(e){var t;const n=ue(e.nativeEvent);if(w(n)&&(ee(g(n))!==e.currentTarget?null!=(t=c.floating.current)&&t.contains(n)&&n.closest('[role="dialog"],[role="menu"],[role="listbox"],[role="tree"],[role="grid"]')!==e.currentTarget:!e.currentTarget.contains(n)))return;y.current.length>0&&" "!==y.current[0]&&(o.current.typing=!0," "===e.key&&ce(e));const r=l.current;if(null==r||I.current.includes(e.key)||1!==e.key.length||e.ctrlKey||e.metaKey||e.altKey)return;r.every((e=>{var t,n;return!e||(null==(t=e[0])?void 0:t.toLocaleLowerCase())!==(null==(n=e[1])?void 0:n.toLocaleLowerCase())}))&&y.current===e.key&&(y.current="",h.current=E.current),y.current+=e.key,clearTimeout(b.current),b.current=setTimeout((()=>{y.current="",h.current=E.current,o.current.typing=!1}),m);const u=h.current,i=[...r.slice((u||0)+1),...r.slice(0,(u||0)+1)],s=x.current?x.current(i,y.current):i.find((e=>0===(null==e?void 0:e.toLocaleLowerCase().indexOf(y.current.toLocaleLowerCase())))),a=s?r.indexOf(s):-1;-1!==a&&(R(a),E.current=a)}return{reference:{onKeyDown:e},floating:{onKeyDown:e}}}),[f,o,l,m,I,x,R,c])},Object.defineProperty(e,"__esModule",{value:!0})}));
