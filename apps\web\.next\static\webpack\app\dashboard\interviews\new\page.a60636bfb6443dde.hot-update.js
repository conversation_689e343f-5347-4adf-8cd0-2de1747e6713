"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/interviews/new/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/interviews/new/page.tsx":
/*!***************************************************!*\
  !*** ./src/app/dashboard/interviews/new/page.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ NewInterviewPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _services_aiInterviewService__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/services/aiInterviewService */ \"(app-pages-browser)/./src/services/aiInterviewService.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Brain,Building,Code,FileText,Heart,Mic,Palette,Settings,TrendingUp,Users,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Brain,Building,Code,FileText,Heart,Mic,Palette,Settings,TrendingUp,Users,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Brain,Building,Code,FileText,Heart,Mic,Palette,Settings,TrendingUp,Users,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Brain,Building,Code,FileText,Heart,Mic,Palette,Settings,TrendingUp,Users,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Brain,Building,Code,FileText,Heart,Mic,Palette,Settings,TrendingUp,Users,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Brain,Building,Code,FileText,Heart,Mic,Palette,Settings,TrendingUp,Users,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Brain,Building,Code,FileText,Heart,Mic,Palette,Settings,TrendingUp,Users,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/video.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Brain,Building,Code,FileText,Heart,Mic,Palette,Settings,TrendingUp,Users,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Brain,Building,Code,FileText,Heart,Mic,Palette,Settings,TrendingUp,Users,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Brain,Building,Code,FileText,Heart,Mic,Palette,Settings,TrendingUp,Users,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Brain,Building,Code,FileText,Heart,Mic,Palette,Settings,TrendingUp,Users,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Brain,Building,Code,FileText,Heart,Mic,Palette,Settings,TrendingUp,Users,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Brain,Building,Code,FileText,Heart,Mic,Palette,Settings,TrendingUp,Users,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Brain,Building,Code,FileText,Heart,Mic,Palette,Settings,TrendingUp,Users,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction NewInterviewPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [isCreating, setIsCreating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [setup, setSetup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        jobTitle: \"\",\n        company: \"\",\n        industry: \"\",\n        difficulty: \"medium\",\n        duration: 30,\n        questionTypes: [\n            \"behavioral\",\n            \"technical\"\n        ],\n        interviewType: \"video\",\n        jobDescription: \"\",\n        customQuestions: []\n    });\n    const industries = [\n        {\n            id: \"technology\",\n            name: \"Technology\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            color: \"blue\"\n        },\n        {\n            id: \"finance\",\n            name: \"Finance\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            color: \"green\"\n        },\n        {\n            id: \"healthcare\",\n            name: \"Healthcare\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            color: \"red\"\n        },\n        {\n            id: \"consulting\",\n            name: \"Consulting\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            color: \"purple\"\n        },\n        {\n            id: \"design\",\n            name: \"Design\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            color: \"pink\"\n        },\n        {\n            id: \"other\",\n            name: \"Other\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n            color: \"gray\"\n        }\n    ];\n    const questionTypes = [\n        {\n            id: \"behavioral\",\n            name: \"Behavioral\",\n            description: \"Past experiences and situations\"\n        },\n        {\n            id: \"technical\",\n            name: \"Technical\",\n            description: \"Role-specific technical knowledge\"\n        },\n        {\n            id: \"situational\",\n            name: \"Situational\",\n            description: \"Hypothetical scenarios\"\n        },\n        {\n            id: \"company-specific\",\n            name: \"Company-Specific\",\n            description: \"Company culture and values\"\n        }\n    ];\n    const interviewTypes = [\n        {\n            id: \"video\",\n            name: \"Video Interview\",\n            description: \"Full video recording with AI analysis of verbal and non-verbal communication\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n            features: [\n                \"Video recording\",\n                \"Body language analysis\",\n                \"Eye contact tracking\",\n                \"Professional presence\"\n            ]\n        },\n        {\n            id: \"audio\",\n            name: \"Audio Interview\",\n            description: \"Audio-only recording focusing on verbal communication and content\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n            features: [\n                \"Audio recording\",\n                \"Speech analysis\",\n                \"Pace and clarity\",\n                \"Content quality\"\n            ]\n        },\n        {\n            id: \"text\",\n            name: \"Text Interview\",\n            description: \"Written responses with AI analysis of structure and content\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n            features: [\n                \"Written responses\",\n                \"Structure analysis\",\n                \"Grammar check\",\n                \"Content depth\"\n            ]\n        }\n    ];\n    const handleNext = ()=>{\n        if (currentStep < 4) {\n            setCurrentStep(currentStep + 1);\n        }\n    };\n    const handlePrevious = ()=>{\n        if (currentStep > 1) {\n            setCurrentStep(currentStep - 1);\n        }\n    };\n    const handleCreateInterview = async ()=>{\n        setIsCreating(true);\n        try {\n            // Generate questions based on setup\n            const questions = await _services_aiInterviewService__WEBPACK_IMPORTED_MODULE_9__.aiInterviewService.generateQuestions({\n                jobTitle: setup.jobTitle,\n                industry: setup.industry,\n                difficulty: setup.difficulty,\n                count: Math.floor(setup.duration / 5),\n                types: setup.questionTypes\n            });\n            // Store interview setup in sessionStorage\n            sessionStorage.setItem(\"interviewSetup\", JSON.stringify({\n                ...setup,\n                questions\n            }));\n            // Navigate to practice page\n            router.push(\"/dashboard/interviews/practice\");\n        } catch (error) {\n            console.error(\"Error creating interview:\", error);\n        } finally{\n            setIsCreating(false);\n        }\n    };\n    const updateSetup = (updates)=>{\n        setSetup((prev)=>({\n                ...prev,\n                ...updates\n            }));\n    };\n    const toggleQuestionType = (type)=>{\n        const currentTypes = setup.questionTypes;\n        if (currentTypes.includes(type)) {\n            updateSetup({\n                questionTypes: currentTypes.filter((t)=>t !== type)\n            });\n        } else {\n            updateSetup({\n                questionTypes: [\n                    ...currentTypes,\n                    type\n                ]\n            });\n        }\n    };\n    const getStepTitle = (step)=>{\n        switch(step){\n            case 1:\n                return \"Basic Information\";\n            case 2:\n                return \"Interview Type & Settings\";\n            case 3:\n                return \"Question Configuration\";\n            case 4:\n                return \"Review & Create\";\n            default:\n                return \"Setup\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-4xl mx-auto space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            onClick: ()=>router.back(),\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Back\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-foreground flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"h-8 w-8 text-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Create New Interview\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground mt-1\",\n                                    children: \"Set up a personalized AI-powered interview session\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                lineNumber: 168,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    1,\n                    2,\n                    3,\n                    4\n                ].map((step)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium \".concat(step <= currentStep ? \"bg-primary text-primary-foreground\" : \"bg-muted text-muted-foreground\"),\n                                children: step\n                            }, void 0, false, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 13\n                            }, this),\n                            step < 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-1 mx-2 \".concat(step < currentStep ? \"bg-primary\" : \"bg-muted\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, step, true, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                lineNumber: 192,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                            className: \"flex items-center space-x-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Step \",\n                                    currentStep,\n                                    \": \",\n                                    getStepTitle(currentStep)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        className: \"space-y-6\",\n                        children: [\n                            currentStep === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"title\",\n                                                children: \"Interview Title\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"title\",\n                                                value: setup.title,\n                                                onChange: (e)=>updateSetup({\n                                                        title: e.target.value\n                                                    }),\n                                                placeholder: \"e.g., Software Engineer Interview Practice\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"jobTitle\",\n                                                        children: \"Job Title\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        id: \"jobTitle\",\n                                                        value: setup.jobTitle,\n                                                        onChange: (e)=>updateSetup({\n                                                                jobTitle: e.target.value\n                                                            }),\n                                                        placeholder: \"e.g., Senior Software Engineer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"company\",\n                                                        children: \"Company (Optional)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        id: \"company\",\n                                                        value: setup.company,\n                                                        onChange: (e)=>updateSetup({\n                                                                company: e.target.value\n                                                            }),\n                                                        placeholder: \"e.g., Google, Microsoft\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                children: \"Industry\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 md:grid-cols-3 gap-3\",\n                                                children: industries.map((industry)=>{\n                                                    const Icon = industry.icon;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4 border rounded-lg cursor-pointer transition-all \".concat(setup.industry === industry.id ? \"border-primary bg-primary/10\" : \"border-border hover:border-border/80\"),\n                                                        onClick: ()=>updateSetup({\n                                                                industry: industry.id\n                                                            }),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                    className: \"h-5 w-5 text-\".concat(industry.color, \"-600\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 269,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: industry.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 270,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, industry.id, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 23\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 13\n                            }, this),\n                            currentStep === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                children: \"Interview Type\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                children: interviewTypes.map((type)=>{\n                                                    const Icon = type.icon;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4 border rounded-lg cursor-pointer transition-all \".concat(setup.interviewType === type.id ? \"border-primary bg-primary/10\" : \"border-border hover:border-border/80\"),\n                                                        onClick: ()=>updateSetup({\n                                                                interviewType: type.id\n                                                            }),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                            className: \"h-5 w-5 text-primary\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 300,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: type.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 301,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 299,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: type.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 303,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-1\",\n                                                                    children: type.features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2 text-xs text-muted-foreground\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-1 h-1 bg-muted-foreground rounded-full\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                                    lineNumber: 307,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: feature\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                                    lineNumber: 308,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, index, true, {\n                                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 306,\n                                                                            columnNumber: 31\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 304,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                            lineNumber: 298,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, type.id, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 23\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"duration\",\n                                                        children: \"Duration (minutes)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        id: \"duration\",\n                                                        value: setup.duration,\n                                                        onChange: (e)=>updateSetup({\n                                                                duration: Number(e.target.value)\n                                                            }),\n                                                        className: \"w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring bg-background text-foreground\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 15,\n                                                                children: \"15 minutes\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                lineNumber: 328,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 30,\n                                                                children: \"30 minutes\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 45,\n                                                                children: \"45 minutes\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                lineNumber: 330,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 60,\n                                                                children: \"60 minutes\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                lineNumber: 331,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"difficulty\",\n                                                        children: \"Difficulty Level\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        id: \"difficulty\",\n                                                        value: setup.difficulty,\n                                                        onChange: (e)=>updateSetup({\n                                                                difficulty: e.target.value\n                                                            }),\n                                                        className: \"w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring bg-background text-foreground\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"easy\",\n                                                                children: \"Easy\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                lineNumber: 342,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"medium\",\n                                                                children: \"Medium\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                lineNumber: 343,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"hard\",\n                                                                children: \"Hard\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                lineNumber: 344,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 13\n                            }, this),\n                            currentStep === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                children: \"Question Types\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-3\",\n                                                children: questionTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4 border rounded-lg cursor-pointer transition-all \".concat(setup.questionTypes.includes(type.id) ? \"border-primary bg-primary/10\" : \"border-border hover:border-border/80\"),\n                                                        onClick: ()=>toggleQuestionType(type.id),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"font-medium\",\n                                                                            children: type.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 369,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-muted-foreground\",\n                                                                            children: type.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 370,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 368,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                setup.questionTypes.includes(type.id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                    variant: \"default\",\n                                                                    children: \"Selected\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 373,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, type.id, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                        lineNumber: 358,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"jobDescription\",\n                                                children: \"Job Description (Optional)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                id: \"jobDescription\",\n                                                value: setup.jobDescription,\n                                                onChange: (e)=>updateSetup({\n                                                        jobDescription: e.target.value\n                                                    }),\n                                                placeholder: \"Paste the job description here for more targeted questions...\",\n                                                rows: 4\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"Adding a job description helps our AI generate more relevant questions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 13\n                            }, this),\n                            currentStep === 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-50 p-6 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-blue-900 mb-4\",\n                                                children: \"Interview Summary\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-blue-800\",\n                                                                children: \"Title:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                lineNumber: 404,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-2\",\n                                                                children: setup.title || \"Untitled Interview\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                lineNumber: 405,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                        lineNumber: 403,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-blue-800\",\n                                                                children: \"Job Title:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                lineNumber: 408,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-2\",\n                                                                children: setup.jobTitle\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                lineNumber: 409,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                        lineNumber: 407,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-blue-800\",\n                                                                children: \"Industry:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                lineNumber: 412,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-2 capitalize\",\n                                                                children: setup.industry\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                lineNumber: 413,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-blue-800\",\n                                                                children: \"Duration:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                lineNumber: 416,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-2\",\n                                                                children: [\n                                                                    setup.duration,\n                                                                    \" minutes\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                lineNumber: 417,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                        lineNumber: 415,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-blue-800\",\n                                                                children: \"Type:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                lineNumber: 420,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-2 capitalize\",\n                                                                children: setup.interviewType\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                lineNumber: 421,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                        lineNumber: 419,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-blue-800\",\n                                                                children: \"Difficulty:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                lineNumber: 424,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-2 capitalize\",\n                                                                children: setup.difficulty\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                lineNumber: 425,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                        lineNumber: 423,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium text-blue-800\",\n                                                        children: \"Question Types:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-2 mt-2\",\n                                                        children: setup.questionTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                variant: \"secondary\",\n                                                                className: \"capitalize\",\n                                                                children: type.replace(\"-\", \" \")\n                                                            }, type, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                                lineNumber: 432,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                        lineNumber: 430,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4 p-4 bg-green-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"h-8 w-8 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                lineNumber: 441,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-green-900\",\n                                                        children: \"AI-Powered Features\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                        lineNumber: 443,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-green-700\",\n                                                        children: \"Your interview will include real-time AI analysis, personalized feedback, and performance scoring\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                lineNumber: 212,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        variant: \"outline\",\n                        onClick: handlePrevious,\n                        disabled: currentStep === 1,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                lineNumber: 461,\n                                columnNumber: 11\n                            }, this),\n                            \"Previous\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                        lineNumber: 456,\n                        columnNumber: 9\n                    }, this),\n                    currentStep < 4 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        onClick: handleNext,\n                        disabled: currentStep === 1 && (!setup.jobTitle || !setup.industry) || currentStep === 3 && setup.questionTypes.length === 0,\n                        children: [\n                            \"Next\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                className: \"ml-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                lineNumber: 474,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                        lineNumber: 466,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        onClick: handleCreateInterview,\n                        disabled: isCreating || !setup.jobTitle || !setup.industry,\n                        className: \"flex items-center space-x-2\",\n                        children: isCreating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                    className: \"mr-2 h-4 w-4 animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                    lineNumber: 484,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Creating Interview...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                    lineNumber: 485,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Brain_Building_Code_FileText_Heart_Mic_Palette_Settings_TrendingUp_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    className: \"mr-2 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                    lineNumber: 489,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Start Interview\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                                    lineNumber: 490,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                        lineNumber: 477,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n                lineNumber: 455,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\new\\\\page.tsx\",\n        lineNumber: 166,\n        columnNumber: 5\n    }, this);\n}\n_s(NewInterviewPage, \"tyB/KZ4bdaElduFTKLwGo/eK0eQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = NewInterviewPage;\nvar _c;\n$RefreshReg$(_c, \"NewInterviewPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/interviews/new/page.tsx\n"));

/***/ })

});