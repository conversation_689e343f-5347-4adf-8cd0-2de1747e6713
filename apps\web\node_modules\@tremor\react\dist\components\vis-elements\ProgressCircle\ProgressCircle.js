import{__rest as e}from"tslib";import r,{useTooltip as t}from"../../util-elements/Tooltip/Tooltip.js";import{colorPalette as o}from"../../../lib/theme.js";import{tremorTwMerge as s}from"../../../lib/tremorTwMerge.js";import{getColorClassNames as a,makeClassName as i}from"../../../lib/utils.js";import l from"react";const n=i("ProgressBar"),c={xs:{radius:15,strokeWidth:3},sm:{radius:19,strokeWidth:4},md:{radius:32,strokeWidth:6},lg:{radius:52,strokeWidth:8},xl:{radius:80,strokeWidth:10}};const d=l.forwardRef(((i,d)=>{const{value:m,size:k="md",className:u,showAnimation:f=!0,color:h,tooltip:p,radius:b,strokeWidth:g,children:x}=i,W=e(i,["value","size","className","showAnimation","color","tooltip","radius","strokeWidth","children"]),j=void 0===(v=m)?0:v>100?100:v;var v;const N=null!=b?b:c[k].radius,y=null!=g?g:c[k].strokeWidth,E=N-y/2,w=2*E*Math.PI,P=w-j/100*w,{tooltipProps:C,getReferenceProps:O}=t();return l.createElement(l.Fragment,null,l.createElement(r,Object.assign({text:p},C)),l.createElement("div",Object.assign({ref:d,className:s(n("root"),"flex flex-col items-center justify-center",u)},W),l.createElement("svg",Object.assign({ref:C.refs.setReference,width:2*N,height:2*N,viewBox:`0 0 ${2*N} ${2*N}`,className:"transform -rotate-90"},O),l.createElement("circle",{r:E,cx:N,cy:N,strokeWidth:y,fill:"transparent",stroke:"",strokeLinecap:"round",className:s("transition-colors ease-linear",h?`${a(h,o.background).strokeColor} opacity-20 dark:opacity-25`:"stroke-tremor-brand-muted/50 dark:stroke-dark-tremor-brand-muted")}),j>=0?l.createElement("circle",{r:E,cx:N,cy:N,strokeWidth:y,strokeDasharray:w+" "+w,strokeDashoffset:P,fill:"transparent",stroke:"",strokeLinecap:"round",className:s("transition-colors ease-linear",h?a(h,o.background).strokeColor:"stroke-tremor-brand dark:stroke-dark-tremor-brand",f?"transition-all duration-300 ease-in-out":"")}):null),l.createElement("div",{className:s("absolute flex")},x)))}));d.displayName="ProgressCircle";export{d as default};
