"use strict";var e=require("tslib"),t=require("react"),r=require("../../../lib/tremorTwMerge.cjs"),a=require("../../../lib/utils.cjs");function l(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var s=l(t);const o=a.makeClassName("Table"),c=s.default.forwardRef(((t,a)=>{const{children:l,className:c}=t,u=e.__rest(t,["children","className"]);return s.default.createElement("div",{className:r.tremorTwMerge(o("root"),"overflow-auto",c)},s.default.createElement("table",Object.assign({ref:a,className:r.tremorTwMerge(o("table"),"w-full text-tremor-default","text-tremor-content","dark:text-dark-tremor-content")},u),l))}));c.displayName="Table",module.exports=c;
