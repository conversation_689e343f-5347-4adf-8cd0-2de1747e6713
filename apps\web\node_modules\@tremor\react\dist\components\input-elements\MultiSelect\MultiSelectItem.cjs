'use client';
"use strict";var e=require("tslib");require("../../../contexts/BaseColorContext.cjs"),require("../../../contexts/IndexContext.cjs"),require("../../../contexts/RootStylesContext.cjs");var t=require("../../../contexts/SelectedValueContext.cjs"),r=require("react"),a=require("../../../lib/tremorTwMerge.cjs"),o=require("../../../lib/utils.cjs"),c=require("@headlessui/react");function s(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var n=s(r);const u=o.makeClassName("MultiSelectItem"),d=n.default.forwardRef(((s,d)=>{const{value:l,className:m,children:i}=s,x=e.__rest(s,["value","className","children"]),{selectedValue:f}=r.useContext(t),k=o.isValueInArray(l,f);return n.default.createElement(c.ListboxOption,Object.assign({className:a.tremorTwMerge(u("root"),"flex justify-start items-center cursor-default text-tremor-default p-2.5","data-[focus]:bg-tremor-background-muted data-[focus]:text-tremor-content-strong data-[select]ed:text-tremor-content-strong text-tremor-content-emphasis","dark:data-[focus]:bg-dark-tremor-background-muted dark:data-[focus]:text-dark-tremor-content-strong dark:data-[select]ed:text-dark-tremor-content-strong dark:data-[select]ed:bg-dark-tremor-background-muted dark:text-dark-tremor-content-emphasis",m),ref:d,key:l,value:l},x),n.default.createElement("input",{type:"checkbox",className:a.tremorTwMerge(u("checkbox"),"flex-none focus:ring-none focus:outline-none cursor-pointer mr-2.5","accent-tremor-brand","dark:accent-dark-tremor-brand"),checked:k,readOnly:!0}),n.default.createElement("span",{className:"whitespace-nowrap truncate"},null!=i?i:l))}));d.displayName="MultiSelectItem",module.exports=d;
