import{startOfToday as t,sub as e,startOfMonth as r,startOfYear as o,startOfDay as n,max as a,min as i,format as u,isEqual as l}from"date-fns";import"../../../lib/tremorTwMerge.js";import{makeClassName as m}from"../../../lib/utils.js";const c=m("DateRangePicker"),f=(t,e,r,o)=>{var i;if(r&&(t=null===(i=o.get(r))||void 0===i?void 0:i.from),t)return n(t&&!e?t:a([t,e]))},s=(e,r,o,a)=>{var u,l;if(o&&(e=n(null!==(l=null===(u=a.get(o))||void 0===u?void 0:u.to)&&void 0!==l?l:t())),e)return n(e&&!r?e:i([e,r]))},d=[{value:"tdy",text:"Today",from:t()},{value:"w",text:"Last 7 days",from:e(t(),{days:7})},{value:"t",text:"Last 30 days",from:e(t(),{days:30})},{value:"m",text:"Month to Date",from:r(t())},{value:"y",text:"Year to Date",from:o(t())}],g=(t,e,r,o)=>{const n=(null==r?void 0:r.code)||"en-US";if(!t&&!e)return"";if(t&&!e){if(o)return u(t,o);const e={year:"numeric",month:"short",day:"numeric"};return t.toLocaleDateString(n,e)}if(t&&e){if(l(t,e)){if(o)return u(t,o);const e={year:"numeric",month:"short",day:"numeric"};return t.toLocaleDateString(n,e)}if(t.getMonth()===e.getMonth()&&t.getFullYear()===e.getFullYear()){if(o)return`${u(t,o)} - ${u(e,o)}`;const r={month:"short",day:"numeric"};return`${t.toLocaleDateString(n,r)} - \n                    ${e.getDate()}, ${e.getFullYear()}`}{if(o)return`${u(t,o)} - ${u(e,o)}`;const r={year:"numeric",month:"short",day:"numeric"};return`${t.toLocaleDateString(n,r)} - \n                    ${e.toLocaleDateString(n,r)}`}}return""};export{d as defaultOptions,g as formatSelectedDates,c as makeDateRangePickerClassName,s as parseEndDate,f as parseStartDate};
