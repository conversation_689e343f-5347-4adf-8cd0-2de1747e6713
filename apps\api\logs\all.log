{"level":"error","message":"Failed to start server: Database health check failed","stack":"Error: Database health check failed\n    at initializeDatabase (C:\\apps\\InterviewSpark\\apps\\api\\src\\database\\connection.ts:56:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async startServer (C:\\apps\\InterviewSpark\\apps\\api\\src\\index.ts:192:5)","timestamp":"2025-07-19 19:48:30:4830"}
{"level":"error","message":"Failed to start server: Database health check failed","stack":"Error: Database health check failed\n    at initializeDatabase (C:\\apps\\InterviewSpark\\apps\\api\\src\\database\\connection.ts:56:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async startServer (C:\\apps\\InterviewSpark\\apps\\api\\src\\index.ts:192:5)","timestamp":"2025-07-19 19:48:46:4846"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-19 19:49:09:499"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-19 19:49:09:499"}
{"level":"info","message":"🔐 Authentication: Clerk","timestamp":"2025-07-19 19:49:09:499"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-19 19:49:09:499"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-19 19:49:09:499"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-07-19 19:49:09:499"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-07-19 19:49:19:4919"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-07-19 19:49:24:4924"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-07-19 19:49:37:4937"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-07-19 19:50:04:504"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-07-19 19:50:15:5015"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-07-19 19:50:21:5021"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-20 16:50:29:5029"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-07-20 16:50:29:5029"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-20 16:50:29:5029"}
{"level":"info","message":"🔐 Authentication: Clerk","timestamp":"2025-07-20 16:50:29:5029"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-20 16:50:29:5029"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-20 16:50:29:5029"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-20 18:51:30:5130"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-20 18:51:30:5130"}
{"level":"info","message":"🔐 Authentication: Clerk","timestamp":"2025-07-20 18:51:30:5130"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-20 18:51:30:5130"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-20 18:51:30:5130"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-07-20 18:51:32:5132"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-20 19:20:17:2017"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-20 19:20:17:2017"}
{"level":"info","message":"🔐 Authentication: Clerk","timestamp":"2025-07-20 19:20:17:2017"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-20 19:20:17:2017"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-20 19:20:17:2017"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-20 19:27:30:2730"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-20 19:27:30:2730"}
{"level":"info","message":"🔐 Authentication: Clerk","timestamp":"2025-07-20 19:27:30:2730"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-20 19:27:30:2730"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-20 19:27:30:2730"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-20 19:33:35:3335"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-20 19:33:35:3335"}
{"level":"info","message":"🔐 Authentication: Clerk","timestamp":"2025-07-20 19:33:35:3335"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-20 19:33:35:3335"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-20 19:33:35:3335"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-07-20 19:35:45:3545"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-20 19:59:54:5954"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-20 19:59:54:5954"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-20 19:59:54:5954"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-20 19:59:54:5954"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-20 19:59:54:5954"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-20 20:08:10:810"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-20 20:08:10:810"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-20 20:08:10:810"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-20 20:08:10:810"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-20 20:08:10:810"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-20 20:15:11:1511"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-20 20:15:11:1511"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-20 20:15:11:1511"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-20 20:15:11:1511"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-20 20:15:11:1511"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-20 20:17:02:172"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-20 20:17:02:172"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-20 20:17:02:172"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-20 20:17:02:172"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-20 20:17:02:172"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-20 20:25:23:2523"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-20 20:25:23:2523"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-20 20:25:23:2523"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-20 20:25:23:2523"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-20 20:25:23:2523"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-07-20 20:27:38:2738"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-20 20:28:59:2859"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-20 20:28:59:2859"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-20 20:28:59:2859"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-20 20:28:59:2859"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-20 20:28:59:2859"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-20 20:34:31:3431"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-20 20:34:31:3431"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-20 20:34:31:3431"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-20 20:34:31:3431"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-20 20:34:31:3431"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-20 20:42:15:4215"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-20 20:42:15:4215"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-20 20:42:15:4215"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-20 20:42:15:4215"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-20 20:42:15:4215"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-21 11:37:42:3742"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-21 11:37:42:3742"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-21 11:37:42:3742"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-21 11:37:42:3742"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-21 11:37:42:3742"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-21 12:13:43:1343"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-21 12:13:43:1343"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-21 12:13:43:1343"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-21 12:13:43:1343"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-21 12:13:43:1343"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-21 13:01:59:159"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-21 13:01:59:159"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-21 13:01:59:159"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-21 13:01:59:159"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-21 13:01:59:159"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-21 13:02:03:23"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-21 13:02:03:23"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-21 13:02:03:23"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-21 13:02:03:23"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-21 13:02:03:23"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-21 13:02:19:219"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-21 13:02:19:219"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-21 13:02:19:219"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-21 13:02:19:219"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-21 13:02:19:219"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-21 13:03:11:311"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-21 13:03:11:311"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-21 13:03:11:311"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-21 13:03:11:311"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-21 13:03:11:311"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-21 15:23:42:2342"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-21 15:23:42:2342"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-21 15:23:42:2342"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-21 15:23:42:2342"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-21 15:23:42:2342"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-21 15:50:39:5039"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-21 15:50:39:5039"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-21 15:50:39:5039"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-21 15:50:39:5039"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-21 15:50:39:5039"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-21 15:53:05:535"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-21 15:53:05:535"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-21 15:53:05:535"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-21 15:53:05:535"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-21 15:53:05:535"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-21 20:51:16:5116"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-21 20:51:16:5116"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-21 20:51:16:5116"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-21 20:51:16:5116"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-21 20:51:16:5116"}
{"ip":"::1","level":"error","message":"Error occurred: Expected property name or '}' in JSON at position 1 (line 1 column 2)","method":"POST","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at parse (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\body-parser\\lib\\types\\json.js:92:19)\n    at C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:507:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","statusCode":400,"timestamp":"2025-07-21 20:57:05:575","url":"/api/auth/login","userAgent":"curl/8.13.0"}
{"ip":"::1","level":"error","message":"Error occurred: Expected property name or '}' in JSON at position 1 (line 1 column 2)","method":"POST","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at parse (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\body-parser\\lib\\types\\json.js:92:19)\n    at C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:507:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","statusCode":400,"timestamp":"2025-07-21 20:57:42:5742","url":"/api/auth/login","userAgent":"curl/8.13.0"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-21 20:58:52:5852"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-21 20:58:52:5852"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-21 20:58:52:5852"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-21 20:58:52:5852"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-21 20:58:52:5852"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-21 20:59:12:5912"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-21 20:59:12:5912"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-21 20:59:12:5912"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-21 20:59:12:5912"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-21 20:59:12:5912"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-21 21:02:24:224"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-21 21:02:24:224"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-21 21:02:24:224"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-21 21:02:24:224"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-21 21:02:24:224"}
