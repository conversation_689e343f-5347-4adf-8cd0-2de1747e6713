{"level":"error","message":"Failed to start server: Database health check failed","stack":"Error: Database health check failed\n    at initializeDatabase (C:\\apps\\InterviewSpark\\apps\\api\\src\\database\\connection.ts:56:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async startServer (C:\\apps\\InterviewSpark\\apps\\api\\src\\index.ts:192:5)","timestamp":"2025-07-19 19:48:30:4830"}
{"level":"error","message":"Failed to start server: Database health check failed","stack":"Error: Database health check failed\n    at initializeDatabase (C:\\apps\\InterviewSpark\\apps\\api\\src\\database\\connection.ts:56:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async startServer (C:\\apps\\InterviewSpark\\apps\\api\\src\\index.ts:192:5)","timestamp":"2025-07-19 19:48:46:4846"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-19 19:49:09:499"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-19 19:49:09:499"}
{"level":"info","message":"🔐 Authentication: Clerk","timestamp":"2025-07-19 19:49:09:499"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-19 19:49:09:499"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-19 19:49:09:499"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-07-19 19:49:09:499"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-07-19 19:49:19:4919"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-07-19 19:49:24:4924"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-07-19 19:49:37:4937"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-07-19 19:50:04:504"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-07-19 19:50:15:5015"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-07-19 19:50:21:5021"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-20 16:50:29:5029"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-07-20 16:50:29:5029"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-20 16:50:29:5029"}
{"level":"info","message":"🔐 Authentication: Clerk","timestamp":"2025-07-20 16:50:29:5029"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-20 16:50:29:5029"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-20 16:50:29:5029"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-20 18:51:30:5130"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-20 18:51:30:5130"}
{"level":"info","message":"🔐 Authentication: Clerk","timestamp":"2025-07-20 18:51:30:5130"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-20 18:51:30:5130"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-20 18:51:30:5130"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-07-20 18:51:32:5132"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-20 19:20:17:2017"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-20 19:20:17:2017"}
{"level":"info","message":"🔐 Authentication: Clerk","timestamp":"2025-07-20 19:20:17:2017"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-20 19:20:17:2017"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-20 19:20:17:2017"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-20 19:27:30:2730"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-20 19:27:30:2730"}
{"level":"info","message":"🔐 Authentication: Clerk","timestamp":"2025-07-20 19:27:30:2730"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-20 19:27:30:2730"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-20 19:27:30:2730"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-20 19:33:35:3335"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-20 19:33:35:3335"}
{"level":"info","message":"🔐 Authentication: Clerk","timestamp":"2025-07-20 19:33:35:3335"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-20 19:33:35:3335"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-20 19:33:35:3335"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-07-20 19:35:45:3545"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-20 19:59:54:5954"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-20 19:59:54:5954"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-20 19:59:54:5954"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-20 19:59:54:5954"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-20 19:59:54:5954"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-20 20:08:10:810"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-20 20:08:10:810"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-20 20:08:10:810"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-20 20:08:10:810"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-20 20:08:10:810"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-20 20:15:11:1511"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-20 20:15:11:1511"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-20 20:15:11:1511"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-20 20:15:11:1511"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-20 20:15:11:1511"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-20 20:17:02:172"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-20 20:17:02:172"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-20 20:17:02:172"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-20 20:17:02:172"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-20 20:17:02:172"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-20 20:25:23:2523"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-20 20:25:23:2523"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-20 20:25:23:2523"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-20 20:25:23:2523"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-20 20:25:23:2523"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-07-20 20:27:38:2738"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-20 20:28:59:2859"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-20 20:28:59:2859"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-20 20:28:59:2859"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-20 20:28:59:2859"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-20 20:28:59:2859"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-20 20:34:31:3431"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-20 20:34:31:3431"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-20 20:34:31:3431"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-20 20:34:31:3431"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-20 20:34:31:3431"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-20 20:42:15:4215"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-20 20:42:15:4215"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-20 20:42:15:4215"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-20 20:42:15:4215"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-20 20:42:15:4215"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-21 11:37:42:3742"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-21 11:37:42:3742"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-21 11:37:42:3742"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-21 11:37:42:3742"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-21 11:37:42:3742"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-21 12:13:43:1343"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-21 12:13:43:1343"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-21 12:13:43:1343"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-21 12:13:43:1343"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-21 12:13:43:1343"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-21 13:01:59:159"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-21 13:01:59:159"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-21 13:01:59:159"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-21 13:01:59:159"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-21 13:01:59:159"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-21 13:02:03:23"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-21 13:02:03:23"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-21 13:02:03:23"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-21 13:02:03:23"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-21 13:02:03:23"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-21 13:02:19:219"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-21 13:02:19:219"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-21 13:02:19:219"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-21 13:02:19:219"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-21 13:02:19:219"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-21 13:03:11:311"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-21 13:03:11:311"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-21 13:03:11:311"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-21 13:03:11:311"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-21 13:03:11:311"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-21 15:23:42:2342"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-21 15:23:42:2342"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-21 15:23:42:2342"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-21 15:23:42:2342"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-21 15:23:42:2342"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-21 15:50:39:5039"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-21 15:50:39:5039"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-21 15:50:39:5039"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-21 15:50:39:5039"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-21 15:50:39:5039"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-21 15:53:05:535"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-21 15:53:05:535"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-21 15:53:05:535"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-21 15:53:05:535"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-21 15:53:05:535"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-21 20:51:16:5116"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-21 20:51:16:5116"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-21 20:51:16:5116"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-21 20:51:16:5116"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-21 20:51:16:5116"}
{"ip":"::1","level":"error","message":"Error occurred: Expected property name or '}' in JSON at position 1 (line 1 column 2)","method":"POST","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at parse (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\body-parser\\lib\\types\\json.js:92:19)\n    at C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:507:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","statusCode":400,"timestamp":"2025-07-21 20:57:05:575","url":"/api/auth/login","userAgent":"curl/8.13.0"}
{"ip":"::1","level":"error","message":"Error occurred: Expected property name or '}' in JSON at position 1 (line 1 column 2)","method":"POST","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at parse (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\body-parser\\lib\\types\\json.js:92:19)\n    at C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:507:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","statusCode":400,"timestamp":"2025-07-21 20:57:42:5742","url":"/api/auth/login","userAgent":"curl/8.13.0"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-21 20:58:52:5852"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-21 20:58:52:5852"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-21 20:58:52:5852"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-21 20:58:52:5852"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-21 20:58:52:5852"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-21 20:59:12:5912"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-21 20:59:12:5912"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-21 20:59:12:5912"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-21 20:59:12:5912"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-21 20:59:12:5912"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-21 21:02:24:224"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-21 21:02:24:224"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-21 21:02:24:224"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-21 21:02:24:224"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-21 21:02:24:224"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:02:25:225"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:02:26:226","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:02:26:226","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:02:35:235","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:02:35:235","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:02:37:237"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:02:37:237","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:02:37:237","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:02:43:243","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:02:43:243","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:02:49:249"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:02:49:249","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:02:49:249","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:02:55:255","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:02:55:255","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:02:57:257"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:02:57:257","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:02:57:257","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:03:02:32"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:03:02:32","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:03:02:32","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:03:25:325","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:03:25:325","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:04:10:410","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:04:10:410","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-21 21:05:13:513"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-21 21:05:13:513"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-21 21:05:13:513"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-21 21:05:13:513"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-21 21:05:13:513"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:05:42:542"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:05:42:542","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:05:42:542","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:05:55:555","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:05:55:555","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-21 21:06:35:635"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-21 21:06:35:635"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-21 21:06:35:635"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-21 21:06:35:635"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-21 21:06:35:635"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:06:38:638"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:06:38:638","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:06:38:638","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-21 21:06:48:648"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-21 21:06:48:648"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-21 21:06:48:648"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-21 21:06:48:648"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-21 21:06:48:648"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:08:29:829","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:08:29:829","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
