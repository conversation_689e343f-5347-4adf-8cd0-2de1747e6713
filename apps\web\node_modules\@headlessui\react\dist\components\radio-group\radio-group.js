"use client";import{useFocusRing as Y}from"@react-aria/focus";import{useHover as Z}from"@react-aria/interactions";import G,{createContext as ee,useCallback as ye,useContext as te,useMemo as x,useReducer as be,useRef as B}from"react";import{useByComparator as ge}from'../../hooks/use-by-comparator.js';import{useControllable as Oe}from'../../hooks/use-controllable.js';import{useDefaultValue as Pe}from'../../hooks/use-default-value.js';import{useEvent as H}from'../../hooks/use-event.js';import{useId as V}from'../../hooks/use-id.js';import{useIsoMorphicEffect as oe}from'../../hooks/use-iso-morphic-effect.js';import{useLatestValue as re}from'../../hooks/use-latest-value.js';import{useSyncRefs as K}from'../../hooks/use-sync-refs.js';import{useDisabled as ne}from'../../internal/disabled.js';import{FormFields as ve}from'../../internal/form-fields.js';import{useProvidedId as De}from'../../internal/id.js';import{isDisabledReactIssue7711 as ie}from'../../utils/bugs.js';import{Focus as N,FocusResult as ae,focusIn as pe,sortByDomNode as Ae}from'../../utils/focus-management.js';import{attemptSubmit as _e}from'../../utils/form.js';import{match as Ee}from'../../utils/match.js';import{getOwnerDocument as Ge}from'../../utils/owner.js';import{forwardRefWithAs as $,mergeProps as le,useRender as j}from'../../utils/render.js';import{Description as xe,useDescribedBy as Ce,useDescriptions as se}from'../description/description.js';import{Keys as F}from'../keyboard.js';import{Label as he,useLabelledBy as Le,useLabels as de}from'../label/label.js';var ke=(e=>(e[e.RegisterOption=0]="RegisterOption",e[e.UnregisterOption=1]="UnregisterOption",e))(ke||{});let Fe={[0](o,t){let e=[...o.options,{id:t.id,element:t.element,propsRef:t.propsRef}];return{...o,options:Ae(e,a=>a.element.current)}},[1](o,t){let e=o.options.slice(),a=o.options.findIndex(g=>g.id===t.id);return a===-1?o:(e.splice(a,1),{...o,options:e})}},J=ee(null);J.displayName="RadioGroupDataContext";function X(o){let t=te(J);if(t===null){let e=new Error(`<${o} /> is missing a parent <RadioGroup /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(e,X),e}return t}let z=ee(null);z.displayName="RadioGroupActionsContext";function q(o){let t=te(z);if(t===null){let e=new Error(`<${o} /> is missing a parent <RadioGroup /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(e,q),e}return t}function Ie(o,t){return Ee(t.type,Fe,o,t)}let Ue="div";function Me(o,t){let e=V(),a=ne(),{id:g=`headlessui-radiogroup-${e}`,value:R,form:O,name:n,onChange:f,by:c,disabled:p=a||!1,defaultValue:I,...m}=o,T=ge(c),[P,C]=be(Ie,{options:[]}),i=P.options,[U,h]=de(),[v,L]=se(),D=B(null),M=K(D,t),l=Pe(I),[s,A]=Oe(R,f,l),S=x(()=>i.find(r=>!r.propsRef.current.disabled),[i]),y=x(()=>i.some(r=>T(r.propsRef.current.value,s)),[i,s]),d=H(r=>{var u;if(p||T(r,s))return!1;let k=(u=i.find(w=>T(w.propsRef.current.value,r)))==null?void 0:u.propsRef.current;return k!=null&&k.disabled?!1:(A==null||A(r),!0)}),_=H(r=>{let k=D.current;if(!k)return;let u=Ge(k),w=i.filter(b=>b.propsRef.current.disabled===!1).map(b=>b.element.current);switch(r.key){case F.Enter:_e(r.currentTarget);break;case F.ArrowLeft:case F.ArrowUp:if(r.preventDefault(),r.stopPropagation(),pe(w,N.Previous|N.WrapAround)===ae.Success){let E=i.find(W=>W.element.current===(u==null?void 0:u.activeElement));E&&d(E.propsRef.current.value)}break;case F.ArrowRight:case F.ArrowDown:if(r.preventDefault(),r.stopPropagation(),pe(w,N.Next|N.WrapAround)===ae.Success){let E=i.find(W=>W.element.current===(u==null?void 0:u.activeElement));E&&d(E.propsRef.current.value)}break;case F.Space:{r.preventDefault(),r.stopPropagation();let b=i.find(E=>E.element.current===(u==null?void 0:u.activeElement));b&&d(b.propsRef.current.value)}break}}),Q=H(r=>(C({type:0,...r}),()=>C({type:1,id:r.id}))),ue=x(()=>({value:s,firstOption:S,containsCheckedOption:y,disabled:p,compare:T,...P}),[s,S,y,p,T,P]),ce=x(()=>({registerOption:Q,change:d}),[Q,d]),fe={ref:M,id:g,role:"radiogroup","aria-labelledby":U,"aria-describedby":v,onKeyDown:_},Te=x(()=>({value:s}),[s]),Re=ye(()=>{if(l!==void 0)return d(l)},[d,l]),me=j();return G.createElement(L,{name:"RadioGroup.Description"},G.createElement(h,{name:"RadioGroup.Label"},G.createElement(z.Provider,{value:ce},G.createElement(J.Provider,{value:ue},n!=null&&G.createElement(ve,{disabled:p,data:{[n]:s||"on"},overrides:{type:"radio",checked:s!=null},form:O,onReset:Re}),me({ourProps:fe,theirProps:m,slot:Te,defaultTag:Ue,name:"RadioGroup"})))))}let Se="div";function He(o,t){var y;let e=X("RadioGroup.Option"),a=q("RadioGroup.Option"),g=V(),{id:R=`headlessui-radiogroup-option-${g}`,value:O,disabled:n=e.disabled||!1,autoFocus:f=!1,...c}=o,p=B(null),I=K(p,t),[m,T]=de(),[P,C]=se(),i=re({value:O,disabled:n});oe(()=>a.registerOption({id:R,element:p,propsRef:i}),[R,a,p,i]);let U=H(d=>{var _;if(ie(d.currentTarget))return d.preventDefault();a.change(O)&&((_=p.current)==null||_.focus())}),h=((y=e.firstOption)==null?void 0:y.id)===R,{isFocusVisible:v,focusProps:L}=Y({autoFocus:f}),{isHovered:D,hoverProps:M}=Z({isDisabled:n}),l=e.compare(e.value,O),s=le({ref:I,id:R,role:"radio","aria-checked":l?"true":"false","aria-labelledby":m,"aria-describedby":P,"aria-disabled":n?!0:void 0,tabIndex:(()=>n?-1:l||!e.containsCheckedOption&&h?0:-1)(),onClick:n?void 0:U,autoFocus:f},L,M),A=x(()=>({checked:l,disabled:n,active:v,hover:D,focus:v,autofocus:f}),[l,n,D,v,f]),S=j();return G.createElement(C,{name:"RadioGroup.Description"},G.createElement(T,{name:"RadioGroup.Label"},S({ourProps:s,theirProps:c,slot:A,defaultTag:Se,name:"RadioGroup.Option"})))}let we="span";function Ne(o,t){var y;let e=X("Radio"),a=q("Radio"),g=V(),R=De(),O=ne(),{id:n=R||`headlessui-radio-${g}`,value:f,disabled:c=e.disabled||O||!1,autoFocus:p=!1,...I}=o,m=B(null),T=K(m,t),P=Le(),C=Ce(),i=re({value:f,disabled:c});oe(()=>a.registerOption({id:n,element:m,propsRef:i}),[n,a,m,i]);let U=H(d=>{var _;if(ie(d.currentTarget))return d.preventDefault();a.change(f)&&((_=m.current)==null||_.focus())}),{isFocusVisible:h,focusProps:v}=Y({autoFocus:p}),{isHovered:L,hoverProps:D}=Z({isDisabled:c}),M=((y=e.firstOption)==null?void 0:y.id)===n,l=e.compare(e.value,f),s=le({ref:T,id:n,role:"radio","aria-checked":l?"true":"false","aria-labelledby":P,"aria-describedby":C,"aria-disabled":c?!0:void 0,tabIndex:(()=>c?-1:l||!e.containsCheckedOption&&M?0:-1)(),autoFocus:p,onClick:c?void 0:U},v,D),A=x(()=>({checked:l,disabled:c,hover:L,focus:h,autofocus:p}),[l,c,L,h,p]);return j()({ourProps:s,theirProps:I,slot:A,defaultTag:we,name:"Radio"})}let We=$(Me),Be=$(He),Ve=$(Ne),Ke=he,$e=xe,Rt=Object.assign(We,{Option:Be,Radio:Ve,Label:Ke,Description:$e});export{Ve as Radio,Rt as RadioGroup,$e as RadioGroupDescription,Ke as RadioGroupLabel,Be as RadioGroupOption};
