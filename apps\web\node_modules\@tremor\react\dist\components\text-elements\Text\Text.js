import{colorPalette as t}from"../../../lib/theme.js";import{tremorTwMerge as e}from"../../../lib/tremorTwMerge.js";import{getColorClassNames as r}from"../../../lib/utils.js";import o from"react";const m=o.forwardRef(((m,a)=>{const{color:l,className:s,children:c}=m;return o.createElement("p",{ref:a,className:e("text-tremor-default",l?r(l,t.text).textColor:e("text-tremor-content","dark:text-dark-tremor-content"),s)},c)}));m.displayName="Text";export{m as default};
