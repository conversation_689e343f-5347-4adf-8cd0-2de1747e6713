'use client';
import{__rest as a}from"tslib";import{BaseColors as e}from"../../../lib/constants.js";import{themeColorRange as t,colorPalette as o}from"../../../lib/theme.js";import{tremorTwMerge as r}from"../../../lib/tremorTwMerge.js";import{getColorClassNames as i}from"../../../lib/utils.js";import m from"react";import{ResponsiveContainer as n,BarChart as l,YAxis as s,XAxis as c,Bar as u}from"recharts";import{constructCategoryColors as d,getYAxisDomain as f}from"../../chart-elements/common/utils.js";import p from"../../chart-elements/common/NoData.js";const h=m.forwardRef(((h,g)=>{const{data:x=[],categories:b=[],index:v,colors:j=t,stack:k=!1,relative:D=!1,animationDuration:E=900,showAnimation:N=!1,noDataText:w,autoMinValue:y=!1,minValue:V,maxValue:A,className:T}=h,M=a(h,["data","categories","index","colors","stack","relative","animationDuration","showAnimation","noDataText","autoMinValue","minValue","maxValue","className"]),C=d(b,j),K=f(y,V,A);return m.createElement("div",Object.assign({ref:g,className:r("w-28 h-12",T)},M),m.createElement(n,{className:"h-full w-full"},(null==x?void 0:x.length)?m.createElement(l,{data:x,stackOffset:D?"expand":"none",margin:{top:0,left:-1.5,right:-1.5,bottom:0}},m.createElement(s,{hide:!0,domain:K}),m.createElement(c,{hide:!0,dataKey:v}),b.map((a=>{var t;return m.createElement(u,{className:r(i(null!==(t=C.get(a))&&void 0!==t?t:e.Gray,o.background).fillColor),key:a,name:a,type:"linear",stackId:k||D?"a":void 0,dataKey:a,fill:"",isAnimationActive:N,animationDuration:E})}))):m.createElement(p,{noDataText:w})))}));h.displayName="SparkBarChart";export{h as default};
