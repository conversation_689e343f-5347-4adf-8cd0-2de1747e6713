import e from"react";import{BaseColors as r}from"../../../lib/constants.js";import{colorPalette as t}from"../../../lib/theme.js";import{tremorTwMerge as a}from"../../../lib/tremorTwMerge.js";import{getColorClassNames as o,defaultValueFormatter as d}from"../../../lib/utils.js";const m=({children:r})=>e.createElement("div",{className:a("rounded-tremor-default text-tremor-default border","bg-tremor-background shadow-tremor-dropdown border-tremor-border","dark:bg-dark-tremor-background dark:shadow-dark-tremor-dropdown dark:border-dark-tremor-border")},r),n=({value:r,name:t})=>e.createElement("div",{className:"flex items-center justify-between space-x-8"},e.createElement("div",{className:"flex items-center space-x-2"},e.createElement("p",{className:a("text-right whitespace-nowrap","text-tremor-content","dark:text-dark-tremor-content")},t)),e.createElement("p",{className:a("font-medium tabular-nums text-right whitespace-nowrap","text-tremor-content-emphasis","dark:text-dark-tremor-content-emphasis")},r)),l=({label:l,active:s,payload:c,valueFormatter:i,axis:p,category:u,categoryColors:b})=>{var k,x;return s&&c?e.createElement(m,null,e.createElement("div",{className:a("flex items-center space-x-2 border-b px-4 py-2","border-tremor-border","dark:border-dark-tremor-border")},e.createElement("span",{className:a("shrink-0 rounded-tremor-full border-2 h-3 w-3","border-tremor-background shadow-tremor-card","dark:border-dark-tremor-background dark:shadow-dark-tremor-card",o(u&&null!==(x=b.get(null===(k=null==c?void 0:c[0])||void 0===k?void 0:k.payload[u]))&&void 0!==x?x:r.Blue,t.background).bgColor)}),e.createElement("p",{className:a("font-medium","text-tremor-content-emphasis","dark:text-dark-tremor-content-emphasis")},l)),e.createElement("div",{className:a("px-4 py-2 space-y-1")},c.map((({value:r,name:t},a)=>{var o,m;const l=null!==(o=Object.keys(p).find((e=>p[e]===t)))&&void 0!==o?o:"",s=null!==(m=i[l])&&void 0!==m?m:d;return e.createElement(n,{key:`id-${a}`,value:i&&s?s(r):`${r}`,name:t})})))):null};export{m as ChartTooltipFrame,n as ChartTooltipRow,l as default};
