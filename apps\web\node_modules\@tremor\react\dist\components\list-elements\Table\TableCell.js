import{__rest as e}from"tslib";import r from"react";import{tremorTwMerge as t}from"../../../lib/tremorTwMerge.js";import{makeClassName as l}from"../../../lib/utils.js";const a=l("TableCell"),m=r.forwardRef(((l,m)=>{const{children:s,className:o}=l,i=e(l,["children","className"]);return r.createElement(r.Fragment,null,r.createElement("td",Object.assign({ref:m,className:t(a("root"),"align-middle whitespace-nowrap text-left p-4",o)},i),s))}));m.displayName="TableCell";export{m as default};
