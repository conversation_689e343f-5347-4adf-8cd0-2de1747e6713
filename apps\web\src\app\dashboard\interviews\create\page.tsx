'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'

export default function CreateInterviewRedirect() {
  const router = useRouter()

  useEffect(() => {
    // Redirect to the new interview page
    router.replace('/dashboard/interviews/new')
  }, [router])

  return (
    <div className="min-h-screen bg-background flex items-center justify-center">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
    </div>
  )
}
