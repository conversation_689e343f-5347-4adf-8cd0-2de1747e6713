// --- START api/database/schema.ts --- //
// Database schema for AI-InterviewSpark using Drizzle ORM
// Defines all tables and relationships for the application

import { pgTable, text, timestamp, integer, boolean, jsonb, uuid, decimal, serial } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { z } from 'zod';

// Users table
export const users = pgTable('users', {
  id: uuid('id').primaryKey().defaultRandom(),
  email: text('email').notNull().unique(),
  password: text('password').notNull(),
  firstName: text('first_name').notNull(),
  lastName: text('last_name').notNull(),
  role: text('role', { enum: ['job_seeker', 'expert', 'admin'] }).notNull().default('job_seeker'),
  avatar: text('avatar'),
  bio: text('bio'),
  location: text('location'),
  timezone: text('timezone'),
  language: text('language').notNull().default('en'),
  accessibility: jsonb('accessibility').$type<{
    highContrast: boolean;
    screenReader: boolean;
    captions: boolean;
  }>().default({ highContrast: false, screenReader: false, captions: true }),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Interview sessions table
export const interviewSessions = pgTable('interview_sessions', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  type: text('type', { enum: ['video', 'audio', 'text', 'peer', 'expert'] }).notNull(),
  status: text('status', { enum: ['scheduled', 'in_progress', 'completed', 'cancelled'] }).notNull().default('scheduled'),
  title: text('title').notNull(),
  description: text('description'),
  jobTitle: text('job_title'),
  company: text('company'),
  duration: integer('duration').notNull(), // minutes
  difficulty: text('difficulty', { enum: ['beginner', 'intermediate', 'advanced'] }).notNull(),
  topics: text('topics').array().notNull().default([]),
  scheduledAt: timestamp('scheduled_at'),
  startedAt: timestamp('started_at'),
  completedAt: timestamp('completed_at'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Questions table
export const questions = pgTable('questions', {
  id: uuid('id').primaryKey().defaultRandom(),
  sessionId: uuid('session_id').notNull().references(() => interviewSessions.id, { onDelete: 'cascade' }),
  type: text('type', { enum: ['behavioral', 'technical', 'situational', 'strengths', 'weaknesses'] }).notNull(),
  text: text('text').notNull(),
  category: text('category').notNull(),
  difficulty: text('difficulty', { enum: ['beginner', 'intermediate', 'advanced'] }).notNull(),
  expectedKeywords: text('expected_keywords').array(),
  timeLimit: integer('time_limit'), // seconds
  order: integer('order').notNull(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
});

// Answers table
export const answers = pgTable('answers', {
  id: uuid('id').primaryKey().defaultRandom(),
  questionId: uuid('question_id').notNull().references(() => questions.id, { onDelete: 'cascade' }),
  sessionId: uuid('session_id').notNull().references(() => interviewSessions.id, { onDelete: 'cascade' }),
  userId: uuid('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  text: text('text'),
  audioUrl: text('audio_url'),
  videoUrl: text('video_url'),
  duration: integer('duration'), // seconds
  createdAt: timestamp('created_at').notNull().defaultNow(),
});

// Feedback table
export const feedback = pgTable('feedback', {
  id: uuid('id').primaryKey().defaultRandom(),
  answerId: uuid('answer_id').notNull().references(() => answers.id, { onDelete: 'cascade' }),
  sessionId: uuid('session_id').notNull().references(() => interviewSessions.id, { onDelete: 'cascade' }),
  userId: uuid('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  category: text('category', { 
    enum: ['content', 'delivery', 'confidence', 'clarity', 'relevance', 'emotional_state'] 
  }).notNull(),
  score: decimal('score', { precision: 3, scale: 1 }).notNull(), // 0.0 to 10.0
  feedback: text('feedback').notNull(),
  suggestions: jsonb('suggestions').$type<string[]>().notNull().default([]),
  emotionalAnalysis: jsonb('emotional_analysis').$type<Array<{
    emotion: string;
    confidence: number;
    timestamp: number;
    source: string;
  }>>(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
});

// Resumes table
export const resumes = pgTable('resumes', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  fileName: text('file_name').notNull(),
  fileUrl: text('file_url').notNull(),
  fileSize: integer('file_size').notNull(),
  uploadDate: timestamp('upload_date').notNull().defaultNow(),
  parsedData: jsonb('parsed_data').$type<{
    skills: string[];
    experience: Array<{
      title: string;
      company: string;
      duration: string;
      description: string;
    }>;
    education: Array<{
      degree: string;
      institution: string;
      year: number;
    }>;
  }>(),
  atsScore: decimal('ats_score', { precision: 5, scale: 2 }), // 0.00 to 100.00
  keywords: jsonb('keywords').$type<string[]>(),
});

// Expert profiles table
export const expertProfiles = pgTable('expert_profiles', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  specialties: jsonb('specialties').$type<string[]>().notNull().default([]),
  experience: integer('experience').notNull(), // years
  hourlyRate: decimal('hourly_rate', { precision: 8, scale: 2 }).notNull(),
  availability: jsonb('availability').$type<Array<{
    day: number;
    startTime: string;
    endTime: string;
  }>>().notNull().default([]),
  rating: decimal('rating', { precision: 3, scale: 2 }), // 0.00 to 5.00
  totalSessions: integer('total_sessions').notNull().default(0),
  isVerified: boolean('is_verified').notNull().default(false),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Performance metrics table
export const performanceMetrics = pgTable('performance_metrics', {
  id: uuid('id').primaryKey().defaultRandom(),
  sessionId: uuid('session_id').notNull().references(() => interviewSessions.id, { onDelete: 'cascade' }),
  userId: uuid('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  overallScore: decimal('overall_score', { precision: 5, scale: 2 }).notNull(), // 0.00 to 100.00
  categoryScores: jsonb('category_scores').$type<Record<string, number>>().notNull(),
  emotionalTrends: jsonb('emotional_trends').$type<Array<{
    emotion: string;
    averageConfidence: number;
    frequency: number;
  }>>().notNull().default([]),
  improvementAreas: jsonb('improvement_areas').$type<string[]>().notNull().default([]),
  strengths: jsonb('strengths').$type<string[]>().notNull().default([]),
  sessionDuration: integer('session_duration').notNull(), // minutes
  questionsAnswered: integer('questions_answered').notNull().default(0),
  createdAt: timestamp('created_at').notNull().defaultNow(),
});

// Peer sessions table (for peer-to-peer interviews)
export const peerSessions = pgTable('peer_sessions', {
  id: uuid('id').primaryKey().defaultRandom(),
  sessionId: uuid('session_id').notNull().references(() => interviewSessions.id, { onDelete: 'cascade' }),
  peerUserId: uuid('peer_user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  status: text('status', { enum: ['pending', 'accepted', 'declined', 'completed'] }).notNull().default('pending'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Expert sessions table (for expert coaching)
export const expertSessions = pgTable('expert_sessions', {
  id: uuid('id').primaryKey().defaultRandom(),
  sessionId: uuid('session_id').notNull().references(() => interviewSessions.id, { onDelete: 'cascade' }),
  expertId: uuid('expert_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  status: text('status', { enum: ['scheduled', 'confirmed', 'completed', 'cancelled'] }).notNull().default('scheduled'),
  notes: text('notes'),
  rating: integer('rating'), // 1-5 stars
  review: text('review'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Notifications table
export const notifications = pgTable('notifications', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  type: text('type', { 
    enum: ['session_reminder', 'feedback_ready', 'peer_request', 'expert_confirmation', 'system'] 
  }).notNull(),
  title: text('title').notNull(),
  message: text('message').notNull(),
  data: jsonb('data').$type<Record<string, any>>(),
  isRead: boolean('is_read').notNull().default(false),
  createdAt: timestamp('created_at').notNull().defaultNow(),
});

// Define relationships
export const usersRelations = relations(users, ({ many }) => ({
  interviewSessions: many(interviewSessions),
  answers: many(answers),
  feedback: many(feedback),
  resumes: many(resumes),
  expertProfile: many(expertProfiles),
  performanceMetrics: many(performanceMetrics),
  notifications: many(notifications),
}));

export const interviewSessionsRelations = relations(interviewSessions, ({ one, many }) => ({
  user: one(users, {
    fields: [interviewSessions.userId],
    references: [users.id],
  }),
  questions: many(questions),
  answers: many(answers),
  feedback: many(feedback),
  performanceMetrics: many(performanceMetrics),
  peerSession: many(peerSessions),
  expertSession: many(expertSessions),
}));

export const questionsRelations = relations(questions, ({ one, many }) => ({
  session: one(interviewSessions, {
    fields: [questions.sessionId],
    references: [interviewSessions.id],
  }),
  answers: many(answers),
}));

export const answersRelations = relations(answers, ({ one, many }) => ({
  question: one(questions, {
    fields: [answers.questionId],
    references: [questions.id],
  }),
  session: one(interviewSessions, {
    fields: [answers.sessionId],
    references: [interviewSessions.id],
  }),
  user: one(users, {
    fields: [answers.userId],
    references: [users.id],
  }),
  feedback: many(feedback),
}));

export const feedbackRelations = relations(feedback, ({ one }) => ({
  answer: one(answers, {
    fields: [feedback.answerId],
    references: [answers.id],
  }),
  session: one(interviewSessions, {
    fields: [feedback.sessionId],
    references: [interviewSessions.id],
  }),
  user: one(users, {
    fields: [feedback.userId],
    references: [users.id],
  }),
}));

export const performanceMetricsRelations = relations(performanceMetrics, ({ one }) => ({
  session: one(interviewSessions, {
    fields: [performanceMetrics.sessionId],
    references: [interviewSessions.id],
  }),
  user: one(users, {
    fields: [performanceMetrics.userId],
    references: [users.id],
  }),
}));

// Create Zod schemas for validation
export const insertUserSchema = z.object({
  email: z.string().email(),
  firstName: z.string().min(1),
  lastName: z.string().min(1),
  role: z.enum(['job_seeker', 'expert', 'admin']).default('job_seeker'),
  avatar: z.string().optional(),
  bio: z.string().optional(),
  location: z.string().optional(),
  timezone: z.string().optional(),
  language: z.string().default('en'),
  accessibility: z.object({
    highContrast: z.boolean().default(false),
    screenReader: z.boolean().default(false),
    captions: z.boolean().default(true),
  }).default({ highContrast: false, screenReader: false, captions: true }),
});

export const selectUserSchema = z.object({
  id: z.string().uuid(),
  email: z.string().email(),
  firstName: z.string(),
  lastName: z.string(),
  role: z.enum(['job_seeker', 'expert', 'admin']),
  avatar: z.string().nullable(),
  bio: z.string().nullable(),
  location: z.string().nullable(),
  timezone: z.string().nullable(),
  language: z.string(),
  accessibility: z.object({
    highContrast: z.boolean(),
    screenReader: z.boolean(),
    captions: z.boolean(),
  }),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const insertInterviewSessionSchema = z.object({
  userId: z.string().uuid(),
  type: z.enum(['video', 'audio', 'text', 'peer', 'expert']),
  status: z.enum(['scheduled', 'in_progress', 'completed', 'cancelled']).default('scheduled'),
  title: z.string().min(1),
  description: z.string().optional(),
  jobTitle: z.string().optional(),
  company: z.string().optional(),
  duration: z.number().positive(),
  difficulty: z.enum(['beginner', 'intermediate', 'advanced']),
  topics: z.array(z.string()).default([]),
  scheduledAt: z.date().optional(),
});

export const selectInterviewSessionSchema = z.object({
  id: z.string().uuid(),
  userId: z.string().uuid(),
  type: z.enum(['video', 'audio', 'text', 'peer', 'expert']),
  status: z.enum(['scheduled', 'in_progress', 'completed', 'cancelled']),
  title: z.string(),
  description: z.string().nullable(),
  jobTitle: z.string().nullable(),
  company: z.string().nullable(),
  duration: z.number(),
  difficulty: z.enum(['beginner', 'intermediate', 'advanced']),
  topics: z.array(z.string()),
  scheduledAt: z.date().nullable(),
  startedAt: z.date().nullable(),
  completedAt: z.date().nullable(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const insertQuestionSchema = z.object({
  sessionId: z.string().uuid(),
  type: z.enum(['behavioral', 'technical', 'situational', 'strengths', 'weaknesses']),
  text: z.string().min(1),
  category: z.string(),
  difficulty: z.enum(['beginner', 'intermediate', 'advanced']),
  expectedKeywords: z.array(z.string()).optional(),
  timeLimit: z.number().positive().optional(),
  order: z.number().positive(),
});

export const selectQuestionSchema = z.object({
  id: z.string().uuid(),
  sessionId: z.string().uuid(),
  type: z.enum(['behavioral', 'technical', 'situational', 'strengths', 'weaknesses']),
  text: z.string(),
  category: z.string(),
  difficulty: z.enum(['beginner', 'intermediate', 'advanced']),
  expectedKeywords: z.array(z.string()).nullable(),
  timeLimit: z.number().nullable(),
  order: z.number(),
  createdAt: z.date(),
});

export const insertAnswerSchema = z.object({
  questionId: z.string().uuid(),
  sessionId: z.string().uuid(),
  userId: z.string().uuid(),
  text: z.string().optional(),
  audioUrl: z.string().url().optional(),
  videoUrl: z.string().url().optional(),
  duration: z.number().positive().optional(),
});

export const selectAnswerSchema = z.object({
  id: z.string().uuid(),
  questionId: z.string().uuid(),
  sessionId: z.string().uuid(),
  userId: z.string().uuid(),
  text: z.string().nullable(),
  audioUrl: z.string().nullable(),
  videoUrl: z.string().nullable(),
  duration: z.number().nullable(),
  createdAt: z.date(),
});

export const insertFeedbackSchema = z.object({
  answerId: z.string().uuid(),
  sessionId: z.string().uuid(),
  userId: z.string().uuid(),
  category: z.enum(['content', 'delivery', 'confidence', 'clarity', 'relevance', 'emotional_state']),
  score: z.number().min(0).max(10),
  feedback: z.string().min(1),
  suggestions: z.array(z.string()).default([]),
  emotionalAnalysis: z.array(z.object({
    emotion: z.string(),
    confidence: z.number(),
    timestamp: z.number(),
    source: z.string(),
  })).optional(),
});

export const selectFeedbackSchema = z.object({
  id: z.string().uuid(),
  answerId: z.string().uuid(),
  sessionId: z.string().uuid(),
  userId: z.string().uuid(),
  category: z.enum(['content', 'delivery', 'confidence', 'clarity', 'relevance', 'emotional_state']),
  score: z.number(),
  feedback: z.string(),
  suggestions: z.array(z.string()),
  emotionalAnalysis: z.array(z.object({
    emotion: z.string(),
    confidence: z.number(),
    timestamp: z.number(),
    source: z.string(),
  })).nullable(),
  createdAt: z.date(),
});

export const insertResumeSchema = z.object({
  userId: z.string().uuid(),
  fileName: z.string().min(1),
  fileUrl: z.string().url(),
  fileSize: z.number().positive(),
  parsedData: z.object({
    skills: z.array(z.string()),
    experience: z.array(z.object({
      title: z.string(),
      company: z.string(),
      duration: z.string(),
      description: z.string(),
    })),
    education: z.array(z.object({
      degree: z.string(),
      institution: z.string(),
      year: z.number(),
    })),
  }).optional(),
  atsScore: z.number().min(0).max(100).optional(),
  keywords: z.array(z.string()).optional(),
});

export const selectResumeSchema = z.object({
  id: z.string().uuid(),
  userId: z.string().uuid(),
  fileName: z.string(),
  fileUrl: z.string(),
  fileSize: z.number(),
  uploadDate: z.date(),
  parsedData: z.object({
    skills: z.array(z.string()),
    experience: z.array(z.object({
      title: z.string(),
      company: z.string(),
      duration: z.string(),
      description: z.string(),
    })),
    education: z.array(z.object({
      degree: z.string(),
      institution: z.string(),
      year: z.number(),
    })),
  }).nullable(),
  atsScore: z.number().nullable(),
  keywords: z.array(z.string()).nullable(),
});

export const insertExpertProfileSchema = z.object({
  userId: z.string().uuid(),
  specialties: z.array(z.string()).default([]),
  experience: z.number().min(0),
  hourlyRate: z.string(),
  availability: z.array(z.object({
    day: z.number().min(0).max(6),
    startTime: z.string(),
    endTime: z.string(),
  })).default([]),
  rating: z.string().optional(),
  totalSessions: z.number().default(0),
  isVerified: z.boolean().default(false),
});

export const selectExpertProfileSchema = z.object({
  id: z.string().uuid(),
  userId: z.string().uuid(),
  specialties: z.array(z.string()),
  experience: z.number(),
  hourlyRate: z.string(),
  availability: z.array(z.object({
    day: z.number(),
    startTime: z.string(),
    endTime: z.string(),
  })),
  rating: z.string().nullable(),
  totalSessions: z.number(),
  isVerified: z.boolean(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const insertPerformanceMetricsSchema = z.object({
  sessionId: z.string().uuid(),
  userId: z.string().uuid(),
  overallScore: z.number().min(0).max(100),
  categoryScores: z.record(z.string(), z.number()),
  emotionalTrends: z.array(z.object({
    emotion: z.string(),
    averageConfidence: z.number(),
    frequency: z.number(),
  })).default([]),
  improvementAreas: z.array(z.string()).default([]),
  strengths: z.array(z.string()).default([]),
  sessionDuration: z.number().positive(),
  questionsAnswered: z.number().default(0),
});

export const selectPerformanceMetricsSchema = z.object({
  id: z.string().uuid(),
  sessionId: z.string().uuid(),
  userId: z.string().uuid(),
  overallScore: z.number(),
  categoryScores: z.record(z.string(), z.number()),
  emotionalTrends: z.array(z.object({
    emotion: z.string(),
    averageConfidence: z.number(),
    frequency: z.number(),
  })),
  improvementAreas: z.array(z.string()),
  strengths: z.array(z.string()),
  sessionDuration: z.number(),
  questionsAnswered: z.number(),
  createdAt: z.date(),
});

// Export types
export type User = z.infer<typeof selectUserSchema>;
export type NewUser = z.infer<typeof insertUserSchema>;

export type InterviewSession = z.infer<typeof selectInterviewSessionSchema>;
export type NewInterviewSession = z.infer<typeof insertInterviewSessionSchema>;

export type Question = z.infer<typeof selectQuestionSchema>;
export type NewQuestion = z.infer<typeof insertQuestionSchema>;

export type Answer = z.infer<typeof selectAnswerSchema>;
export type NewAnswer = z.infer<typeof insertAnswerSchema>;

export type Feedback = z.infer<typeof selectFeedbackSchema>;
export type NewFeedback = z.infer<typeof insertFeedbackSchema>;

export type Resume = z.infer<typeof selectResumeSchema>;
export type NewResume = z.infer<typeof insertResumeSchema>;

export type ExpertProfile = z.infer<typeof selectExpertProfileSchema>;
export type NewExpertProfile = z.infer<typeof insertExpertProfileSchema>;

export type PerformanceMetrics = z.infer<typeof selectPerformanceMetricsSchema>;
export type NewPerformanceMetrics = z.infer<typeof insertPerformanceMetricsSchema>; 