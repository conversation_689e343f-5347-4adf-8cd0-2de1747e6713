"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("../../lib/tremorTwMerge.cjs");function r(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var t=r(require("react"));const o=e=>["string","number"].includes(typeof e)?e:e instanceof Array?e.map(o).join(""):"object"==typeof e&&e?o(e.props.children):void 0;exports.constructValueToNameMapping=function(e){const r=new Map;return t.default.Children.map(e,(e=>{var t;r.set(e.props.value,null!==(t=o(e))&&void 0!==t?t:e.props.value)})),r},exports.getFilteredOptions=function(e,r){return t.default.Children.map(r,(r=>{var t;if((null!==(t=o(r))&&void 0!==t?t:r.props.value).toLowerCase().includes(e.toLowerCase()))return r}))},exports.getNodeText=o,exports.getSelectButtonColors=(r,t,o=!1)=>e.tremorTwMerge(t?"bg-tremor-background-subtle dark:bg-dark-tremor-background-subtle":"bg-tremor-background dark:bg-dark-tremor-background",!t&&"hover:bg-tremor-background-muted dark:hover:bg-dark-tremor-background-muted",r?"text-tremor-content-emphasis dark:text-dark-tremor-content-emphasis":"text-tremor-content dark:text-dark-tremor-content",t&&"text-tremor-content-subtle dark:text-dark-tremor-content-subtle",o&&"text-red-500 placeholder:text-red-500 dark:text-red-500 dark:placeholder:text-red-500",o?"border-red-500 dark:border-red-500":"border-tremor-border dark:border-dark-tremor-border"),exports.hasValue=function(e){return null!=e&&""!==e};
