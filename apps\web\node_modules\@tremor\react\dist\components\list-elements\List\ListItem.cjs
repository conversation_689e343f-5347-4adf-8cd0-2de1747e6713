"use strict";var e=require("tslib"),t=require("react"),r=require("../../../lib/tremorTwMerge.cjs"),l=require("../../../lib/utils.cjs");function a(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var s=a(t);const i=l.makeClassName("ListItem"),u=s.default.forwardRef(((t,l)=>{const{children:a,className:u}=t,c=e.__rest(t,["children","className"]);return s.default.createElement(s.default.Fragment,null,s.default.createElement("li",Object.assign({ref:l,className:r.tremorTwMerge(i("root"),"w-full flex justify-between items-center text-tremor-default py-2",u)},c),a))}));u.displayName="ListItem",module.exports=u;
