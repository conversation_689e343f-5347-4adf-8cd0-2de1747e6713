import e from"react";import{tremorTwMerge as o}from"../../../lib/tremorTwMerge.js";import{ChartTooltipFrame as r,ChartTooltipRow as l}from"../common/ChartTooltip.js";const t=({active:t,payload:a,valueFormatter:m})=>{if(t&&(null==a?void 0:a[0])){const t=null==a?void 0:a[0];return e.createElement(r,null,e.createElement("div",{className:o("px-4 py-2")},e.createElement(l,{value:m(t.value),name:t.name,color:t.payload.color})))}return null};export{t as DonutChartTooltip};
