"use strict";var e=require("tslib"),r=require("../../../lib/tremorTwMerge.cjs"),t=require("../../../lib/utils.cjs");function a(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var l=a(require("react"));const d=t.makeClassName("Divider"),m=l.default.forwardRef(((t,a)=>{const{className:m,children:o}=t,s=e.__rest(t,["className","children"]);return l.default.createElement("div",Object.assign({ref:a,className:r.tremorTwMerge(d("root"),"w-full mx-auto my-6 flex justify-between gap-3 items-center text-tremor-default","text-tremor-content","dark:text-dark-tremor-content",m)},s),o?l.default.createElement(l.default.Fragment,null,l.default.createElement("div",{className:r.tremorTwMerge("w-full h-[1px] bg-tremor-border dark:bg-dark-tremor-border")}),l.default.createElement("div",{className:r.tremorTwMerge("text-inherit whitespace-nowrap")},o),l.default.createElement("div",{className:r.tremorTwMerge("w-full h-[1px] bg-tremor-border dark:bg-dark-tremor-border")})):l.default.createElement("div",{className:r.tremorTwMerge("w-full h-[1px] bg-tremor-border dark:bg-dark-tremor-border")}))}));m.displayName="Divider",module.exports=m;
