'use client';
"use strict";var e=require("tslib"),t=require("../../../lib/constants.cjs"),a=require("../../../lib/theme.cjs"),l=require("../../../lib/tremorTwMerge.cjs"),o=require("../../../lib/utils.cjs"),r=require("react"),n=require("recharts"),i=require("../common/NoData.cjs"),u=require("./DonutChartTooltip.cjs"),s=require("./inputParser.cjs");function c(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var d=c(r);const m=e=>{const{cx:t,cy:a,innerRadius:l,outerRadius:o,startAngle:r,endAngle:i,className:u}=e;return d.default.createElement("g",null,d.default.createElement(n.Sector,{cx:t,cy:a,innerRadius:l,outerRadius:o,startAngle:r,endAngle:i,className:u,fill:"",opacity:.3,style:{outline:"none"}}))},f=d.default.forwardRef(((c,f)=>{const{data:v=[],category:p="value",index:g="name",colors:h=a.themeColorRange,variant:y="donut",valueFormatter:b=o.defaultValueFormatter,label:A,showLabel:T=!0,animationDuration:w=900,showAnimation:E=!1,showTooltip:j=!0,noDataText:x,onValueChange:k,customTooltip:q,className:C}=c,D=e.__rest(c,["data","category","index","colors","variant","valueFormatter","label","showLabel","animationDuration","showAnimation","showTooltip","noDataText","onValueChange","customTooltip","className"]),N=q,R="donut"==y,F=s.parseLabelInput(A,b,v,p),[S,L]=d.default.useState(void 0),M=!!k;return r.useEffect((()=>{const e=document.querySelectorAll(".recharts-pie-sector");e&&e.forEach((e=>{e.setAttribute("style","outline: none")}))}),[S]),d.default.createElement("div",Object.assign({ref:f,className:l.tremorTwMerge("w-full h-40",C)},D),d.default.createElement(n.ResponsiveContainer,{className:"h-full w-full"},(null==v?void 0:v.length)?d.default.createElement(n.PieChart,{onClick:M&&S?()=>{L(void 0),null==k||k(null)}:void 0,margin:{top:0,left:0,right:0,bottom:0}},T&&R?d.default.createElement("text",{className:l.tremorTwMerge("fill-tremor-content-emphasis","dark:fill-dark-tremor-content-emphasis"),x:"50%",y:"50%",textAnchor:"middle",dominantBaseline:"middle"},F):null,d.default.createElement(n.Pie,{className:l.tremorTwMerge("stroke-tremor-background dark:stroke-dark-tremor-background",k?"cursor-pointer":"cursor-default"),data:s.parseData(v,h),cx:"50%",cy:"50%",startAngle:90,endAngle:-270,innerRadius:R?"75%":"0%",outerRadius:"100%",stroke:"",strokeLinejoin:"round",dataKey:p,nameKey:g,isAnimationActive:E,animationDuration:w,onClick:function(e,t,a){a.stopPropagation(),M&&(S===t?(L(void 0),null==k||k(null)):(L(t),null==k||k(Object.assign({eventType:"slice"},e.payload.payload))))},activeIndex:S,inactiveShape:m,style:{outline:"none"}}),d.default.createElement(n.Tooltip,{wrapperStyle:{outline:"none"},isAnimationActive:!1,content:j?({active:e,payload:a})=>{var l;return N?d.default.createElement(N,{payload:null==a?void 0:a.map((e=>{var l,o,r;return Object.assign(Object.assign({},e),{color:null!==(r=null===(o=null===(l=null==a?void 0:a[0])||void 0===l?void 0:l.payload)||void 0===o?void 0:o.color)&&void 0!==r?r:t.BaseColors.Gray})})),active:e,label:null===(l=null==a?void 0:a[0])||void 0===l?void 0:l.name}):d.default.createElement(u.DonutChartTooltip,{active:e,payload:a,valueFormatter:b})}:d.default.createElement(d.default.Fragment,null)})):d.default.createElement(i,{noDataText:x})))}));f.displayName="DonutChart",module.exports=f;
