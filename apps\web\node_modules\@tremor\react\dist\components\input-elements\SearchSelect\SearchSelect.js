'use client';
import{__rest as e}from"tslib";import r from"../../../hooks/useInternalState.js";import t,{useRef as a,useMemo as l,isValidElement as o}from"react";import{Combobox as n,ComboboxButton as s,ComboboxInput as d,Transition as m,ComboboxOptions as c}from"@headlessui/react";import u from"../../../assets/ArrowDownHeadIcon.js";import i from"../../../assets/XCircleIcon.js";import{tremorTwMerge as p}from"../../../lib/tremorTwMerge.js";import{makeClassName as b}from"../../../lib/utils.js";import{constructValueToNameMapping as f,getFilteredOptions as h,getSelectButtonColors as g,hasValue as k}from"../selectUtils.js";const v=b("SearchSelect"),x=b("SearchSelect"),w=t.forwardRef(((b,w)=>{const{defaultValue:y="",searchValue:E,onSearchValueChange:N,value:C,onValueChange:S,placeholder:V="Select...",disabled:j=!1,icon:I,enableClear:M=!0,name:T,required:D,error:F=!1,errorMessage:q,children:z,className:A,id:H,autoComplete:O="off"}=b,R=e(b,["defaultValue","searchValue","onSearchValueChange","value","onValueChange","placeholder","disabled","icon","enableClear","name","required","error","errorMessage","children","className","id","autoComplete"]),U=a(null),[X,B]=r("",E),[G,J]=r(y,C),K=I,{reactElementChildren:L,valueToNameMapping:P}=l((()=>{const e=t.Children.toArray(z).filter(o);return{reactElementChildren:e,valueToNameMapping:f(e)}}),[z]),Q=l((()=>h(null!=X?X:"",L)),[X,L]);return t.createElement("div",{className:p("w-full min-w-[10rem] text-tremor-default",A)},t.createElement("div",{className:"relative"},t.createElement("select",{title:"search-select-hidden",required:D,className:p("h-full w-full absolute left-0 top-0 -z-10 opacity-0"),value:G,onChange:e=>{e.preventDefault()},name:T,disabled:j,id:H,onFocus:()=>{const e=U.current;e&&e.focus()}},t.createElement("option",{className:"hidden",value:"",disabled:!0,hidden:!0},V),Q.map((e=>{const r=e.props.value,a=e.props.children;return t.createElement("option",{className:"hidden",key:r,value:r},a)}))),t.createElement(n,Object.assign({as:"div",ref:w,defaultValue:G,value:G,onChange:e=>{null==S||S(e),J(e)},disabled:j,id:H},R),(({value:e})=>t.createElement(t.Fragment,null,t.createElement(s,{className:"w-full"},K&&t.createElement("span",{className:p("absolute inset-y-0 left-0 flex items-center ml-px pl-2.5")},t.createElement(K,{className:p(v("Icon"),"flex-none h-5 w-5","text-tremor-content-subtle","dark:text-dark-tremor-content-subtle")})),t.createElement(d,{ref:U,className:p("w-full outline-none text-left whitespace-nowrap truncate rounded-tremor-default focus:ring-2 transition duration-100 text-tremor-default pr-14 border py-2","border-tremor-border shadow-tremor-input focus:border-tremor-brand-subtle focus:ring-tremor-brand-muted","dark:border-dark-tremor-border dark:shadow-dark-tremor-input dark:focus:border-dark-tremor-brand-subtle dark:focus:ring-dark-tremor-brand-muted",K?"pl-10":"pl-3",j?"placeholder:text-tremor-content-subtle dark:placeholder:text-tremor-content-subtle":"placeholder:text-tremor-content dark:placeholder:text-tremor-content",g(k(e),j,F)),placeholder:V,onChange:e=>{null==N||N(e.target.value),B(e.target.value)},displayValue:e=>{var r;return null!==(r=P.get(e))&&void 0!==r?r:""},autoComplete:O}),t.createElement("div",{className:p("absolute inset-y-0 right-0 flex items-center pr-2.5")},t.createElement(u,{className:p(v("arrowDownIcon"),"flex-none h-5 w-5","!text-tremor-content-subtle","!dark:text-dark-tremor-content-subtle")}))),M&&G?t.createElement("button",{type:"button",className:p("absolute inset-y-0 right-0 flex items-center mr-8"),onClick:e=>{e.preventDefault(),J(""),B(""),null==S||S(""),null==N||N("")}},t.createElement(i,{className:p(x("clearIcon"),"flex-none h-4 w-4","text-tremor-content-subtle","dark:text-dark-tremor-content-subtle")})):null,Q.length>0&&t.createElement(m,{enter:"transition ease duration-100 transform",enterFrom:"opacity-0 -translate-y-4",enterTo:"opacity-100 translate-y-0",leave:"transition ease duration-100 transform",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 -translate-y-4"},t.createElement(c,{anchor:"bottom start",className:p("z-10 divide-y w-[var(--button-width)] overflow-y-auto outline-none rounded-tremor-default text-tremor-default max-h-[228px] border [--anchor-gap:4px]","bg-tremor-background border-tremor-border divide-tremor-border shadow-tremor-dropdown","dark:bg-dark-tremor-background dark:border-dark-tremor-border dark:divide-dark-tremor-border dark:shadow-dark-tremor-dropdown")},Q)))))),F&&q?t.createElement("p",{className:p("errorMessage","text-sm text-rose-500 mt-1")},q):null)}));w.displayName="SearchSelect";export{w as default};
