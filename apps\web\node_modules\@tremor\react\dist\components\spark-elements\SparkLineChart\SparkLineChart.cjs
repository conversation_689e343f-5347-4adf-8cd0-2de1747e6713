'use client';
"use strict";var e=require("tslib"),t=require("react"),a=require("recharts"),r=require("../../../lib/constants.cjs"),o=require("../../../lib/theme.cjs"),n=require("../../../lib/tremorTwMerge.cjs"),i=require("../../../lib/utils.cjs"),l=require("../../chart-elements/common/utils.cjs"),s=require("../../chart-elements/common/NoData.cjs");function u(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var c=u(t);const m=c.default.forwardRef(((t,u)=>{const{data:m=[],categories:d=[],index:f,colors:h=o.themeColorRange,animationDuration:g=900,showAnimation:p=!1,curveType:x="linear",connectNulls:y=!1,noDataText:N,autoMinValue:v=!1,minValue:C,maxValue:j,className:q}=t,b=e.__rest(t,["data","categories","index","colors","animationDuration","showAnimation","curveType","connectNulls","noDataText","autoMinValue","minValue","maxValue","className"]),k=l.constructCategoryColors(d,h),w=l.getYAxisDomain(v,C,j);return c.default.createElement("div",Object.assign({ref:u,className:n.tremorTwMerge("w-28 h-12",q)},b),c.default.createElement(a.ResponsiveContainer,{className:"h-full w-full"},(null==m?void 0:m.length)?c.default.createElement(a.LineChart,{data:m,margin:{top:1,left:1,right:1,bottom:1}},c.default.createElement(a.YAxis,{hide:!0,domain:w}),c.default.createElement(a.XAxis,{hide:!0,dataKey:f}),d.map((e=>{var t;return c.default.createElement(a.Line,{className:n.tremorTwMerge(i.getColorClassNames(null!==(t=k.get(e))&&void 0!==t?t:r.BaseColors.Gray,o.colorPalette.text).strokeColor),strokeOpacity:1,dot:!1,key:e,name:e,type:x,dataKey:e,stroke:"",strokeWidth:2,strokeLinejoin:"round",strokeLinecap:"round",isAnimationActive:p,animationDuration:g,connectNulls:y})}))):c.default.createElement(s,{noDataText:N})))}));m.displayName="SparkLineChart",module.exports=m;
