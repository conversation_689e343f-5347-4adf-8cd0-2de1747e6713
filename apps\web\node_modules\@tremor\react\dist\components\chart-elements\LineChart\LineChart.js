'use client';
import{__rest as e}from"tslib";import t,{useState as o,Fragment as a}from"react";import{ResponsiveContainer as r,<PERSON><PERSON><PERSON> as i,CartesianGrid as n,XAxis as l,Label as s,YAxis as d,<PERSON>ltip as c,Legend as m,Line as u,Dot as p}from"recharts";import k from"../common/ChartLegend.js";import y from"../common/ChartTooltip.js";import v from"../common/NoData.js";import{constructCategoryColors as h,hasOnlyOneValueForThisKey as f,getYAxisDomain as g}from"../common/utils.js";import{BaseColors as x}from"../../../lib/constants.js";import{themeColorRange as b,colorPalette as L}from"../../../lib/theme.js";import{tremorTwMerge as E}from"../../../lib/tremorTwMerge.js";import{getColorClassNames as w,defaultValueFormatter as A}from"../../../lib/utils.js";const N=t.forwardRef(((N,T)=>{const{data:j=[],categories:K=[],index:C,colors:D=b,valueFormatter:G=A,startEndOnly:W=!1,showXAxis:O=!0,showYAxis:V=!0,yAxisWidth:S=56,intervalType:F="equidistantPreserveStart",animationDuration:X=900,showAnimation:M=!1,showTooltip:P=!0,showLegend:Y=!0,showGridLines:q=!0,autoMinValue:z=!1,curveType:B="linear",minValue:H,maxValue:R,connectNulls:I=!1,allowDecimals:J=!0,noDataText:Q,className:U,onValueChange:Z,enableLegendSlider:$=!1,customTooltip:_,rotateLabelX:ee,padding:te=(O||V?{left:20,right:20}:{left:0,right:0}),tickGap:oe=5,xAxisLabel:ae,yAxisLabel:re}=N,ie=e(N,["data","categories","index","colors","valueFormatter","startEndOnly","showXAxis","showYAxis","yAxisWidth","intervalType","animationDuration","showAnimation","showTooltip","showLegend","showGridLines","autoMinValue","curveType","minValue","maxValue","connectNulls","allowDecimals","noDataText","className","onValueChange","enableLegendSlider","customTooltip","rotateLabelX","padding","tickGap","xAxisLabel","yAxisLabel"]),ne=_,[le,se]=o(60),[de,ce]=o(void 0),[me,ue]=o(void 0),pe=h(K,D),ke=g(z,H,R),ye=!!Z;function ve(e){ye&&(e===me&&!de||f(j,e)&&de&&de.dataKey===e?(ue(void 0),null==Z||Z(null)):(ue(e),null==Z||Z({eventType:"category",categoryClicked:e})),ce(void 0))}return t.createElement("div",Object.assign({ref:T,className:E("w-full h-80",U)},ie),t.createElement(r,{className:"h-full w-full"},(null==j?void 0:j.length)?t.createElement(i,{data:j,onClick:ye&&(me||de)?()=>{ce(void 0),ue(void 0),null==Z||Z(null)}:void 0,margin:{bottom:ae?30:void 0,left:re?20:void 0,right:re?5:void 0,top:5}},q?t.createElement(n,{className:E("stroke-1","stroke-tremor-border","dark:stroke-dark-tremor-border"),horizontal:!0,vertical:!1}):null,t.createElement(l,{padding:te,hide:!O,dataKey:C,interval:W?"preserveStartEnd":F,tick:{transform:"translate(0, 6)"},ticks:W?[j[0][C],j[j.length-1][C]]:void 0,fill:"",stroke:"",className:E("text-tremor-label","fill-tremor-content","dark:fill-dark-tremor-content"),tickLine:!1,axisLine:!1,minTickGap:oe,angle:null==ee?void 0:ee.angle,dy:null==ee?void 0:ee.verticalShift,height:null==ee?void 0:ee.xAxisHeight},ae&&t.createElement(s,{position:"insideBottom",offset:-20,className:"fill-tremor-content-emphasis text-tremor-default font-medium dark:fill-dark-tremor-content-emphasis"},ae)),t.createElement(d,{width:S,hide:!V,axisLine:!1,tickLine:!1,type:"number",domain:ke,tick:{transform:"translate(-3, 0)"},fill:"",stroke:"",className:E("text-tremor-label","fill-tremor-content","dark:fill-dark-tremor-content"),tickFormatter:G,allowDecimals:J},re&&t.createElement(s,{position:"insideLeft",style:{textAnchor:"middle"},angle:-90,offset:-15,className:"fill-tremor-content-emphasis text-tremor-default font-medium dark:fill-dark-tremor-content-emphasis"},re)),t.createElement(c,{wrapperStyle:{outline:"none"},isAnimationActive:!1,cursor:{stroke:"#d1d5db",strokeWidth:1},content:P?({active:e,payload:o,label:a})=>ne?t.createElement(ne,{payload:null==o?void 0:o.map((e=>{var t;return Object.assign(Object.assign({},e),{color:null!==(t=pe.get(e.dataKey))&&void 0!==t?t:x.Gray})})),active:e,label:a}):t.createElement(y,{active:e,payload:o,label:a,valueFormatter:G,categoryColors:pe}):t.createElement(t.Fragment,null),position:{y:0}}),Y?t.createElement(m,{verticalAlign:"top",height:le,content:({payload:e})=>k({payload:e},pe,se,me,ye?e=>ve(e):void 0,$)}):null,K.map((e=>{var o;return t.createElement(u,{className:E(w(null!==(o=pe.get(e))&&void 0!==o?o:x.Gray,L.text).strokeColor),strokeOpacity:de||me&&me!==e?.3:1,activeDot:e=>{var o;const{cx:a,cy:r,stroke:i,strokeLinecap:n,strokeLinejoin:l,strokeWidth:s,dataKey:d}=e;return t.createElement(p,{className:E("stroke-tremor-background dark:stroke-dark-tremor-background",Z?"cursor-pointer":"",w(null!==(o=pe.get(d))&&void 0!==o?o:x.Gray,L.text).fillColor),cx:a,cy:r,r:5,fill:"",stroke:i,strokeLinecap:n,strokeLinejoin:l,strokeWidth:s,onClick:(t,o)=>function(e,t){t.stopPropagation(),ye&&(e.index===(null==de?void 0:de.index)&&e.dataKey===(null==de?void 0:de.dataKey)||f(j,e.dataKey)&&me&&me===e.dataKey?(ue(void 0),ce(void 0),null==Z||Z(null)):(ue(e.dataKey),ce({index:e.index,dataKey:e.dataKey}),null==Z||Z(Object.assign({eventType:"dot",categoryClicked:e.dataKey},e.payload))))}(e,o)})},dot:o=>{var r;const{stroke:i,strokeLinecap:n,strokeLinejoin:l,strokeWidth:s,cx:d,cy:c,dataKey:m,index:u}=o;return f(j,e)&&!(de||me&&me!==e)||(null==de?void 0:de.index)===u&&(null==de?void 0:de.dataKey)===e?t.createElement(p,{key:u,cx:d,cy:c,r:5,stroke:i,fill:"",strokeLinecap:n,strokeLinejoin:l,strokeWidth:s,className:E("stroke-tremor-background dark:stroke-dark-tremor-background",Z?"cursor-pointer":"",w(null!==(r=pe.get(m))&&void 0!==r?r:x.Gray,L.text).fillColor)}):t.createElement(a,{key:u})},key:e,name:e,type:B,dataKey:e,stroke:"",strokeWidth:2,strokeLinejoin:"round",strokeLinecap:"round",isAnimationActive:M,animationDuration:X,connectNulls:I})})),Z?K.map((e=>t.createElement(u,{className:E("cursor-pointer"),strokeOpacity:0,key:e,name:e,type:B,dataKey:e,stroke:"transparent",fill:"transparent",legendType:"none",tooltipType:"none",strokeWidth:12,connectNulls:I,onClick:(e,t)=>{t.stopPropagation();const{name:o}=e;ve(o)}}))):null):t.createElement(v,{noDataText:Q})))}));N.displayName="LineChart";export{N as default};
