"use client";import n,{Fragment as N,create<PERSON>ontext as ae,createRef as ie,useContext as pe,useEffect as se,useMemo as E,useReducer as de,useRef as W}from"react";import{useEscape as ue}from'../../hooks/use-escape.js';import{useEvent as A}from'../../hooks/use-event.js';import{useId as M}from'../../hooks/use-id.js';import{useInertOthers as Te}from'../../hooks/use-inert-others.js';import{useIsTouchDevice as fe}from'../../hooks/use-is-touch-device.js';import{useOnDisappear as ge}from'../../hooks/use-on-disappear.js';import{useOutsideClick as ce}from'../../hooks/use-outside-click.js';import{useOwnerDocument as me}from'../../hooks/use-owner.js';import{MainTreeProvider as $,useMainTreeNode as De,useRootContainers as Pe}from'../../hooks/use-root-containers.js';import{useScrollLock as ye}from'../../hooks/use-scroll-lock.js';import{useServerHandoffComplete as Ee}from'../../hooks/use-server-handoff-complete.js';import{useSyncRefs as G}from'../../hooks/use-sync-refs.js';import{CloseProvider as Ae}from'../../internal/close-provider.js';import{ResetOpenClosedProvider as _e,State as x,useOpenClosed as j}from'../../internal/open-closed.js';import{ForcePortalRoot as Y}from'../../internal/portal-force-root.js';import{match as Ce}from'../../utils/match.js';import{RenderFeatures as J,forwardRefWithAs as _,useRender as L}from'../../utils/render.js';import{Description as K,useDescriptions as Re}from'../description/description.js';import{FocusTrap as Fe,FocusTrapFeatures as C}from'../focus-trap/focus-trap.js';import{Portal as be,PortalGroup as ve,useNestedPortals as xe}from'../portal/portal.js';import{Transition as Le,TransitionChild as X}from'../transition/transition.js';var Oe=(o=>(o[o.Open=0]="Open",o[o.Closed=1]="Closed",o))(Oe||{}),he=(t=>(t[t.SetTitleId=0]="SetTitleId",t))(he||{});let Se={[0](e,t){return e.titleId===t.id?e:{...e,titleId:t.id}}},k=ae(null);k.displayName="DialogContext";function O(e){let t=pe(k);if(t===null){let o=new Error(`<${e} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(o,O),o}return t}function Ie(e,t){return Ce(t.type,Se,e,t)}let V=_(function(t,o){let a=M(),{id:l=`headlessui-dialog-${a}`,open:i,onClose:p,initialFocus:d,role:s="dialog",autoFocus:f=!0,__demoMode:u=!1,unmount:P=!1,...h}=t,R=W(!1);s=function(){return s==="dialog"||s==="alertdialog"?s:(R.current||(R.current=!0,console.warn(`Invalid role [${s}] passed to <Dialog />. Only \`dialog\` and and \`alertdialog\` are supported. Using \`dialog\` instead.`)),"dialog")}();let c=j();i===void 0&&c!==null&&(i=(c&x.Open)===x.Open);let T=W(null),S=G(T,o),F=me(T),g=i?0:1,[b,q]=de(Ie,{titleId:null,descriptionId:null,panelRef:ie()}),m=A(()=>p(!1)),w=A(r=>q({type:0,id:r})),D=Ee()?g===0:!1,[z,Q]=xe(),Z={get current(){var r;return(r=b.panelRef.current)!=null?r:T.current}},v=De(),{resolveContainers:I}=Pe({mainTreeNode:v,portals:z,defaultContainers:[Z]}),B=c!==null?(c&x.Closing)===x.Closing:!1;Te(u||B?!1:D,{allowed:A(()=>{var r,H;return[(H=(r=T.current)==null?void 0:r.closest("[data-headlessui-portal]"))!=null?H:null]}),disallowed:A(()=>{var r;return[(r=v==null?void 0:v.closest("body > *:not(#headlessui-portal-root)"))!=null?r:null]})}),ce(D,I,r=>{r.preventDefault(),m()}),ue(D,F==null?void 0:F.defaultView,r=>{r.preventDefault(),r.stopPropagation(),document.activeElement&&"blur"in document.activeElement&&typeof document.activeElement.blur=="function"&&document.activeElement.blur(),m()}),ye(u||B?!1:D,F,I),ge(D,T,m);let[ee,te]=Re(),oe=E(()=>[{dialogState:g,close:m,setTitleId:w,unmount:P},b],[g,b,m,w,P]),U=E(()=>({open:g===0}),[g]),ne={ref:S,id:l,role:s,tabIndex:-1,"aria-modal":u?void 0:g===0?!0:void 0,"aria-labelledby":b.titleId,"aria-describedby":ee,unmount:P},re=!fe(),y=C.None;D&&!u&&(y|=C.RestoreFocus,y|=C.TabLock,f&&(y|=C.AutoFocus),re&&(y|=C.InitialFocus));let le=L();return n.createElement(_e,null,n.createElement(Y,{force:!0},n.createElement(be,null,n.createElement(k.Provider,{value:oe},n.createElement(ve,{target:T},n.createElement(Y,{force:!1},n.createElement(te,{slot:U},n.createElement(Q,null,n.createElement(Fe,{initialFocus:d,initialFocusFallback:T,containers:I,features:y},n.createElement(Ae,{value:m},le({ourProps:ne,theirProps:h,slot:U,defaultTag:Me,features:Ge,visible:g===0,name:"Dialog"})))))))))))}),Me="div",Ge=J.RenderStrategy|J.Static;function ke(e,t){let{transition:o=!1,open:a,...l}=e,i=j(),p=e.hasOwnProperty("open")||i!==null,d=e.hasOwnProperty("onClose");if(!p&&!d)throw new Error("You have to provide an `open` and an `onClose` prop to the `Dialog` component.");if(!p)throw new Error("You provided an `onClose` prop to the `Dialog`, but forgot an `open` prop.");if(!d)throw new Error("You provided an `open` prop to the `Dialog`, but forgot an `onClose` prop.");if(!i&&typeof e.open!="boolean")throw new Error(`You provided an \`open\` prop to the \`Dialog\`, but the value is not a boolean. Received: ${e.open}`);if(typeof e.onClose!="function")throw new Error(`You provided an \`onClose\` prop to the \`Dialog\`, but the value is not a function. Received: ${e.onClose}`);return(a!==void 0||o)&&!l.static?n.createElement($,null,n.createElement(Le,{show:a,transition:o,unmount:l.unmount},n.createElement(V,{ref:t,...l}))):n.createElement($,null,n.createElement(V,{ref:t,open:a,...l}))}let we="div";function Be(e,t){let o=M(),{id:a=`headlessui-dialog-panel-${o}`,transition:l=!1,...i}=e,[{dialogState:p,unmount:d},s]=O("Dialog.Panel"),f=G(t,s.panelRef),u=E(()=>({open:p===0}),[p]),P=A(S=>{S.stopPropagation()}),h={ref:f,id:a,onClick:P},R=l?X:N,c=l?{unmount:d}:{},T=L();return n.createElement(R,{...c},T({ourProps:h,theirProps:i,slot:u,defaultTag:we,name:"Dialog.Panel"}))}let Ue="div";function He(e,t){let{transition:o=!1,...a}=e,[{dialogState:l,unmount:i}]=O("Dialog.Backdrop"),p=E(()=>({open:l===0}),[l]),d={ref:t,"aria-hidden":!0},s=o?X:N,f=o?{unmount:i}:{},u=L();return n.createElement(s,{...f},u({ourProps:d,theirProps:a,slot:p,defaultTag:Ue,name:"Dialog.Backdrop"}))}let Ne="h2";function We(e,t){let o=M(),{id:a=`headlessui-dialog-title-${o}`,...l}=e,[{dialogState:i,setTitleId:p}]=O("Dialog.Title"),d=G(t);se(()=>(p(a),()=>p(null)),[a,p]);let s=E(()=>({open:i===0}),[i]),f={ref:d,id:a};return L()({ourProps:f,theirProps:l,slot:s,defaultTag:Ne,name:"Dialog.Title"})}let $e=_(ke),je=_(Be),Dt=_(He),Ye=_(We),Pt=K,yt=Object.assign($e,{Panel:je,Title:Ye,Description:K});export{yt as Dialog,Dt as DialogBackdrop,Pt as DialogDescription,je as DialogPanel,Ye as DialogTitle};
