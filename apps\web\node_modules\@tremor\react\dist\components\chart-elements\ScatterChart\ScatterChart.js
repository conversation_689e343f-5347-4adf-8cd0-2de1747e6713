'use client';
import{__rest as e}from"tslib";import t,{useState as a}from"react";import{ResponsiveContainer as l,<PERSON><PERSON><PERSON><PERSON><PERSON> as o,CartesianGrid as i,XAxis as r,Label as n,YAxis as s,Tooltip as m,<PERSON><PERSON>xi<PERSON> as c,<PERSON><PERSON><PERSON> as d,<PERSON> as u,Dot as p}from"recharts";import y from"../common/ChartLegend.js";import v from"./ScatterChartTooltip.js";import f from"../common/NoData.js";import{constructCategories as h,constructCategoryColors as g,deepEqual as x,getYAxisDomain as k}from"../common/utils.js";import{BaseColors as b}from"../../../lib/constants.js";import{themeColorRange as w,colorPalette as E}from"../../../lib/theme.js";import{tremorTwMerge as A}from"../../../lib/tremorTwMerge.js";import{getColorClassNames as L,defaultValueFormatter as T}from"../../../lib/utils.js";const V=t.forwardRef(((V,C)=>{const{data:j=[],x:D,y:N,size:O,category:S,colors:X=w,showOpacity:G=!1,sizeRange:Y=[1,1e3],valueFormatter:z={x:T,y:T,size:T},startEndOnly:F=!1,showXAxis:M=!0,showYAxis:K=!0,yAxisWidth:R=56,intervalType:W="equidistantPreserveStart",animationDuration:P=900,showAnimation:q=!1,showTooltip:B=!0,showLegend:H=!0,showGridLines:$=!0,autoMinXValue:I=!1,minXValue:J,maxXValue:Q,autoMinYValue:U=!1,minYValue:Z,maxYValue:_,allowDecimals:ee=!0,noDataText:te,onValueChange:ae,customTooltip:le,rotateLabelX:oe,className:ie,enableLegendSlider:re=!1,tickGap:ne=5,xAxisLabel:se,yAxisLabel:me}=V,ce=e(V,["data","x","y","size","category","colors","showOpacity","sizeRange","valueFormatter","startEndOnly","showXAxis","showYAxis","yAxisWidth","intervalType","animationDuration","showAnimation","showTooltip","showLegend","showGridLines","autoMinXValue","minXValue","maxXValue","autoMinYValue","minYValue","maxYValue","allowDecimals","noDataText","onValueChange","customTooltip","rotateLabelX","className","enableLegendSlider","tickGap","xAxisLabel","yAxisLabel"]),de=le,[ue,pe]=a(60),[ye,ve]=t.useState(void 0),[fe,he]=a(void 0),ge=!!ae;function xe(e,t,a){a.stopPropagation(),ge&&(x(ye,e.node)?(he(void 0),ve(void 0),null==ae||ae(null)):(ve(e.node),he(e.payload[S]),null==ae||ae(Object.assign({eventType:"bubble",categoryClicked:e.payload[S]},e.payload))))}const ke=h(j,S),be=g(ke,X),we=k(I,J,Q),Ee=k(U,Z,_);return t.createElement("div",Object.assign({ref:C,className:A("w-full h-80",ie)},ce),t.createElement(l,{className:"h-full w-full"},(null==j?void 0:j.length)?t.createElement(o,{onClick:ge&&(fe||ye)?()=>{ve(void 0),he(void 0),null==ae||ae(null)}:void 0,margin:{bottom:se?20:void 0,left:20,right:20,top:5}},$?t.createElement(i,{className:A("stroke-1","stroke-tremor-border","dark:stroke-dark-tremor-border"),horizontal:!0,vertical:!0}):null,D?t.createElement(r,{hide:!M,dataKey:D,interval:F?"preserveStartEnd":W,tick:{transform:"translate(0, 6)"},ticks:F?[j[0][D],j[j.length-1][D]]:void 0,type:"number",name:D,fill:"",stroke:"",className:A("text-tremor-label","fill-tremor-content","dark:fill-dark-tremor-content"),tickLine:!1,tickFormatter:z.x,axisLine:!1,minTickGap:ne,domain:we,allowDataOverflow:!0,angle:null==oe?void 0:oe.angle,dy:null==oe?void 0:oe.verticalShift,height:null==oe?void 0:oe.xAxisHeight},se&&t.createElement(n,{position:"insideBottom",offset:-20,className:"fill-tremor-content-emphasis text-tremor-default font-medium dark:fill-dark-tremor-content-emphasis"},se)):null,N?t.createElement(s,{width:R,hide:!K,axisLine:!1,tickLine:!1,dataKey:N,type:"number",name:N,domain:Ee,tick:{transform:"translate(-3, 0)"},tickFormatter:z.y,fill:"",stroke:"",className:A("text-tremor-label","fill-tremor-content","dark:fill-dark-tremor-content"),allowDecimals:ee,allowDataOverflow:!0},me&&t.createElement(n,{position:"insideLeft",style:{textAnchor:"middle"},angle:-90,offset:-15,className:"fill-tremor-content-emphasis text-tremor-default font-medium dark:fill-dark-tremor-content-emphasis"},me)):null,t.createElement(m,{wrapperStyle:{outline:"none"},isAnimationActive:!1,cursor:{stroke:"#d1d5db",strokeWidth:1},content:B?({active:e,payload:a,label:l})=>{var o,i;const r=S?null===(i=null===(o=null==a?void 0:a[0])||void 0===o?void 0:o.payload)||void 0===i?void 0:i[S]:l;return de?t.createElement(de,{payload:null==a?void 0:a.map((e=>{var t;return Object.assign(Object.assign({},e),{color:null!==(t=be.get(r))&&void 0!==t?t:b.Gray})})),active:e,label:r}):t.createElement(v,{active:e,payload:a,label:r,valueFormatter:z,axis:{x:D,y:N,size:O},category:S,categoryColors:be})}:t.createElement(t.Fragment,null)}),O?t.createElement(c,{dataKey:O,type:"number",range:Y,name:O}):null,ke.map((e=>{var a,l;return t.createElement(d,{className:A(L(null!==(a=be.get(e))&&void 0!==a?a:b.Gray,E.text).fillColor,G?L(null!==(l=be.get(e))&&void 0!==l?l:b.Gray,E.text).strokeColor:"",ae?"cursor-pointer":""),fill:`url(#${be.get(e)})`,fillOpacity:G?.7:1,key:e,name:e,data:S?j.filter((t=>t[S]===e)):j,isAnimationActive:q,animationDuration:P,shape:e=>((e,a,l)=>{const{cx:o,cy:i,width:r,node:n,fillOpacity:s,name:m}=e;return t.createElement(p,{cx:o,cy:i,r:r/2,opacity:a||l&&l!==m?x(a,n)?s:.3:s})})(e,ye,fe),onClick:xe})})),H?t.createElement(u,{verticalAlign:"top",height:ue,content:({payload:e})=>y({payload:e},be,pe,fe,ge?e=>{return t=e,void(ge&&(t!==fe||ye?(he(t),null==ae||ae({eventType:"category",categoryClicked:t})):(he(void 0),null==ae||ae(null)),ve(void 0)));var t}:void 0,re)}):null):t.createElement(f,{noDataText:te})))}));V.displayName="ScatterChart";export{V as default};
