'use client';
import{__rest as e}from"tslib";import r,{createContext as t}from"react";import o from"../../../contexts/BaseColorContext.js";import"../../../contexts/IndexContext.js";import"../../../contexts/RootStylesContext.js";import"../../../contexts/SelectedValueContext.js";import{Tab as a}from"@headlessui/react";import{tremorTwMerge as s}from"../../../lib/tremorTwMerge.js";import{makeClassName as l}from"../../../lib/utils.js";const i=l("TabList"),m=t("line"),n={line:s("flex border-b space-x-4","border-tremor-border","dark:border-dark-tremor-border"),solid:s("inline-flex p-0.5 rounded-tremor-default space-x-1.5","bg-tremor-background-subtle","dark:bg-dark-tremor-background-subtle")},c=r.forwardRef(((t,l)=>{const{color:c,variant:d="line",children:b,className:f}=t,p=e(t,["color","variant","children","className"]);return r.createElement(a.List,Object.assign({ref:l,className:s(i("root"),"justify-start overflow-x-clip",n[d],f)},p),r.createElement(m.Provider,{value:d},r.createElement(o.Provider,{value:c},b)))}));c.displayName="TabList";export{m as TabVariantContext,c as default};
