import{__rest as r}from"tslib";import e from"react";import{tremorTwMerge as t}from"../../../lib/tremorTwMerge.js";import{makeClassName as o}from"../../../lib/utils.js";const a=o("TableFoot"),m=e.forwardRef(((o,m)=>{const{children:s,className:d}=o,l=r(o,["children","className"]);return e.createElement("tfoot",Object.assign({ref:m,className:t(a("root"),"text-left font-medium border-t-[1px] ","text-tremor-content border-tremor-border","dark:text-dark-tremor-content dark:border-dark-tremor-border",d)},l),s)}));m.displayName="TableFoot";export{m as default};
