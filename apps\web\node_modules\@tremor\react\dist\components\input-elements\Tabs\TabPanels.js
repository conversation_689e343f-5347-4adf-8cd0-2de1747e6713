'use client';
import{__rest as e}from"tslib";import{Tab as t}from"@headlessui/react";import"../../../contexts/BaseColorContext.js";import r from"../../../contexts/IndexContext.js";import"../../../contexts/RootStylesContext.js";import o from"../../../contexts/SelectedValueContext.js";import{tremorTwMerge as s}from"../../../lib/tremorTwMerge.js";import{makeClassName as a}from"../../../lib/utils.js";import l from"react";const m=a("TabPanels"),n=l.forwardRef(((a,n)=>{const{children:i,className:c}=a,d=e(a,["children","className"]);return l.createElement(t.Panels,Object.assign({as:"div",ref:n,className:s(m("root"),"w-full",c)},d),(({selectedIndex:e})=>l.createElement(o.Provider,{value:{selectedValue:e}},l.Children.map(i,((e,t)=>l.createElement(r.Provider,{value:t},e))))))}));n.displayName="TabPanels";export{n as default};
