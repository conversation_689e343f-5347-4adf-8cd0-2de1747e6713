'use client';
"use strict";var e=require("tslib"),t=require("react"),r=require("@headlessui/react"),a=require("../../../lib/tremorTwMerge.cjs"),o=require("../../../lib/utils.cjs");function s(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var c=s(t);const n=o.makeClassName("SelectItem"),d=c.default.forwardRef(((t,o)=>{const{value:s,icon:d,className:l,children:u}=t,m=e.__rest(t,["value","icon","className","children"]),i=d;return c.default.createElement(r.ListboxOption,Object.assign({className:a.tremorTwMerge(n("root"),"flex justify-start items-center cursor-default text-tremor-default px-2.5 py-2.5","data-[focus]:bg-tremor-background-muted  data-[focus]:text-tremor-content-strong data-[selected]:text-tremor-content-strong data-[selected]:bg-tremor-background-muted text-tremor-content-emphasis","dark:data-[focus]:bg-dark-tremor-background-muted  dark:data-[focus]:text-dark-tremor-content-strong dark:data-[selected]:text-dark-tremor-content-strong dark:data-[selected]:bg-dark-tremor-background-muted dark:text-dark-tremor-content-emphasis",l),ref:o,key:s,value:s},m),i&&c.default.createElement(i,{className:a.tremorTwMerge(n("icon"),"flex-none w-5 h-5 mr-1.5","text-tremor-content-subtle","dark:text-dark-tremor-content-subtle")}),c.default.createElement("span",{className:"whitespace-nowrap truncate"},null!=u?u:s))}));d.displayName="SelectItem",module.exports=d;
