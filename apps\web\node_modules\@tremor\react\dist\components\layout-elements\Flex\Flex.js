import{__rest as e}from"tslib";import{tremorTwMerge as t}from"../../../lib/tremorTwMerge.js";import{makeClassName as r}from"../../../lib/utils.js";import s from"react";const i=r("Flex"),l={start:"justify-start",end:"justify-end",center:"justify-center",between:"justify-between",around:"justify-around",evenly:"justify-evenly"},n={start:"items-start",end:"items-end",center:"items-center",baseline:"items-baseline",stretch:"items-stretch"},o={row:"flex-row",col:"flex-col","row-reverse":"flex-row-reverse","col-reverse":"flex-col-reverse"},a=s.forwardRef(((r,a)=>{const{flexDirection:f="row",justifyContent:c="between",alignItems:m="center",children:u,className:d}=r,j=e(r,["flexDirection","justifyContent","alignItems","children","className"]);return s.createElement("div",Object.assign({ref:a,className:t(i("root"),"flex w-full",o[f],l[c],n[m],d)},j),u)}));a.displayName="Flex";export{a as default};
