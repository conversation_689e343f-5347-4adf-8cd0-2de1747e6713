'use client';
import{__rest as e}from"tslib";import{BaseColors as t}from"../../../lib/constants.js";import{themeColorRange as a}from"../../../lib/theme.js";import{tremorTwMerge as o}from"../../../lib/tremorTwMerge.js";import{defaultValueFormatter as n}from"../../../lib/utils.js";import l,{useEffect as r}from"react";import{ResponsiveContainer as i,PieChart as s,Pie as m,Tooltip as c,Sector as u}from"recharts";import d from"../common/NoData.js";import{DonutChartTooltip as p}from"./DonutChartTooltip.js";import{parseLabelInput as v,parseData as f}from"./inputParser.js";const g=e=>{const{cx:t,cy:a,innerRadius:o,outerRadius:n,startAngle:r,endAngle:i,className:s}=e;return l.createElement("g",null,l.createElement(u,{cx:t,cy:a,innerRadius:o,outerRadius:n,startAngle:r,endAngle:i,className:s,fill:"",opacity:.3,style:{outline:"none"}}))},y=l.forwardRef(((u,y)=>{const{data:h=[],category:b="value",index:A="name",colors:x=a,variant:E="donut",valueFormatter:j=n,label:k,showLabel:w=!0,animationDuration:N=900,showAnimation:T=!1,showTooltip:D=!0,noDataText:R,onValueChange:C,customTooltip:F,className:O}=u,S=e(u,["data","category","index","colors","variant","valueFormatter","label","showLabel","animationDuration","showAnimation","showTooltip","noDataText","onValueChange","customTooltip","className"]),L=F,K="donut"==E,P=v(k,j,h,b),[V,q]=l.useState(void 0),B=!!C;return r((()=>{const e=document.querySelectorAll(".recharts-pie-sector");e&&e.forEach((e=>{e.setAttribute("style","outline: none")}))}),[V]),l.createElement("div",Object.assign({ref:y,className:o("w-full h-40",O)},S),l.createElement(i,{className:"h-full w-full"},(null==h?void 0:h.length)?l.createElement(s,{onClick:B&&V?()=>{q(void 0),null==C||C(null)}:void 0,margin:{top:0,left:0,right:0,bottom:0}},w&&K?l.createElement("text",{className:o("fill-tremor-content-emphasis","dark:fill-dark-tremor-content-emphasis"),x:"50%",y:"50%",textAnchor:"middle",dominantBaseline:"middle"},P):null,l.createElement(m,{className:o("stroke-tremor-background dark:stroke-dark-tremor-background",C?"cursor-pointer":"cursor-default"),data:f(h,x),cx:"50%",cy:"50%",startAngle:90,endAngle:-270,innerRadius:K?"75%":"0%",outerRadius:"100%",stroke:"",strokeLinejoin:"round",dataKey:b,nameKey:A,isAnimationActive:T,animationDuration:N,onClick:function(e,t,a){a.stopPropagation(),B&&(V===t?(q(void 0),null==C||C(null)):(q(t),null==C||C(Object.assign({eventType:"slice"},e.payload.payload))))},activeIndex:V,inactiveShape:g,style:{outline:"none"}}),l.createElement(c,{wrapperStyle:{outline:"none"},isAnimationActive:!1,content:D?({active:e,payload:a})=>{var o;return L?l.createElement(L,{payload:null==a?void 0:a.map((e=>{var o,n,l;return Object.assign(Object.assign({},e),{color:null!==(l=null===(n=null===(o=null==a?void 0:a[0])||void 0===o?void 0:o.payload)||void 0===n?void 0:n.color)&&void 0!==l?l:t.Gray})})),active:e,label:null===(o=null==a?void 0:a[0])||void 0===o?void 0:o.name}):l.createElement(p,{active:e,payload:a,valueFormatter:j})}:l.createElement(l.Fragment,null)})):l.createElement(d,{noDataText:R})))}));y.displayName="DonutChart";export{y as default};
