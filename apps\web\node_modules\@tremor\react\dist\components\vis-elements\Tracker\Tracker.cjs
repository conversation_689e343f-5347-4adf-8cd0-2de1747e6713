'use client';
"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("tslib"),r=require("../../../lib/theme.cjs"),t=require("../../../lib/tremorTwMerge.cjs"),l=require("../../../lib/utils.cjs"),a=require("react"),o=require("../../util-elements/Tooltip/Tooltip.cjs");function s(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var c=s(a);const i=l.makeClassName("Tracker"),u=c.default.forwardRef(((a,s)=>{const{color:u,tooltip:n}=a,d=e.__rest(a,["color","tooltip"]),{tooltipProps:f,getReferenceProps:m}=o.useTooltip();return c.default.createElement("div",Object.assign({ref:l.mergeRefs([s,f.refs.setReference]),className:t.tremorTwMerge(i("trackingBlock"),"w-full h-full rounded-[1px] first:rounded-l-[4px] last:rounded-r-[4px]",l.getColorClassNames(null!=u?u:"gray",r.colorPalette.background).bgColor)},d,m),c.default.createElement(o.default,Object.assign({text:n},f)))}));u.displayName="TrackerBlock";const n=c.default.forwardRef(((r,l)=>{const{data:a=[],className:o}=r,s=e.__rest(r,["data","className"]);return c.default.createElement("div",Object.assign({ref:l,className:t.tremorTwMerge(i("root"),"h-10 flex items-center space-x-0.5",o)},s),a.map(((e,r)=>{var t;return c.default.createElement(u,{key:null!==(t=e.key)&&void 0!==t?t:r,color:e.color,tooltip:e.tooltip})})))}));n.displayName="Tracker",exports.default=n,exports.makeTrackerClassName=i;
