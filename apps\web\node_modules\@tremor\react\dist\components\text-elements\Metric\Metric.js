import{__rest as r}from"tslib";import{colorPalette as t}from"../../../lib/theme.js";import{tremorTwMerge as e}from"../../../lib/tremorTwMerge.js";import{getColorClassNames as o}from"../../../lib/utils.js";import m from"react";const s=m.forwardRef(((s,a)=>{const{color:i,children:l,className:c}=s,n=r(s,["color","children","className"]);return m.createElement("p",Object.assign({ref:a,className:e("font-semibold text-tremor-metric",i?o(i,t.darkText).textColor:"text-tremor-content-strong dark:text-dark-tremor-content-strong",c)},n),l)}));s.displayName="Metric";export{s as default};
