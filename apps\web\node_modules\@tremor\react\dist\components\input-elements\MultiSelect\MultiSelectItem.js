'use client';
import{__rest as e}from"tslib";import"../../../contexts/BaseColorContext.js";import"../../../contexts/IndexContext.js";import"../../../contexts/RootStylesContext.js";import t from"../../../contexts/SelectedValueContext.js";import r,{useContext as o}from"react";import{tremorTwMerge as a}from"../../../lib/tremorTwMerge.js";import{isValueInArray as n,makeClassName as s}from"../../../lib/utils.js";import{ListboxOption as c}from"@headlessui/react";const m=s("MultiSelectItem"),d=r.forwardRef(((s,d)=>{const{value:l,className:u,children:i}=s,f=e(s,["value","className","children"]),{selectedValue:x}=o(t),p=n(l,x);return r.createElement(c,Object.assign({className:a(m("root"),"flex justify-start items-center cursor-default text-tremor-default p-2.5","data-[focus]:bg-tremor-background-muted data-[focus]:text-tremor-content-strong data-[select]ed:text-tremor-content-strong text-tremor-content-emphasis","dark:data-[focus]:bg-dark-tremor-background-muted dark:data-[focus]:text-dark-tremor-content-strong dark:data-[select]ed:text-dark-tremor-content-strong dark:data-[select]ed:bg-dark-tremor-background-muted dark:text-dark-tremor-content-emphasis",u),ref:d,key:l,value:l},f),r.createElement("input",{type:"checkbox",className:a(m("checkbox"),"flex-none focus:ring-none focus:outline-none cursor-pointer mr-2.5","accent-tremor-brand","dark:accent-dark-tremor-brand"),checked:p,readOnly:!0}),r.createElement("span",{className:"whitespace-nowrap truncate"},null!=i?i:l))}));d.displayName="MultiSelectItem";export{d as default};
