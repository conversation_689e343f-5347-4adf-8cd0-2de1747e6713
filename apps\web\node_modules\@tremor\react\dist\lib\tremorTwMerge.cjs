"use strict";Object.defineProperty(exports,"__esModule",{value:!0});const e=require("tailwind-merge").extendTailwindMerge({extend:{classGroups:{shadow:[{shadow:[{tremor:["input","card","dropdown"],"dark-tremor":["input","card","dropdown"]}]}],rounded:[{rounded:[{tremor:["small","default","full"],"dark-tremor":["small","default","full"]}]}],"font-size":[{text:[{tremor:["default","title","metric"],"dark-tremor":["default","title","metric"]}]}]}}});exports.tremorTwMerge=e;
