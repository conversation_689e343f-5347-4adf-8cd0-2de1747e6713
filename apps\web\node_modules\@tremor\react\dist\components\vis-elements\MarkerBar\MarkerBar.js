'use client';
import{__rest as e}from"tslib";import r from"react";import t,{useTooltip as a}from"../../util-elements/Tooltip/Tooltip.js";import{colorPalette as o}from"../../../lib/theme.js";import{tremorTwMerge as l}from"../../../lib/tremorTwMerge.js";import{getColorClassNames as n,makeClassName as s}from"../../../lib/utils.js";const m=s("MarkerBar"),i=r.forwardRef(((s,i)=>{const{value:d,minValue:c,maxValue:u,markerTooltip:b,rangeTooltip:f,showAnimation:g=!1,color:p,className:k}=s,v=e(s,["value","minValue","maxValue","markerTooltip","rangeTooltip","showAnimation","color","className"]),{tooltipProps:j,getReferenceProps:h}=a(),{tooltipProps:w,getReferenceProps:x}=a();return r.createElement("div",Object.assign({ref:i,className:l(m("root"),"relative flex items-center w-full rounded-tremor-full h-2","bg-tremor-background-subtle","dark:bg-dark-tremor-background-subtle",k)},v),void 0!==c&&void 0!==u?r.createElement(r.Fragment,null,r.createElement(t,Object.assign({text:f},w)),r.createElement("div",Object.assign({ref:w.refs.setReference,className:l(m("rangeBar"),"absolute h-full rounded-tremor-full","bg-tremor-content-subtle","dark:bg-dark-tremor-content-subtle"),style:{left:`${c}%`,width:u-c+"%",transition:g?"all duration-300":""}},x))):null,r.createElement(t,Object.assign({text:b},j)),r.createElement("div",Object.assign({ref:j.refs.setReference,className:l(m("markerWrapper"),"absolute right-1/2 -translate-x-1/2 w-5"),style:{left:`${d}%`,transition:g?"all 1s":""}},h),r.createElement("div",{className:l(m("marker"),"ring-2 mx-auto rounded-tremor-full h-4 w-1","ring-tremor-brand-inverted","dark:ring-dark-tremor-brand-inverted",p?n(p,o.background).bgColor:"dark:bg-dark-tremor-brand bg-tremor-brand")})))}));i.displayName="MarkerBar";export{i as default};
