"use strict";var e=require("tslib"),r=require("../../../lib/tremorTwMerge.cjs"),s=require("../../../lib/utils.cjs"),t=require("react"),m=require("./styles.cjs");function i(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var u=i(t);const l=s.makeClassName("Grid"),a=(e,r)=>e&&Object.keys(r).includes(String(e))?r[e]:"",d=u.default.forwardRef(((s,t)=>{const{numItems:i=1,numItemsSm:d,numItemsMd:n,numItemsLg:c,children:o,className:g}=s,f=e.__rest(s,["numItems","numItemsSm","numItemsMd","numItemsLg","children","className"]),I=a(i,m.gridCols),b=a(d,m.gridColsSm),j=a(n,m.gridColsMd),M=a(c,m.gridColsLg),q=r.tremorTwMerge(I,b,j,M);return u.default.createElement("div",Object.assign({ref:t,className:r.tremorTwMerge(l("root"),"grid",q,g)},f),o)}));d.displayName="Grid",module.exports=d;
