"use strict";var e=require("tslib"),r=require("../../../lib/theme.cjs"),t=require("../../../lib/tremorTwMerge.cjs"),o=require("../../../lib/utils.cjs");function s(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var l=s(require("react"));const a=l.default.forwardRef(((s,a)=>{const{color:c,children:i,className:n}=s,m=e.__rest(s,["color","children","className"]);return l.default.createElement("p",Object.assign({ref:a,className:t.tremorTwMerge("font-semibold text-tremor-metric",c?o.getColorClassNames(c,r.colorPalette.darkText).textColor:"text-tremor-content-strong dark:text-dark-tremor-content-strong",n)},m),i)}));a.displayName="Metric",module.exports=a;
