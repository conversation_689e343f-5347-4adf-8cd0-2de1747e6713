'use client';
import{__rest as e}from"tslib";import t,{useRef as r,useMemo as a,isValidElement as o,useState as l}from"react";import"../../../contexts/BaseColorContext.js";import"../../../contexts/IndexContext.js";import"../../../contexts/RootStylesContext.js";import n from"../../../contexts/SelectedValueContext.js";import s from"../../../hooks/useInternalState.js";import m from"../../../assets/ArrowDownHeadIcon.js";import d from"../../../assets/SearchIcon.js";import c from"../../../assets/XCircleIcon.js";import u from"../../../assets/XIcon.js";import{tremorTwMerge as i}from"../../../lib/tremorTwMerge.js";import{makeClassName as p}from"../../../lib/utils.js";import{getFilteredOptions as b,getSelectButtonColors as f}from"../selectUtils.js";import{Listbox as h,ListboxButton as x,Transition as k,ListboxOptions as v}from"@headlessui/react";const g=p("MultiSelect"),w=t.forwardRef(((p,w)=>{const{defaultValue:E=[],value:y,onValueChange:N,placeholder:C="Select...",placeholderSearch:j="Search",disabled:I=!1,icon:S,children:V,className:D,required:M,name:A,error:F=!1,errorMessage:q,id:R}=p,T=e(p,["defaultValue","value","onValueChange","placeholder","placeholderSearch","disabled","icon","children","className","required","name","error","errorMessage","id"]),z=r(null),B=S,[O,P]=s(E,y),{reactElementChildren:X,optionsAvailable:H}=a((()=>{const e=t.Children.toArray(V).filter(o);return{reactElementChildren:e,optionsAvailable:b("",e)}}),[V]),[K,U]=l(""),G=(null!=O?O:[]).length>0,J=a((()=>K?b(K,X):H),[K,X,H]),L=()=>{U("")};return t.createElement("div",{className:i("w-full min-w-[10rem] text-tremor-default",D)},t.createElement("div",{className:"relative"},t.createElement("select",{title:"multi-select-hidden",required:M,className:i("h-full w-full absolute left-0 top-0 -z-10 opacity-0"),value:O,onChange:e=>{e.preventDefault()},name:A,disabled:I,multiple:!0,id:R,onFocus:()=>{const e=z.current;e&&e.focus()}},t.createElement("option",{className:"hidden",value:"",disabled:!0,hidden:!0},C),J.map((e=>{const r=e.props.value,a=e.props.children;return t.createElement("option",{className:"hidden",key:r,value:r},a)}))),t.createElement(h,Object.assign({as:"div",ref:w,defaultValue:O,value:O,onChange:e=>{null==N||N(e),P(e)},disabled:I,id:R,multiple:!0},T),(({value:e})=>t.createElement(t.Fragment,null,t.createElement(x,{className:i("w-full outline-none text-left whitespace-nowrap truncate rounded-tremor-default focus:ring-2 transition duration-100 border pr-8 py-1.5","border-tremor-border shadow-tremor-input focus:border-tremor-brand-subtle focus:ring-tremor-brand-muted","dark:border-dark-tremor-border dark:shadow-dark-tremor-input dark:focus:border-dark-tremor-brand-subtle dark:focus:ring-dark-tremor-brand-muted",B?"pl-11 -ml-0.5":"pl-3",f(e.length>0,I,F)),ref:z},B&&t.createElement("span",{className:i("absolute inset-y-0 left-0 flex items-center ml-px pl-2.5")},t.createElement(B,{className:i(g("Icon"),"flex-none h-5 w-5","text-tremor-content-subtle","dark:text-dark-tremor-content-subtle")})),t.createElement("div",{className:"h-6 flex items-center"},e.length>0?t.createElement("div",{className:"flex flex-nowrap overflow-x-scroll [&::-webkit-scrollbar]:hidden [scrollbar-width:none] gap-x-1 mr-5 -ml-1.5 relative"},H.filter((t=>e.includes(t.props.value))).map(((r,a)=>{var o;return t.createElement("div",{key:a,className:i("max-w-[100px] lg:max-w-[200px] flex justify-center items-center pl-2 pr-1.5 py-1 font-medium","rounded-tremor-small","bg-tremor-background-muted dark:bg-dark-tremor-background-muted","bg-tremor-background-subtle dark:bg-dark-tremor-background-subtle","text-tremor-content-default dark:text-dark-tremor-content-default","text-tremor-content-emphasis dark:text-dark-tremor-content-emphasis")},t.createElement("div",{className:"text-xs truncate "},null!==(o=r.props.children)&&void 0!==o?o:r.props.value),t.createElement("div",{onClick:t=>{t.preventDefault();const a=e.filter((e=>e!==r.props.value));null==N||N(a),P(a)}},t.createElement(u,{className:i(g("clearIconItem"),"cursor-pointer rounded-tremor-full w-3.5 h-3.5 ml-2","text-tremor-content-subtle hover:text-tremor-content","dark:text-dark-tremor-content-subtle dark:hover:text-tremor-content")})))}))):t.createElement("span",null,C)),t.createElement("span",{className:i("absolute inset-y-0 right-0 flex items-center mr-2.5")},t.createElement(m,{className:i(g("arrowDownIcon"),"flex-none h-5 w-5","text-tremor-content-subtle","dark:text-dark-tremor-content-subtle")}))),G&&!I?t.createElement("button",{type:"button",className:i("absolute inset-y-0 right-0 flex items-center mr-8"),onClick:e=>{e.preventDefault(),P([]),null==N||N([])}},t.createElement(c,{className:i(g("clearIconAllItems"),"flex-none h-4 w-4","text-tremor-content-subtle","dark:text-dark-tremor-content-subtle")})):null,t.createElement(k,{enter:"transition ease duration-100 transform",enterFrom:"opacity-0 -translate-y-4",enterTo:"opacity-100 translate-y-0",leave:"transition ease duration-100 transform",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 -translate-y-4"},t.createElement(v,{anchor:"bottom start",className:i("z-10 divide-y w-[var(--button-width)] overflow-y-auto outline-none rounded-tremor-default max-h-[228px]  border [--anchor-gap:4px]","bg-tremor-background border-tremor-border divide-tremor-border shadow-tremor-dropdown","dark:bg-dark-tremor-background dark:border-dark-tremor-border dark:divide-dark-tremor-border dark:shadow-dark-tremor-dropdown")},t.createElement("div",{className:i("flex items-center w-full px-2.5","bg-tremor-background-muted","dark:bg-dark-tremor-background-muted")},t.createElement("span",null,t.createElement(d,{className:i("flex-none w-4 h-4 mr-2","text-tremor-content-subtle","dark:text-dark-tremor-content-subtle")})),t.createElement("input",{name:"search",type:"input",autoComplete:"off",placeholder:j,className:i("w-full focus:outline-none focus:ring-none bg-transparent text-tremor-default py-2","text-tremor-content-emphasis","dark:text-dark-tremor-content-subtle"),onKeyDown:e=>{"Space"===e.code&&""!==e.target.value&&e.stopPropagation()},onChange:e=>U(e.target.value),value:K})),t.createElement(n.Provider,Object.assign({},{onBlur:{handleResetSearch:L}},{value:{selectedValue:e}}),J))))))),F&&q?t.createElement("p",{className:i("errorMessage","text-sm text-rose-500 mt-1")},q):null)}));w.displayName="MultiSelect";export{w as default};
