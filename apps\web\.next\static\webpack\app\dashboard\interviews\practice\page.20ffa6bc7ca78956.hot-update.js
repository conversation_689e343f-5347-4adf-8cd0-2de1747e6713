"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/interviews/practice/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/interviews/practice/page.tsx":
/*!********************************************************!*\
  !*** ./src/app/dashboard/interviews/practice/page.tsx ***!
  \********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ InterviewPracticePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./src/components/ui/progress.tsx\");\n/* harmony import */ var _services_aiInterviewService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/services/aiInterviewService */ \"(app-pages-browser)/./src/services/aiInterviewService.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Award_Brain_CheckCircle_Clock_Lightbulb_Mic_MicOff_Play_RefreshCw_SkipForward_Square_Target_TrendingUp_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Award,Brain,CheckCircle,Clock,Lightbulb,Mic,MicOff,Play,RefreshCw,SkipForward,Square,Target,TrendingUp,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Award_Brain_CheckCircle_Clock_Lightbulb_Mic_MicOff_Play_RefreshCw_SkipForward_Square_Target_TrendingUp_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Award,Brain,CheckCircle,Clock,Lightbulb,Mic,MicOff,Play,RefreshCw,SkipForward,Square,Target,TrendingUp,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Award_Brain_CheckCircle_Clock_Lightbulb_Mic_MicOff_Play_RefreshCw_SkipForward_Square_Target_TrendingUp_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Award,Brain,CheckCircle,Clock,Lightbulb,Mic,MicOff,Play,RefreshCw,SkipForward,Square,Target,TrendingUp,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Award_Brain_CheckCircle_Clock_Lightbulb_Mic_MicOff_Play_RefreshCw_SkipForward_Square_Target_TrendingUp_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Award,Brain,CheckCircle,Clock,Lightbulb,Mic,MicOff,Play,RefreshCw,SkipForward,Square,Target,TrendingUp,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Award_Brain_CheckCircle_Clock_Lightbulb_Mic_MicOff_Play_RefreshCw_SkipForward_Square_Target_TrendingUp_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Award,Brain,CheckCircle,Clock,Lightbulb,Mic,MicOff,Play,RefreshCw,SkipForward,Square,Target,TrendingUp,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/video.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Award_Brain_CheckCircle_Clock_Lightbulb_Mic_MicOff_Play_RefreshCw_SkipForward_Square_Target_TrendingUp_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Award,Brain,CheckCircle,Clock,Lightbulb,Mic,MicOff,Play,RefreshCw,SkipForward,Square,Target,TrendingUp,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/video-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Award_Brain_CheckCircle_Clock_Lightbulb_Mic_MicOff_Play_RefreshCw_SkipForward_Square_Target_TrendingUp_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Award,Brain,CheckCircle,Clock,Lightbulb,Mic,MicOff,Play,RefreshCw,SkipForward,Square,Target,TrendingUp,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Award_Brain_CheckCircle_Clock_Lightbulb_Mic_MicOff_Play_RefreshCw_SkipForward_Square_Target_TrendingUp_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Award,Brain,CheckCircle,Clock,Lightbulb,Mic,MicOff,Play,RefreshCw,SkipForward,Square,Target,TrendingUp,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mic-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Award_Brain_CheckCircle_Clock_Lightbulb_Mic_MicOff_Play_RefreshCw_SkipForward_Square_Target_TrendingUp_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Award,Brain,CheckCircle,Clock,Lightbulb,Mic,MicOff,Play,RefreshCw,SkipForward,Square,Target,TrendingUp,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Award_Brain_CheckCircle_Clock_Lightbulb_Mic_MicOff_Play_RefreshCw_SkipForward_Square_Target_TrendingUp_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Award,Brain,CheckCircle,Clock,Lightbulb,Mic,MicOff,Play,RefreshCw,SkipForward,Square,Target,TrendingUp,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Award_Brain_CheckCircle_Clock_Lightbulb_Mic_MicOff_Play_RefreshCw_SkipForward_Square_Target_TrendingUp_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Award,Brain,CheckCircle,Clock,Lightbulb,Mic,MicOff,Play,RefreshCw,SkipForward,Square,Target,TrendingUp,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/skip-forward.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Award_Brain_CheckCircle_Clock_Lightbulb_Mic_MicOff_Play_RefreshCw_SkipForward_Square_Target_TrendingUp_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Award,Brain,CheckCircle,Clock,Lightbulb,Mic,MicOff,Play,RefreshCw,SkipForward,Square,Target,TrendingUp,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Award_Brain_CheckCircle_Clock_Lightbulb_Mic_MicOff_Play_RefreshCw_SkipForward_Square_Target_TrendingUp_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Award,Brain,CheckCircle,Clock,Lightbulb,Mic,MicOff,Play,RefreshCw,SkipForward,Square,Target,TrendingUp,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Award_Brain_CheckCircle_Clock_Lightbulb_Mic_MicOff_Play_RefreshCw_SkipForward_Square_Target_TrendingUp_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Award,Brain,CheckCircle,Clock,Lightbulb,Mic,MicOff,Play,RefreshCw,SkipForward,Square,Target,TrendingUp,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Award_Brain_CheckCircle_Clock_Lightbulb_Mic_MicOff_Play_RefreshCw_SkipForward_Square_Target_TrendingUp_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Award,Brain,CheckCircle,Clock,Lightbulb,Mic,MicOff,Play,RefreshCw,SkipForward,Square,Target,TrendingUp,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Award_Brain_CheckCircle_Clock_Lightbulb_Mic_MicOff_Play_RefreshCw_SkipForward_Square_Target_TrendingUp_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Award,Brain,CheckCircle,Clock,Lightbulb,Mic,MicOff,Play,RefreshCw,SkipForward,Square,Target,TrendingUp,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Award_Brain_CheckCircle_Clock_Lightbulb_Mic_MicOff_Play_RefreshCw_SkipForward_Square_Target_TrendingUp_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Award,Brain,CheckCircle,Clock,Lightbulb,Mic,MicOff,Play,RefreshCw,SkipForward,Square,Target,TrendingUp,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction InterviewPracticePage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const videoRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const mediaRecorderRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [stream, setStream] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [interviewState, setInterviewState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isActive: false,\n        currentQuestionIndex: 0,\n        isRecording: false,\n        isVideoEnabled: true,\n        isAudioEnabled: true,\n        responses: []\n    });\n    const [questions, setQuestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentAnalysis, setCurrentAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isAnalyzing, setIsAnalyzing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sessionScore, setSessionScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [timeElapsed, setTimeElapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadQuestions();\n        return ()=>{\n            if (stream) {\n                stream.getTracks().forEach((track)=>track.stop());\n            }\n        };\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        let interval;\n        if (interviewState.isActive && interviewState.startTime) {\n            interval = setInterval(()=>{\n                setTimeElapsed(Math.floor((Date.now() - interviewState.startTime.getTime()) / 1000));\n            }, 1000);\n        }\n        return ()=>clearInterval(interval);\n    }, [\n        interviewState.isActive,\n        interviewState.startTime\n    ]);\n    const loadQuestions = async ()=>{\n        try {\n            const generatedQuestions = await _services_aiInterviewService__WEBPACK_IMPORTED_MODULE_7__.aiInterviewService.generateQuestions({\n                jobTitle: \"Software Engineer\",\n                industry: \"Technology\",\n                difficulty: \"medium\",\n                count: 5,\n                types: [\n                    \"behavioral\",\n                    \"technical\",\n                    \"situational\"\n                ]\n            });\n            setQuestions(generatedQuestions);\n        } catch (error) {\n            console.error(\"Error loading questions:\", error);\n        }\n    };\n    const initializeMedia = async ()=>{\n        try {\n            const mediaStream = await navigator.mediaDevices.getUserMedia({\n                video: interviewState.isVideoEnabled,\n                audio: interviewState.isAudioEnabled\n            });\n            setStream(mediaStream);\n            if (videoRef.current && interviewState.isVideoEnabled) {\n                videoRef.current.srcObject = mediaStream;\n            }\n            return mediaStream;\n        } catch (error) {\n            console.error(\"Error accessing media devices:\", error);\n            throw error;\n        }\n    };\n    const startInterview = async ()=>{\n        try {\n            const mediaStream = await initializeMedia();\n            setInterviewState((prev)=>({\n                    ...prev,\n                    isActive: true,\n                    startTime: new Date()\n                }));\n        } catch (error) {\n            console.error(\"Error starting interview:\", error);\n        }\n    };\n    const startRecording = async ()=>{\n        if (!stream) return;\n        try {\n            const mediaRecorder = new MediaRecorder(stream);\n            const chunks = [];\n            mediaRecorder.ondataavailable = (event)=>{\n                if (event.data.size > 0) {\n                    chunks.push(event.data);\n                }\n            };\n            mediaRecorder.onstop = async ()=>{\n                const blob = new Blob(chunks, {\n                    type: \"video/webm\"\n                });\n                const audioUrl = URL.createObjectURL(blob);\n                // Simulate response analysis\n                await analyzeResponse(audioUrl);\n            };\n            mediaRecorder.start();\n            mediaRecorderRef.current = mediaRecorder;\n            setInterviewState((prev)=>({\n                    ...prev,\n                    isRecording: true\n                }));\n        } catch (error) {\n            console.error(\"Error starting recording:\", error);\n        }\n    };\n    const stopRecording = ()=>{\n        if (mediaRecorderRef.current && interviewState.isRecording) {\n            mediaRecorderRef.current.stop();\n            setInterviewState((prev)=>({\n                    ...prev,\n                    isRecording: false\n                }));\n        }\n    };\n    const analyzeResponse = async (audioUrl)=>{\n        if (questions.length === 0) return;\n        setIsAnalyzing(true);\n        try {\n            const currentQuestion = questions[interviewState.currentQuestionIndex];\n            const mockResponse = \"I believe this is a great opportunity to demonstrate my problem-solving skills. In my previous role, I encountered a similar situation where I had to work with a team to deliver a critical project. I took the initiative to organize daily standups and implemented a new tracking system that improved our efficiency by 25%. The result was that we delivered the project two weeks ahead of schedule and received positive feedback from stakeholders.\";\n            const analysis = await _services_aiInterviewService__WEBPACK_IMPORTED_MODULE_7__.aiInterviewService.analyzeResponse({\n                question: currentQuestion,\n                response: mockResponse,\n                audioUrl,\n                duration: 120\n            });\n            const newResponse = {\n                questionId: currentQuestion.id,\n                response: mockResponse,\n                duration: 120,\n                audioUrl,\n                analysis\n            };\n            setInterviewState((prev)=>({\n                    ...prev,\n                    responses: [\n                        ...prev.responses,\n                        newResponse\n                    ]\n                }));\n            setCurrentAnalysis(analysis);\n            // Update session score\n            const allResponses = [\n                ...interviewState.responses,\n                newResponse\n            ];\n            const avgScore = allResponses.reduce((sum, r)=>{\n                var _r_analysis;\n                return sum + (((_r_analysis = r.analysis) === null || _r_analysis === void 0 ? void 0 : _r_analysis.score) || 0);\n            }, 0) / allResponses.length;\n            setSessionScore(avgScore);\n        } catch (error) {\n            console.error(\"Error analyzing response:\", error);\n        } finally{\n            setIsAnalyzing(false);\n        }\n    };\n    const nextQuestion = ()=>{\n        if (interviewState.currentQuestionIndex < questions.length - 1) {\n            setInterviewState((prev)=>({\n                    ...prev,\n                    currentQuestionIndex: prev.currentQuestionIndex + 1\n                }));\n            setCurrentAnalysis(null);\n        } else {\n            finishInterview();\n        }\n    };\n    const finishInterview = async ()=>{\n        if (stream) {\n            stream.getTracks().forEach((track)=>track.stop());\n        }\n        // Generate final feedback\n        try {\n            const feedback = await _services_aiInterviewService__WEBPACK_IMPORTED_MODULE_7__.aiInterviewService.generateFeedback(interviewState.responses);\n            // Store results in sessionStorage for the results page\n            sessionStorage.setItem(\"interviewResults\", JSON.stringify({\n                score: sessionScore,\n                feedback,\n                responses: interviewState.responses,\n                duration: timeElapsed\n            }));\n            router.push(\"/dashboard/interviews/results\");\n        } catch (error) {\n            console.error(\"Error generating feedback:\", error);\n            router.push(\"/dashboard/interviews\");\n        }\n    };\n    const toggleVideo = ()=>{\n        setInterviewState((prev)=>({\n                ...prev,\n                isVideoEnabled: !prev.isVideoEnabled\n            }));\n        if (stream) {\n            const videoTrack = stream.getVideoTracks()[0];\n            if (videoTrack) {\n                videoTrack.enabled = !interviewState.isVideoEnabled;\n            }\n        }\n    };\n    const toggleAudio = ()=>{\n        setInterviewState((prev)=>({\n                ...prev,\n                isAudioEnabled: !prev.isAudioEnabled\n            }));\n        if (stream) {\n            const audioTrack = stream.getAudioTracks()[0];\n            if (audioTrack) {\n                audioTrack.enabled = !interviewState.isAudioEnabled;\n            }\n        }\n    };\n    const formatTime = (seconds)=>{\n        const mins = Math.floor(seconds / 60);\n        const secs = seconds % 60;\n        return \"\".concat(mins.toString().padStart(2, \"0\"), \":\").concat(secs.toString().padStart(2, \"0\"));\n    };\n    const getScoreColor = (score)=>{\n        if (score >= 80) return \"text-emerald-600 dark:text-emerald-400\";\n        if (score >= 70) return \"text-amber-600 dark:text-amber-400\";\n        return \"text-red-600 dark:text-red-400\";\n    };\n    const currentQuestion = questions[interviewState.currentQuestionIndex];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-6xl mx-auto space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: ()=>router.back(),\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Award_Brain_CheckCircle_Clock_Lightbulb_Mic_MicOff_Play_RefreshCw_SkipForward_Square_Target_TrendingUp_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Back\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900 flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Award_Brain_CheckCircle_Clock_Lightbulb_Mic_MicOff_Play_RefreshCw_SkipForward_Square_Target_TrendingUp_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-8 w-8 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"AI Interview Practice\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mt-1\",\n                                        children: \"Practice with real-time AI feedback and analysis\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                        lineNumber: 282,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Award_Brain_CheckCircle_Clock_Lightbulb_Mic_MicOff_Play_RefreshCw_SkipForward_Square_Target_TrendingUp_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-4 w-4 text-gray-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-mono text-lg\",\n                                        children: formatTime(timeElapsed)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 11\n                            }, this),\n                            sessionScore > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Award_Brain_CheckCircle_Clock_Lightbulb_Mic_MicOff_Play_RefreshCw_SkipForward_Square_Target_TrendingUp_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-4 w-4 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-bold \".concat(getScoreColor(sessionScore)),\n                                        children: [\n                                            sessionScore.toFixed(0),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                        lineNumber: 302,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                lineNumber: 281,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                    className: \"pt-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium\",\n                                    children: [\n                                        \"Question \",\n                                        interviewState.currentQuestionIndex + 1,\n                                        \" of \",\n                                        questions.length\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                    variant: \"outline\",\n                                    children: [\n                                        Math.round((interviewState.currentQuestionIndex + 1) / questions.length * 100),\n                                        \"% Complete\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                            lineNumber: 321,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_6__.Progress, {\n                            value: (interviewState.currentQuestionIndex + 1) / questions.length * 100,\n                            className: \"h-2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                            lineNumber: 329,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                    lineNumber: 320,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                lineNumber: 319,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Award_Brain_CheckCircle_Clock_Lightbulb_Mic_MicOff_Play_RefreshCw_SkipForward_Square_Target_TrendingUp_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-5 w-5 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Interview Session\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative bg-gray-900 rounded-lg overflow-hidden aspect-video\",\n                                                children: [\n                                                    interviewState.isVideoEnabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                        ref: videoRef,\n                                                        autoPlay: true,\n                                                        muted: true,\n                                                        className: \"w-full h-full object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 19\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center h-full\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Award_Brain_CheckCircle_Clock_Lightbulb_Mic_MicOff_Play_RefreshCw_SkipForward_Square_Target_TrendingUp_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-16 w-16 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                            lineNumber: 356,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                        lineNumber: 355,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    interviewState.isRecording && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-4 left-4 flex items-center space-x-2 bg-red-600 text-white px-3 py-1 rounded-full\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-white rounded-full animate-pulse\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                lineNumber: 363,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Recording\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                lineNumber: 364,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                        lineNumber: 362,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute bottom-4 left-1/2 transform -translate-x-1/2 flex items-center space-x-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: interviewState.isVideoEnabled ? \"default\" : \"secondary\",\n                                                                onClick: toggleVideo,\n                                                                children: interviewState.isVideoEnabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Award_Brain_CheckCircle_Clock_Lightbulb_Mic_MicOff_Play_RefreshCw_SkipForward_Square_Target_TrendingUp_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                    lineNumber: 375,\n                                                                    columnNumber: 54\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Award_Brain_CheckCircle_Clock_Lightbulb_Mic_MicOff_Play_RefreshCw_SkipForward_Square_Target_TrendingUp_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                    lineNumber: 375,\n                                                                    columnNumber: 86\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                lineNumber: 370,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: interviewState.isAudioEnabled ? \"default\" : \"secondary\",\n                                                                onClick: toggleAudio,\n                                                                children: interviewState.isAudioEnabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Award_Brain_CheckCircle_Clock_Lightbulb_Mic_MicOff_Play_RefreshCw_SkipForward_Square_Target_TrendingUp_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                    lineNumber: 382,\n                                                                    columnNumber: 54\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Award_Brain_CheckCircle_Clock_Lightbulb_Mic_MicOff_Play_RefreshCw_SkipForward_Square_Target_TrendingUp_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                    lineNumber: 382,\n                                                                    columnNumber: 84\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                lineNumber: 377,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                lineNumber: 346,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-4\",\n                                                children: !interviewState.isActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    onClick: startInterview,\n                                                    size: \"lg\",\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Award_Brain_CheckCircle_Clock_Lightbulb_Mic_MicOff_Play_RefreshCw_SkipForward_Square_Target_TrendingUp_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                            lineNumber: 391,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Start Interview\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                            lineNumber: 392,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                    lineNumber: 390,\n                                                    columnNumber: 19\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        !interviewState.isRecording ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            onClick: startRecording,\n                                                            size: \"lg\",\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Award_Brain_CheckCircle_Clock_Lightbulb_Mic_MicOff_Play_RefreshCw_SkipForward_Square_Target_TrendingUp_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"h-5 w-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                    lineNumber: 398,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Start Recording\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                    lineNumber: 399,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                            lineNumber: 397,\n                                                            columnNumber: 23\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            onClick: stopRecording,\n                                                            size: \"lg\",\n                                                            variant: \"destructive\",\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Award_Brain_CheckCircle_Clock_Lightbulb_Mic_MicOff_Play_RefreshCw_SkipForward_Square_Target_TrendingUp_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-5 w-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                    lineNumber: 403,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Stop Recording\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                    lineNumber: 404,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                            lineNumber: 402,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            onClick: nextQuestion,\n                                                            variant: \"outline\",\n                                                            size: \"lg\",\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Award_Brain_CheckCircle_Clock_Lightbulb_Mic_MicOff_Play_RefreshCw_SkipForward_Square_Target_TrendingUp_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"h-5 w-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                    lineNumber: 408,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Next Question\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                    lineNumber: 409,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                            lineNumber: 407,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                        lineNumber: 344,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 11\n                            }, this),\n                            currentQuestion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Interview Question\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                            lineNumber: 423,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                        lineNumber: 422,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: \"outline\",\n                                                        children: currentQuestion.type\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                        lineNumber: 425,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                lineNumber: 421,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                children: [\n                                                    \"Category: \",\n                                                    currentQuestion.category,\n                                                    \" • Expected time: \",\n                                                    currentQuestion.expectedDuration,\n                                                    \"s\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                lineNumber: 427,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                        lineNumber: 420,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-lg font-medium text-gray-900 p-4 bg-blue-50 rounded-lg\",\n                                                children: currentQuestion.question\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 17\n                                            }, this),\n                                            currentQuestion.tips && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-sm flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Award_Brain_CheckCircle_Clock_Lightbulb_Mic_MicOff_Play_RefreshCw_SkipForward_Square_Target_TrendingUp_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"h-4 w-4 text-yellow-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                lineNumber: 439,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Tips for answering:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                lineNumber: 440,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                        lineNumber: 438,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"text-sm text-gray-600 space-y-1\",\n                                                        children: currentQuestion.tips.map((tip, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                className: \"flex items-start space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-blue-600\",\n                                                                        children: \"•\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                        lineNumber: 445,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: tip\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                        lineNumber: 446,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                lineNumber: 444,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                        lineNumber: 442,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                lineNumber: 437,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                lineNumber: 419,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"sticky top-8 space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Award_Brain_CheckCircle_Clock_Lightbulb_Mic_MicOff_Play_RefreshCw_SkipForward_Square_Target_TrendingUp_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-5 w-5 text-purple-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                        lineNumber: 464,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"AI Analysis\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                        lineNumber: 465,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                lineNumber: 463,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                            lineNumber: 462,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            children: isAnalyzing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center py-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Award_Brain_CheckCircle_Clock_Lightbulb_Mic_MicOff_Play_RefreshCw_SkipForward_Square_Target_TrendingUp_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-8 w-8 animate-spin text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                        lineNumber: 471,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2\",\n                                                        children: \"Analyzing response...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                        lineNumber: 472,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                lineNumber: 470,\n                                                columnNumber: 19\n                                            }, this) : currentAnalysis ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Response Score\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                lineNumber: 477,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-lg font-bold \".concat(getScoreColor(currentAnalysis.score)),\n                                                                children: [\n                                                                    currentAnalysis.score,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                lineNumber: 478,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                        lineNumber: 476,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Clarity\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                        lineNumber: 485,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            currentAnalysis.clarity,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                        lineNumber: 486,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                lineNumber: 484,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_6__.Progress, {\n                                                                value: currentAnalysis.clarity,\n                                                                className: \"h-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                lineNumber: 488,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                        lineNumber: 483,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Structure\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                        lineNumber: 493,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            currentAnalysis.structure,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                        lineNumber: 494,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                lineNumber: 492,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_6__.Progress, {\n                                                                value: currentAnalysis.structure,\n                                                                className: \"h-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                lineNumber: 496,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Relevance\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                        lineNumber: 501,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            currentAnalysis.relevance,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                        lineNumber: 502,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                lineNumber: 500,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_6__.Progress, {\n                                                                value: currentAnalysis.relevance,\n                                                                className: \"h-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                lineNumber: 504,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                        lineNumber: 499,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    currentAnalysis.strengths.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-sm text-green-600 mb-2\",\n                                                                children: \"Strengths:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                lineNumber: 509,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"text-xs text-gray-600 space-y-1\",\n                                                                children: currentAnalysis.strengths.map((strength, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        className: \"flex items-start space-x-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Award_Brain_CheckCircle_Clock_Lightbulb_Mic_MicOff_Play_RefreshCw_SkipForward_Square_Target_TrendingUp_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                className: \"h-3 w-3 text-green-600 mt-0.5\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                                lineNumber: 513,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: strength\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                                lineNumber: 514,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, index, true, {\n                                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                        lineNumber: 512,\n                                                                        columnNumber: 29\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                lineNumber: 510,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                        lineNumber: 508,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    currentAnalysis.improvements.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-sm text-orange-600 mb-2\",\n                                                                children: \"Improvements:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                lineNumber: 523,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"text-xs text-gray-600 space-y-1\",\n                                                                children: currentAnalysis.improvements.map((improvement, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        className: \"flex items-start space-x-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Award_Brain_CheckCircle_Clock_Lightbulb_Mic_MicOff_Play_RefreshCw_SkipForward_Square_Target_TrendingUp_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                className: \"h-3 w-3 text-orange-600 mt-0.5\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                                lineNumber: 527,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: improvement\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                                lineNumber: 528,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, index, true, {\n                                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                        lineNumber: 526,\n                                                                        columnNumber: 29\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                lineNumber: 524,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                        lineNumber: 522,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                lineNumber: 475,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8 text-gray-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Award_Brain_CheckCircle_Clock_Lightbulb_Mic_MicOff_Play_RefreshCw_SkipForward_Square_Target_TrendingUp_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-12 w-12 mx-auto mb-2 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                        lineNumber: 537,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: \"Start recording to get AI feedback\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                        lineNumber: 538,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                lineNumber: 536,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                            lineNumber: 468,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                    lineNumber: 461,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Award_Brain_CheckCircle_Clock_Lightbulb_Mic_MicOff_Play_RefreshCw_SkipForward_Square_Target_TrendingUp_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        className: \"h-5 w-5 text-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                        lineNumber: 548,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Session Progress\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                        lineNumber: 549,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                lineNumber: 547,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                            lineNumber: 546,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold \".concat(getScoreColor(sessionScore)),\n                                                            children: [\n                                                                sessionScore.toFixed(0),\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                            lineNumber: 554,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"Average Score\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                            lineNumber: 557,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                    lineNumber: 553,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Questions Completed\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                    lineNumber: 562,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        interviewState.responses.length,\n                                                                        \"/\",\n                                                                        questions.length\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                    lineNumber: 563,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                            lineNumber: 561,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_6__.Progress, {\n                                                            value: interviewState.responses.length / questions.length * 100,\n                                                            className: \"h-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                            lineNumber: 565,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                    lineNumber: 560,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-lg font-bold text-blue-600\",\n                                                            children: formatTime(timeElapsed)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                            lineNumber: 569,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"Time Elapsed\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                            lineNumber: 570,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                    lineNumber: 568,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                            lineNumber: 552,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                    lineNumber: 545,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Award_Brain_CheckCircle_Clock_Lightbulb_Mic_MicOff_Play_RefreshCw_SkipForward_Square_Target_TrendingUp_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                        className: \"h-5 w-5 text-yellow-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                        lineNumber: 579,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Quick Tips\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                        lineNumber: 580,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                lineNumber: 578,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                            lineNumber: 577,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Award_Brain_CheckCircle_Clock_Lightbulb_Mic_MicOff_Play_RefreshCw_SkipForward_Square_Target_TrendingUp_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"h-4 w-4 text-green-600 mt-0.5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                lineNumber: 586,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Maintain eye contact with the camera\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                lineNumber: 587,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                        lineNumber: 585,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Award_Brain_CheckCircle_Clock_Lightbulb_Mic_MicOff_Play_RefreshCw_SkipForward_Square_Target_TrendingUp_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"h-4 w-4 text-green-600 mt-0.5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                lineNumber: 590,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Use the STAR method for behavioral questions\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                lineNumber: 591,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                        lineNumber: 589,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Award_Brain_CheckCircle_Clock_Lightbulb_Mic_MicOff_Play_RefreshCw_SkipForward_Square_Target_TrendingUp_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"h-4 w-4 text-green-600 mt-0.5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                lineNumber: 594,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Speak clearly and at a moderate pace\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                lineNumber: 595,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                        lineNumber: 593,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Award_Brain_CheckCircle_Clock_Lightbulb_Mic_MicOff_Play_RefreshCw_SkipForward_Square_Target_TrendingUp_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"h-4 w-4 text-green-600 mt-0.5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                lineNumber: 598,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Include specific examples and results\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                                lineNumber: 599,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                        lineNumber: 597,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                                lineNumber: 584,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                            lineNumber: 583,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                                    lineNumber: 576,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                            lineNumber: 459,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                        lineNumber: 458,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n                lineNumber: 333,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\interviews\\\\practice\\\\page.tsx\",\n        lineNumber: 279,\n        columnNumber: 5\n    }, this);\n}\n_s(InterviewPracticePage, \"OIxBV0/w0F+MMXOzx6fsYrD2n98=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = InterviewPracticePage;\nvar _c;\n$RefreshReg$(_c, \"InterviewPracticePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/interviews/practice/page.tsx\n"));

/***/ })

});