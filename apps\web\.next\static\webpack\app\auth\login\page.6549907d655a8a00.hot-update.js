"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/login/page",{

/***/ "(app-pages-browser)/./src/lib/mockApi.ts":
/*!****************************!*\
  !*** ./src/lib/mockApi.ts ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MockApiClient: function() { return /* binding */ MockApiClient; },\n/* harmony export */   mockApiClient: function() { return /* binding */ mockApiClient; }\n/* harmony export */ });\n// Mock data\nconst mockUser = {\n    id: \"user-123\",\n    email: \"<EMAIL>\",\n    firstName: \"John\",\n    lastName: \"Doe\",\n    avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face\",\n    role: \"user\",\n    createdAt: new Date(\"2024-01-01\"),\n    updatedAt: new Date(),\n    preferences: {\n        notifications: true,\n        theme: \"light\",\n        language: \"en\"\n    }\n};\nconst mockSessions = [\n    {\n        id: \"session-1\",\n        userId: \"user-123\",\n        jobTitle: \"Senior Software Engineer\",\n        company: \"Google\",\n        industry: \"Technology\",\n        status: \"completed\",\n        createdAt: new Date(\"2024-01-15\"),\n        updatedAt: new Date(\"2024-01-15\"),\n        config: {\n            duration: 45,\n            questionTypes: [\n                \"technical\",\n                \"behavioral\"\n            ],\n            difficulty: \"medium\",\n            recordingEnabled: true\n        },\n        questions: [],\n        answers: [],\n        performanceMetrics: {\n            overallScore: 85,\n            categoryScores: {\n                technical: 82,\n                behavioral: 88,\n                communication: 85,\n                problemSolving: 83\n            },\n            strengths: [\n                \"Clear communication\",\n                \"Strong technical knowledge\"\n            ],\n            improvements: [\n                \"More specific examples\",\n                \"Better structure\"\n            ],\n            recommendations: [\n                \"Practice system design\",\n                \"Prepare more STAR examples\"\n            ]\n        }\n    },\n    {\n        id: \"session-2\",\n        userId: \"user-123\",\n        jobTitle: \"Product Manager\",\n        company: \"Meta\",\n        industry: \"Technology\",\n        status: \"completed\",\n        createdAt: new Date(\"2024-01-12\"),\n        updatedAt: new Date(\"2024-01-12\"),\n        config: {\n            duration: 60,\n            questionTypes: [\n                \"behavioral\",\n                \"case-study\"\n            ],\n            difficulty: \"hard\",\n            recordingEnabled: true\n        },\n        questions: [],\n        answers: [],\n        performanceMetrics: {\n            overallScore: 78,\n            categoryScores: {\n                strategic: 80,\n                analytical: 75,\n                communication: 82,\n                leadership: 76\n            },\n            strengths: [\n                \"Strategic thinking\",\n                \"Good communication\"\n            ],\n            improvements: [\n                \"Data analysis depth\",\n                \"Leadership examples\"\n            ],\n            recommendations: [\n                \"Practice case studies\",\n                \"Prepare leadership stories\"\n            ]\n        }\n    },\n    {\n        id: \"session-3\",\n        userId: \"user-123\",\n        jobTitle: \"Data Scientist\",\n        company: \"Netflix\",\n        industry: \"Technology\",\n        status: \"in-progress\",\n        createdAt: new Date(\"2024-01-20\"),\n        updatedAt: new Date(\"2024-01-20\"),\n        config: {\n            duration: 30,\n            questionTypes: [\n                \"technical\",\n                \"behavioral\"\n            ],\n            difficulty: \"medium\",\n            recordingEnabled: false\n        },\n        questions: [],\n        answers: []\n    }\n];\nconst mockQuestions = [\n    {\n        id: \"q1\",\n        text: \"Tell me about a challenging project you worked on recently.\",\n        type: \"behavioral\",\n        category: \"experience\",\n        difficulty: \"medium\",\n        timeLimit: 180,\n        followUpQuestions: [\n            \"What was the biggest challenge?\",\n            \"How did you overcome it?\",\n            \"What would you do differently?\"\n        ]\n    },\n    {\n        id: \"q2\",\n        text: \"How would you design a URL shortener like bit.ly?\",\n        type: \"technical\",\n        category: \"system-design\",\n        difficulty: \"hard\",\n        timeLimit: 300,\n        followUpQuestions: [\n            \"How would you handle scale?\",\n            \"What about analytics?\",\n            \"How would you prevent abuse?\"\n        ]\n    }\n];\n// Mock API delay\nconst delay = (ms)=>new Promise((resolve)=>setTimeout(resolve, ms));\nclass MockApiClient {\n    // Authentication methods\n    async login(credentials) {\n        await delay(1000);\n        // Validate email format\n        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n        if (!emailRegex.test(credentials.email)) {\n            throw new Error(\"Invalid email format\");\n        }\n        // Validate password (minimum 6 characters for demo)\n        if (!credentials.password || credentials.password.length < 6) {\n            throw new Error(\"Password must be at least 6 characters\");\n        }\n        // For demo purposes, accept any valid email/password combination\n        // In production, this would validate against a real database\n        this.token = \"mock-jwt-token\";\n        // Create user object based on email\n        const emailParts = credentials.email.split(\"@\")[0].split(\".\");\n        const firstName = emailParts[0] ? emailParts[0].charAt(0).toUpperCase() + emailParts[0].slice(1) : \"User\";\n        const lastName = emailParts[1] ? emailParts[1].charAt(0).toUpperCase() + emailParts[1].slice(1) : \"Demo\";\n        const user = {\n            ...mockUser,\n            email: credentials.email,\n            firstName,\n            lastName\n        };\n        return {\n            user,\n            token: this.token,\n            refreshToken: \"mock-refresh-token\"\n        };\n    }\n    async register(userData) {\n        await delay(1000);\n        // Validate email format\n        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n        if (!emailRegex.test(userData.email)) {\n            throw new Error(\"Invalid email format\");\n        }\n        // Validate password\n        if (!userData.password || userData.password.length < 6) {\n            throw new Error(\"Password must be at least 6 characters\");\n        }\n        // Validate required fields\n        if (!userData.firstName || !userData.lastName) {\n            throw new Error(\"First name and last name are required\");\n        }\n        const newUser = {\n            ...mockUser,\n            id: \"user-\".concat(Date.now()),\n            email: userData.email,\n            firstName: userData.firstName,\n            lastName: userData.lastName,\n            createdAt: new Date(),\n            updatedAt: new Date()\n        };\n        this.token = \"mock-jwt-token\";\n        return {\n            user: newUser,\n            token: this.token,\n            refreshToken: \"mock-refresh-token\"\n        };\n    }\n    async logout() {\n        await delay(500);\n        this.token = null;\n    }\n    async getCurrentUser() {\n        await delay(500);\n        if (!this.token) throw new Error(\"Not authenticated\");\n        return mockUser;\n    }\n    async updateProfile(data) {\n        await delay(1000);\n        return {\n            ...mockUser,\n            ...data,\n            updatedAt: new Date()\n        };\n    }\n    // Interview methods\n    async createInterviewSession(config) {\n        await delay(1000);\n        const newSession = {\n            id: \"session-\".concat(Date.now()),\n            userId: mockUser.id,\n            jobTitle: config.jobTitle || \"Software Engineer\",\n            company: config.company || \"Tech Company\",\n            industry: config.industry || \"Technology\",\n            status: \"created\",\n            createdAt: new Date(),\n            updatedAt: new Date(),\n            config,\n            questions: mockQuestions,\n            answers: []\n        };\n        mockSessions.unshift(newSession);\n        return newSession;\n    }\n    async getInterviewSessions() {\n        await delay(800);\n        return mockSessions;\n    }\n    async getInterviewSession(sessionId) {\n        await delay(500);\n        const session = mockSessions.find((s)=>s.id === sessionId);\n        if (!session) throw new Error(\"Session not found\");\n        return session;\n    }\n    async startInterviewSession(sessionId) {\n        await delay(1000);\n        const session = mockSessions.find((s)=>s.id === sessionId);\n        if (!session) throw new Error(\"Session not found\");\n        session.status = \"in-progress\";\n        session.startedAt = new Date();\n        return mockQuestions[0];\n    }\n    async submitAnswer(sessionId, data) {\n        await delay(2000) // Simulate AI processing time\n        ;\n        const feedback = {\n            id: \"feedback-\".concat(Date.now()),\n            questionId: data.questionId,\n            score: Math.floor(Math.random() * 30) + 70,\n            strengths: [\n                \"Clear communication\",\n                \"Good structure\",\n                \"Relevant examples\"\n            ],\n            improvements: [\n                \"More specific details\",\n                \"Better time management\",\n                \"Stronger conclusion\"\n            ],\n            suggestions: [\n                \"Use the STAR method\",\n                \"Practice with a timer\",\n                \"Prepare more examples\"\n            ],\n            createdAt: new Date()\n        };\n        const nextQuestion = mockQuestions[1] // Return next question or undefined if last\n        ;\n        return {\n            feedback,\n            nextQuestion\n        };\n    }\n    async completeInterviewSession(sessionId) {\n        await delay(1500);\n        const session = mockSessions.find((s)=>s.id === sessionId);\n        if (!session) throw new Error(\"Session not found\");\n        session.status = \"completed\";\n        session.completedAt = new Date();\n        const metrics = {\n            overallScore: Math.floor(Math.random() * 25) + 75,\n            categoryScores: {\n                technical: Math.floor(Math.random() * 30) + 70,\n                behavioral: Math.floor(Math.random() * 30) + 70,\n                communication: Math.floor(Math.random() * 30) + 70,\n                problemSolving: Math.floor(Math.random() * 30) + 70\n            },\n            strengths: [\n                \"Strong technical knowledge\",\n                \"Clear communication\",\n                \"Good problem-solving approach\"\n            ],\n            improvements: [\n                \"More detailed examples\",\n                \"Better time management\",\n                \"Stronger closing statements\"\n            ],\n            recommendations: [\n                \"Practice more behavioral questions\",\n                \"Work on system design skills\",\n                \"Prepare industry-specific examples\"\n            ]\n        };\n        session.performanceMetrics = metrics;\n        return metrics;\n    }\n    async getSessionResults(sessionId) {\n        await delay(1000);\n        const session = mockSessions.find((s)=>s.id === sessionId);\n        if (!session) throw new Error(\"Session not found\");\n        const mockFeedback = [\n            {\n                id: \"feedback-1\",\n                questionId: \"q1\",\n                score: 85,\n                strengths: [\n                    \"Clear structure\",\n                    \"Good examples\"\n                ],\n                improvements: [\n                    \"More specific metrics\"\n                ],\n                suggestions: [\n                    \"Use STAR method\"\n                ],\n                createdAt: new Date()\n            }\n        ];\n        return {\n            session,\n            metrics: session.performanceMetrics,\n            feedback: mockFeedback\n        };\n    }\n    // Resume methods\n    async uploadResume(file) {\n        await delay(2000);\n        return {\n            id: \"resume-\".concat(Date.now()),\n            userId: mockUser.id,\n            filename: file.name,\n            originalName: file.name,\n            size: file.size,\n            mimeType: file.type,\n            url: URL.createObjectURL(file),\n            extractedText: \"Mock extracted text from resume...\",\n            analysis: {\n                atsScore: 85,\n                keywords: [\n                    \"JavaScript\",\n                    \"React\",\n                    \"Node.js\",\n                    \"Python\"\n                ],\n                suggestions: [\n                    \"Add more quantifiable achievements\",\n                    \"Include relevant certifications\"\n                ]\n            },\n            createdAt: new Date(),\n            updatedAt: new Date()\n        };\n    }\n    async getResumes() {\n        await delay(500);\n        return [];\n    }\n    async analyzeResume(resumeId) {\n        await delay(3000);\n        return {\n            atsScore: Math.floor(Math.random() * 30) + 70,\n            keywords: [\n                \"JavaScript\",\n                \"React\",\n                \"Node.js\",\n                \"Python\",\n                \"AWS\"\n            ],\n            suggestions: [\n                \"Add more quantifiable achievements\",\n                \"Include relevant certifications\",\n                \"Optimize for ATS keywords\",\n                \"Improve formatting consistency\"\n            ]\n        };\n    }\n    // Expert methods\n    async getExperts() {\n        await delay(1000);\n        return [];\n    }\n    async bookExpertSession(expertId, data) {\n        await delay(1000);\n        return {\n            success: true,\n            bookingId: \"booking-\".concat(Date.now())\n        };\n    }\n    // Analytics methods\n    async getAnalytics() {\n        await delay(1000);\n        // Return mock analytics data\n        return {\n            totalSessions: mockSessions.length,\n            averageScore: 82,\n            improvementRate: 15,\n            timeSpent: 750,\n            categoryBreakdown: {\n                technical: 80,\n                behavioral: 85,\n                communication: 83,\n                problemSolving: 78\n            },\n            recentActivity: [\n                {\n                    date: \"2024-01-20\",\n                    sessions: 2,\n                    score: 85\n                },\n                {\n                    date: \"2024-01-19\",\n                    sessions: 1,\n                    score: 78\n                },\n                {\n                    date: \"2024-01-18\",\n                    sessions: 3,\n                    score: 82\n                }\n            ],\n            trends: {\n                scoreImprovement: 12,\n                consistencyRating: 85,\n                strongestArea: \"Communication\",\n                weakestArea: \"Technical\"\n            }\n        };\n    }\n    // AI methods\n    async generateQuestions(data) {\n        await delay(2000);\n        return mockQuestions;\n    }\n    async analyzeAnswer(data) {\n        await delay(1500);\n        return {\n            id: \"feedback-\".concat(Date.now()),\n            questionId: data.questionId,\n            score: Math.floor(Math.random() * 30) + 70,\n            strengths: [\n                \"Clear communication\",\n                \"Good structure\"\n            ],\n            improvements: [\n                \"More specific examples\"\n            ],\n            suggestions: [\n                \"Use STAR method\",\n                \"Practice timing\"\n            ],\n            createdAt: new Date()\n        };\n    }\n    async analyzeEmotion(data) {\n        await delay(1000);\n        return {\n            confidence: 0.85,\n            emotions: {\n                confident: 0.7,\n                nervous: 0.2,\n                excited: 0.1\n            },\n            recommendations: [\n                \"Maintain eye contact\",\n                \"Speak more slowly\"\n            ]\n        };\n    }\n    constructor(){\n        this.token = null;\n    }\n}\n// Create and export singleton\nconst mockApiClient = new MockApiClient();\n/* harmony default export */ __webpack_exports__[\"default\"] = (mockApiClient);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/mockApi.ts\n"));

/***/ })

});