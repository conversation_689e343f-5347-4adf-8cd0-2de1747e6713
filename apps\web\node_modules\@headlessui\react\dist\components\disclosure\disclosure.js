"use client";import{useFocusRing as q}from"@react-aria/focus";import{useHover as z}from"@react-aria/interactions";import y,{Fragment as w,createContext as I,useContext as x,useEffect as G,useMemo as C,useReducer as Q,useRef as K,useState as Y}from"react";import{useActivePress as Z}from'../../hooks/use-active-press.js';import{useEvent as P}from'../../hooks/use-event.js';import{useId as W}from'../../hooks/use-id.js';import{useResolveButtonType as ee}from'../../hooks/use-resolve-button-type.js';import{optionalRef as te,useSyncRefs as L}from'../../hooks/use-sync-refs.js';import{transitionDataAttributes as ne,useTransition as oe}from'../../hooks/use-transition.js';import{CloseProvider as le}from'../../internal/close-provider.js';import{OpenClosedProvider as re,ResetOpenClosedProvider as se,State as R,useOpenClosed as ue}from'../../internal/open-closed.js';import{isDisabledReactIssue7711 as ie}from'../../utils/bugs.js';import{match as B}from'../../utils/match.js';import{getOwnerDocument as ae}from'../../utils/owner.js';import{RenderFeatures as j,forwardRefWithAs as v,mergeProps as $,useRender as O}from'../../utils/render.js';import{startTransition as pe}from'../../utils/start-transition.js';import{Keys as A}from'../keyboard.js';var ce=(l=>(l[l.Open=0]="Open",l[l.Closed=1]="Closed",l))(ce||{}),de=(n=>(n[n.ToggleDisclosure=0]="ToggleDisclosure",n[n.CloseDisclosure=1]="CloseDisclosure",n[n.SetButtonId=2]="SetButtonId",n[n.SetPanelId=3]="SetPanelId",n[n.SetButtonElement=4]="SetButtonElement",n[n.SetPanelElement=5]="SetPanelElement",n))(de||{});let Te={[0]:e=>({...e,disclosureState:B(e.disclosureState,{[0]:1,[1]:0})}),[1]:e=>e.disclosureState===1?e:{...e,disclosureState:1},[2](e,t){return e.buttonId===t.buttonId?e:{...e,buttonId:t.buttonId}},[3](e,t){return e.panelId===t.panelId?e:{...e,panelId:t.panelId}},[4](e,t){return e.buttonElement===t.element?e:{...e,buttonElement:t.element}},[5](e,t){return e.panelElement===t.element?e:{...e,panelElement:t.element}}},_=I(null);_.displayName="DisclosureContext";function M(e){let t=x(_);if(t===null){let l=new Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(l,M),l}return t}let F=I(null);F.displayName="DisclosureAPIContext";function J(e){let t=x(F);if(t===null){let l=new Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(l,J),l}return t}let H=I(null);H.displayName="DisclosurePanelContext";function fe(){return x(H)}function me(e,t){return B(t.type,Te,e,t)}let De=w;function ye(e,t){let{defaultOpen:l=!1,...p}=e,i=K(null),c=L(t,te(a=>{i.current=a},e.as===void 0||e.as===w)),n=Q(me,{disclosureState:l?0:1,buttonElement:null,panelElement:null,buttonId:null,panelId:null}),[{disclosureState:o,buttonId:r},m]=n,s=P(a=>{m({type:1});let d=ae(i);if(!d||!r)return;let T=(()=>a?a instanceof HTMLElement?a:a.current instanceof HTMLElement?a.current:d.getElementById(r):d.getElementById(r))();T==null||T.focus()}),E=C(()=>({close:s}),[s]),f=C(()=>({open:o===0,close:s}),[o,s]),D={ref:c},S=O();return y.createElement(_.Provider,{value:n},y.createElement(F.Provider,{value:E},y.createElement(le,{value:s},y.createElement(re,{value:B(o,{[0]:R.Open,[1]:R.Closed})},S({ourProps:D,theirProps:p,slot:f,defaultTag:De,name:"Disclosure"})))))}let Pe="button";function Ee(e,t){let l=W(),{id:p=`headlessui-disclosure-button-${l}`,disabled:i=!1,autoFocus:c=!1,...n}=e,[o,r]=M("Disclosure.Button"),m=fe(),s=m===null?!1:m===o.panelId,E=K(null),f=L(E,t,P(u=>{if(!s)return r({type:4,element:u})}));G(()=>{if(!s)return r({type:2,buttonId:p}),()=>{r({type:2,buttonId:null})}},[p,r,s]);let D=P(u=>{var g;if(s){if(o.disclosureState===1)return;switch(u.key){case A.Space:case A.Enter:u.preventDefault(),u.stopPropagation(),r({type:0}),(g=o.buttonElement)==null||g.focus();break}}else switch(u.key){case A.Space:case A.Enter:u.preventDefault(),u.stopPropagation(),r({type:0});break}}),S=P(u=>{switch(u.key){case A.Space:u.preventDefault();break}}),a=P(u=>{var g;ie(u.currentTarget)||i||(s?(r({type:0}),(g=o.buttonElement)==null||g.focus()):r({type:0}))}),{isFocusVisible:d,focusProps:T}=q({autoFocus:c}),{isHovered:b,hoverProps:h}=z({isDisabled:i}),{pressed:U,pressProps:N}=Z({disabled:i}),X=C(()=>({open:o.disclosureState===0,hover:b,active:U,disabled:i,focus:d,autofocus:c}),[o,b,U,d,i,c]),k=ee(e,o.buttonElement),V=s?$({ref:f,type:k,disabled:i||void 0,autoFocus:c,onKeyDown:D,onClick:a},T,h,N):$({ref:f,id:p,type:k,"aria-expanded":o.disclosureState===0,"aria-controls":o.panelElement?o.panelId:void 0,disabled:i||void 0,autoFocus:c,onKeyDown:D,onKeyUp:S,onClick:a},T,h,N);return O()({ourProps:V,theirProps:n,slot:X,defaultTag:Pe,name:"Disclosure.Button"})}let Se="div",ge=j.RenderStrategy|j.Static;function Ae(e,t){let l=W(),{id:p=`headlessui-disclosure-panel-${l}`,transition:i=!1,...c}=e,[n,o]=M("Disclosure.Panel"),{close:r}=J("Disclosure.Panel"),[m,s]=Y(null),E=L(t,P(b=>{pe(()=>o({type:5,element:b}))}),s);G(()=>(o({type:3,panelId:p}),()=>{o({type:3,panelId:null})}),[p,o]);let f=ue(),[D,S]=oe(i,m,f!==null?(f&R.Open)===R.Open:n.disclosureState===0),a=C(()=>({open:n.disclosureState===0,close:r}),[n.disclosureState,r]),d={ref:E,id:p,...ne(S)},T=O();return y.createElement(se,null,y.createElement(H.Provider,{value:n.panelId},T({ourProps:d,theirProps:c,slot:a,defaultTag:Se,features:ge,visible:D,name:"Disclosure.Panel"})))}let be=v(ye),Ce=v(Ee),Re=v(Ae),je=Object.assign(be,{Button:Ce,Panel:Re});export{je as Disclosure,Ce as DisclosureButton,Re as DisclosurePanel};
