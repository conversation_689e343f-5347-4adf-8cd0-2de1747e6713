"use strict";var e=require("tslib"),r=require("react"),t=require("../../../lib/tremorTwMerge.cjs"),o=require("../../../lib/utils.cjs");function a(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var s=a(r);const l=o.makeClassName("TableFoot"),d=s.default.forwardRef(((r,o)=>{const{children:a,className:d}=r,c=e.__rest(r,["children","className"]);return s.default.createElement("tfoot",Object.assign({ref:o,className:t.tremorTwMerge(l("root"),"text-left font-medium border-t-[1px] ","text-tremor-content border-tremor-border","dark:text-dark-tremor-content dark:border-dark-tremor-border",d)},c),a)}));d.displayName="TableFoot",module.exports=d;
