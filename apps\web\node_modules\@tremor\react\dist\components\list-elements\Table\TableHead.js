import{__rest as e}from"tslib";import t from"react";import{tremorTwMerge as r}from"../../../lib/tremorTwMerge.js";import{makeClassName as a}from"../../../lib/utils.js";const o=a("TableHead"),l=t.forwardRef(((a,l)=>{const{children:m,className:s}=a,n=e(a,["children","className"]);return t.createElement(t.Fragment,null,t.createElement("thead",Object.assign({ref:l,className:r(o("root"),"text-left","text-tremor-content","dark:text-dark-tremor-content",s)},n),m))}));l.displayName="TableHead";export{l as default};
