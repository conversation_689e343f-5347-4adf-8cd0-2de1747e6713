import{__rest as e}from"tslib";import a from"react";import{Transition as t,Dialog as r,DialogBackdrop as s}from"@headlessui/react";import{tremorTwMerge as i}from"../../../lib/tremorTwMerge.js";import{makeClassName as l}from"../../../lib/utils.js";const o=l("dialog"),n=a.forwardRef(((l,n)=>{const{children:m,className:c}=l,d=e(l,["children","className"]);return a.createElement(t,{appear:!0,show:l.open},a.createElement(r,Object.assign({ref:n},d,{className:i(o("root"),"relative z-50",c)}),a.createElement(s,{transition:!0,className:"fixed bg-slate-950/30  dark:bg-slate-950/50  inset-0  transition-opacity data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in"}),a.createElement("div",{className:"fixed inset-0 overflow-y-auto w-screen"},a.createElement("div",{className:"flex min-h-full items-center justify-center p-4"},m))))}));n.displayName="Dialog";export{n as default};
