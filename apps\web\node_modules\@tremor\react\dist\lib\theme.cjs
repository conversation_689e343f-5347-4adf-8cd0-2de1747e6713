"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var o=require("./constants.cjs");const e=[o.BaseColors.Blue,o.BaseColors.<PERSON><PERSON>,o.BaseColors.Sky,o.BaseColors.Indigo,o.BaseColors.Violet,o.BaseColors.Purple,o.BaseColors.Fuchsia,o.BaseColors.Slate,o.BaseColors.Gray,o.BaseColors.Zinc,o.BaseColors.Neutral,o.BaseColors.Stone,o.BaseColors.Red,o.BaseColors.Orange,o.BaseColors.Amber,o.BaseColors.Yellow,o.BaseColors.Lime,o.BaseColors.Green,o.BaseColors.Emerald,o.BaseColors.Teal,o.BaseColors.Pink,o.BaseColors.Rose];exports.colorPalette={canvasBackground:50,lightBackground:100,background:500,darkBackground:600,darkestBackground:800,lightBorder:200,border:500,darkBorder:700,lightRing:200,ring:300,iconRing:500,lightText:400,text:500,iconText:600,darkText:700,darkestText:900,icon:500},exports.themeColorRange=e;
