'use client';
import{__rest as e}from"tslib";import t,{useMemo as r}from"react";import l,{useTooltip as a}from"../../util-elements/Tooltip/Tooltip.js";import{colorPalette as s,themeColorRange as o}from"../../../lib/theme.js";import{tremorTwMerge as n}from"../../../lib/tremorTwMerge.js";import{sumNumericArray as m,getColorClassNames as i,makeClassName as c}from"../../../lib/utils.js";const d=c("CategoryBar"),f=(e,t)=>e?e/t*100:0,u=({values:e})=>{const l=r((()=>m(e)),[e]);let a=0,s=0;return t.createElement("div",{className:n(d("labels"),"relative flex w-full text-tremor-default h-5 mb-2","text-tremor-content","dark:text-dark-tremor-content")},e.slice(0,e.length).map(((e,r)=>{a+=e;const o=(e>=.1*l||s>=.09*l)&&l-a>=.15*l&&a>=.1*l;s=o?0:s+=e;const m=f(e,l);return t.createElement("div",{key:`item-${r}`,className:"flex items-center justify-end",style:{width:`${m}%`}},t.createElement("span",{className:n(o?"block":"hidden","left-1/2 translate-x-1/2")},a))})),t.createElement("div",{className:n("absolute bottom-0 flex items-center left-0")},"0"),t.createElement("div",{className:n("absolute bottom-0 flex items-center right-0")},l))},b=t.forwardRef(((c,b)=>{const{values:v=[],colors:p=o,markerValue:g,showLabels:h=!0,tooltip:x,showAnimation:E=!1,className:N}=c,k=e(c,["values","colors","markerValue","showLabels","tooltip","showAnimation","className"]),w=r((()=>((e,t,r)=>{if(void 0===e)return"";let l=0;for(let a=0;a<t.length;a++){const o=t[a],n=i(r[a],s.background).bgColor;if(l+=o,l>=e)return n}return""})(g,v,p)),[g,v,p]),{tooltipProps:y,getReferenceProps:j}=a(),$=r((()=>m(v)),[v]),C=r((()=>f(g,$)),[g,$]);return t.createElement(t.Fragment,null,t.createElement(l,Object.assign({text:x},y)),t.createElement("div",Object.assign({ref:b,className:n(d("root"),N)},k),h?t.createElement(u,{values:v}):null,t.createElement("div",{className:n(d("barWrapper"),"relative w-full flex items-center h-2")},t.createElement("div",{className:n("flex-1 flex items-center h-full overflow-hidden rounded-tremor-full")},v.map(((e,r)=>{var l;const a=null!==(l=p[r])&&void 0!==l?l:"gray",o=e/$*100;return t.createElement("div",{key:`item-${r}`,className:n(d("categoryBar"),"h-full",i(a,s.background).bgColor),style:{width:`${o}%`}})}))),void 0!==g?t.createElement("div",Object.assign({ref:y.refs.setReference,className:n(d("markerWrapper"),"absolute right-1/2 -translate-x-1/2 w-5"),style:{left:`${C}%`,transition:E?"all 1s":""}},j),t.createElement("div",{className:n(d("marker"),"ring-2 mx-auto rounded-tremor-full h-4 w-1","ring-tremor-brand-inverted","dark:ring-dark-tremor-brand-inverted",w)})):null)))}));b.displayName="CategoryBar";export{b as default};
