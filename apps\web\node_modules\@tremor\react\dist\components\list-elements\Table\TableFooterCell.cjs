"use strict";var e=require("tslib"),t=require("react"),r=require("../../../lib/tremorTwMerge.cjs"),a=require("../../../lib/utils.cjs");function l(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var o=l(t);const s=a.makeClassName("TableFooterCell"),n=o.default.forwardRef(((t,a)=>{const{children:l,className:n}=t,c=e.__rest(t,["children","className"]);return o.default.createElement(o.default.Fragment,null,o.default.createElement("th",Object.assign({ref:a,className:r.tremorTwMerge(s("root"),"top-0 px-4 py-3.5","text-tremor-content font-medium","dark:text-dark-tremor-content",n)},c),l))}));n.displayName="TableFooterCell",module.exports=n;
