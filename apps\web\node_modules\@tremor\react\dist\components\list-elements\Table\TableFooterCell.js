import{__rest as e}from"tslib";import t from"react";import{tremorTwMerge as r}from"../../../lib/tremorTwMerge.js";import{makeClassName as o}from"../../../lib/utils.js";const l=o("TableFooterCell"),a=t.forwardRef(((o,a)=>{const{children:m,className:s}=o,n=e(o,["children","className"]);return t.createElement(t.Fragment,null,t.createElement("th",Object.assign({ref:a,className:r(l("root"),"top-0 px-4 py-3.5","text-tremor-content font-medium","dark:text-dark-tremor-content",s)},n),m))}));a.displayName="TableFooterCell";export{a as default};
