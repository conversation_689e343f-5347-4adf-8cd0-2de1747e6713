import{__rest as e}from"tslib";import t,{use<PERSON><PERSON>back as r,useEffect as l}from"react";import{colorPalette as o,themeColorRange as n}from"../../../lib/theme.js";import{tremorTwMerge as a}from"../../../lib/tremorTwMerge.js";import{makeClassName as c,getColorClassNames as i}from"../../../lib/utils.js";import s from"../../../assets/ChevronLeftFill.js";import u from"../../../assets/ChevronRightFill.js";const d=c("Legend"),m=({name:e,color:r,onClick:l,activeLegend:n})=>{const c=!!l;return t.createElement("li",{className:a(d("legendItem"),"group inline-flex items-center px-2 py-0.5 rounded-tremor-small transition whitespace-nowrap",c?"cursor-pointer":"cursor-default","text-tremor-content",c?"hover:bg-tremor-background-subtle":"","dark:text-dark-tremor-content",c?"dark:hover:bg-dark-tremor-background-subtle":""),onClick:t=>{t.stopPropagation(),null==l||l(e,r)}},t.createElement("svg",{className:a("flex-none h-2 w-2 mr-1.5",i(r,o.text).textColor,n&&n!==e?"opacity-40":"opacity-100"),fill:"currentColor",viewBox:"0 0 8 8"},t.createElement("circle",{cx:4,cy:4,r:4})),t.createElement("p",{className:a("whitespace-nowrap truncate text-tremor-default","text-tremor-content",c?"group-hover:text-tremor-content-emphasis":"","dark:text-dark-tremor-content",n&&n!==e?"opacity-40":"opacity-100",c?"dark:group-hover:text-dark-tremor-content-emphasis":"")},e))},f=({icon:e,onClick:r,disabled:o})=>{const n=e,[c,i]=t.useState(!1),s=t.useRef(null);return t.useEffect((()=>(c?s.current=setInterval((()=>{null==r||r()}),300):clearInterval(s.current),()=>clearInterval(s.current))),[c,r]),l((()=>{o&&(clearInterval(s.current),i(!1))}),[o]),t.createElement("button",{type:"button",className:a(d("legendSliderButton"),"w-5 group inline-flex items-center truncate rounded-tremor-small transition",o?"cursor-not-allowed":"cursor-pointer",o?"text-tremor-content-subtle":"text-tremor-content hover:text-tremor-content-emphasis hover:bg-tremor-background-subtle",o?"dark:text-dark-tremor-subtle":"dark:text-dark-tremor dark:hover:text-tremor-content-emphasis dark:hover:bg-dark-tremor-background-subtle"),disabled:o,onClick:e=>{e.stopPropagation(),null==r||r()},onMouseDown:e=>{e.stopPropagation(),i(!0)},onMouseUp:e=>{e.stopPropagation(),i(!1)}},t.createElement(n,{className:"w-full"}))},p=t.forwardRef(((l,o)=>{const{categories:c,colors:i=n,className:p,onClickLegendItem:g,activeLegend:v,enableLegendSlider:k=!1}=l,h=e(l,["categories","colors","className","onClickLegendItem","activeLegend","enableLegendSlider"]),b=t.useRef(null),x=t.useRef(null),[w,L]=t.useState(null),[E,y]=t.useState(null),C=t.useRef(null),I=r((()=>{const e=null==b?void 0:b.current;if(!e)return;const t=e.scrollLeft>0,r=e.scrollWidth-e.clientWidth>e.scrollLeft;L({left:t,right:r})}),[L]),N=r((e=>{var t,r;const l=null==b?void 0:b.current,o=null==x?void 0:x.current,n=null!==(t=null==l?void 0:l.clientWidth)&&void 0!==t?t:0,a=null!==(r=null==o?void 0:o.clientWidth)&&void 0!==r?r:0;l&&k&&(l.scrollTo({left:"left"===e?l.scrollLeft-n+a:l.scrollLeft+n-a,behavior:"smooth"}),setTimeout((()=>{I()}),400))}),[k,I]);t.useEffect((()=>{const e=e=>{"ArrowLeft"===e?N("left"):"ArrowRight"===e&&N("right")};return E?(e(E),C.current=setInterval((()=>{e(E)}),300)):clearInterval(C.current),()=>clearInterval(C.current)}),[E,N]);const R=e=>{e.stopPropagation(),"ArrowLeft"!==e.key&&"ArrowRight"!==e.key||(e.preventDefault(),y(e.key))},j=e=>{e.stopPropagation(),y(null)};return t.useEffect((()=>{const e=null==b?void 0:b.current;return k&&(I(),null==e||e.addEventListener("keydown",R),null==e||e.addEventListener("keyup",j)),()=>{null==e||e.removeEventListener("keydown",R),null==e||e.removeEventListener("keyup",j)}}),[I,k]),t.createElement("ol",Object.assign({ref:o,className:a(d("root"),"relative overflow-hidden",p)},h),t.createElement("div",{ref:b,tabIndex:0,className:a("h-full flex",k?(null==w?void 0:w.right)||(null==w?void 0:w.left)?"pl-4 pr-12  items-center overflow-auto snap-mandatory [&::-webkit-scrollbar]:hidden [scrollbar-width:none]":"":"flex-wrap")},c.map(((e,r)=>t.createElement(m,{key:`item-${r}`,name:e,color:i[r%i.length],onClick:g,activeLegend:v})))),k&&((null==w?void 0:w.right)||(null==w?void 0:w.left))?t.createElement(t.Fragment,null,t.createElement("div",{className:a("bg-tremor-background","dark:bg-dark-tremor-background","absolute flex top-0 pr-1 bottom-0 right-0 items-center justify-center h-full"),ref:x},t.createElement(f,{icon:s,onClick:()=>{y(null),N("left")},disabled:!(null==w?void 0:w.left)}),t.createElement(f,{icon:u,onClick:()=>{y(null),N("right")},disabled:!(null==w?void 0:w.right)}))):null)}));p.displayName="Legend";export{p as default};
