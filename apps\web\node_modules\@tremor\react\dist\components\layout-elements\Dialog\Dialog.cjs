"use strict";var e=require("tslib"),a=require("react"),t=require("@headlessui/react"),r=require("../../../lib/tremorTwMerge.cjs"),l=require("../../../lib/utils.cjs");function s(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var i=s(a);const n=l.makeClassName("dialog"),o=i.default.forwardRef(((a,l)=>{const{children:s,className:o}=a,c=e.__rest(a,["children","className"]);return i.default.createElement(t.Transition,{appear:!0,show:a.open},i.default.createElement(t.Dialog,Object.assign({ref:l},c,{className:r.tremorTwMerge(n("root"),"relative z-50",o)}),i.default.createElement(t.DialogBackdrop,{transition:!0,className:"fixed bg-slate-950/30  dark:bg-slate-950/50  inset-0  transition-opacity data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in"}),i.default.createElement("div",{className:"fixed inset-0 overflow-y-auto w-screen"},i.default.createElement("div",{className:"flex min-h-full items-center justify-center p-4"},s))))}));o.displayName="Dialog",module.exports=o;
