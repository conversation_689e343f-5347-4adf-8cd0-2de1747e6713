import{__rest as r}from"tslib";import e from"react";import{VerticalPositions as o,HorizontalPositions as t}from"../../../lib/constants.js";import{colorPalette as a}from"../../../lib/theme.js";import{tremorTwMerge as d}from"../../../lib/tremorTwMerge.js";import{getColorClassNames as i,makeClassName as m}from"../../../lib/utils.js";const n=m("Card"),s=r=>{if(!r)return"";switch(r){case t.Left:return"border-l-4";case o.Top:return"border-t-4";case t.Right:return"border-r-4";case o.Bottom:return"border-b-4";default:return""}},c=e.forwardRef(((o,t)=>{const{decoration:m="",decorationColor:c,children:l,className:b}=o,f=r(o,["decoration","decorationColor","children","className"]);return e.createElement("div",Object.assign({ref:t,className:d(n("root"),"relative w-full text-left ring-1 rounded-tremor-default p-6","bg-tremor-background ring-tremor-ring shadow-tremor-card","dark:bg-dark-tremor-background dark:ring-dark-tremor-ring dark:shadow-dark-tremor-card",c?i(c,a.border).borderColor:"border-tremor-brand dark:border-dark-tremor-brand",s(m),b)},f),l)}));c.displayName="Card";export{c as default};
