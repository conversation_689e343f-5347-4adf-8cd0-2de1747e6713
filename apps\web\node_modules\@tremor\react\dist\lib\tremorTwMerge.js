import{extendTailwindMerge as r}from"tailwind-merge";const t=r({extend:{classGroups:{shadow:[{shadow:[{tremor:["input","card","dropdown"],"dark-tremor":["input","card","dropdown"]}]}],rounded:[{rounded:[{tremor:["small","default","full"],"dark-tremor":["small","default","full"]}]}],"font-size":[{text:[{tremor:["default","title","metric"],"dark-tremor":["default","title","metric"]}]}]}}});export{t as tremorTwMerge};
