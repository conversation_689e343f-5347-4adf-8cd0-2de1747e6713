"use strict";var e=require("tslib"),r=require("../../../lib/tremorTwMerge.cjs"),n=require("../../../lib/utils.cjs"),a=require("react"),l=require("./styles.cjs");function t(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var o=t(a);const s=n.makeClassName("Col"),u=o.default.forwardRef(((n,a)=>{const{numColSpan:t=1,numColSpanSm:u,numColSpanMd:c,numColSpanLg:m,children:i,className:S}=n,p=e.__rest(n,["numColSpan","numColSpanSm","numColSpanMd","numColSpanLg","children","className"]),d=(e,r)=>e&&Object.keys(r).includes(String(e))?r[e]:"";return o.default.createElement("div",Object.assign({ref:a,className:r.tremorTwMerge(s("root"),(()=>{const e=d(t,l.colSpan),n=d(u,l.colSpanSm),a=d(c,l.colSpanMd),o=d(m,l.colSpanLg);return r.tremorTwMerge(e,n,a,o)})(),S)},p),i)}));u.displayName="Col",module.exports=u;
