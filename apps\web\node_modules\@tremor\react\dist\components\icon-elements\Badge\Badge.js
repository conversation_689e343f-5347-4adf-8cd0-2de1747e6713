'use client';
import{__rest as r}from"tslib";import e from"react";import t,{useTooltip as o}from"../../util-elements/Tooltip/Tooltip.js";import{Sizes as i}from"../../../lib/constants.js";import{colorPalette as a}from"../../../lib/theme.js";import{tremorTwMerge as n}from"../../../lib/tremorTwMerge.js";import{mergeRefs as s,getColorClassNames as l,makeClassName as m}from"../../../lib/utils.js";import{badgeProportions as c,iconSizes as d}from"./styles.js";const p=m("Badge"),g=e.forwardRef(((m,g)=>{const{color:f,icon:b,size:u=i.SM,tooltip:k,className:h,children:j}=m,x=r(m,["color","icon","size","tooltip","className","children"]),y=b||null,{tooltipProps:w,getReferenceProps:N}=o();return e.createElement("span",Object.assign({ref:s([g,w.refs.setReference]),className:n(p("root"),"w-max shrink-0 inline-flex justify-center items-center cursor-default rounded-tremor-small ring-1 ring-inset",f?n(l(f,a.background).bgColor,l(f,a.iconText).textColor,l(f,a.iconRing).ringColor,"bg-opacity-10 ring-opacity-20","dark:bg-opacity-5 dark:ring-opacity-60"):n("bg-tremor-brand-faint text-tremor-brand-emphasis ring-tremor-brand/20","dark:bg-dark-tremor-brand-muted/50 dark:text-dark-tremor-brand dark:ring-dark-tremor-subtle/20"),c[u].paddingX,c[u].paddingY,c[u].fontSize,h)},N,x),e.createElement(t,Object.assign({text:k},w)),y?e.createElement(y,{className:n(p("icon"),"shrink-0 -ml-1 mr-1.5",d[u].height,d[u].width)}):null,e.createElement("span",{className:n(p("text"),"whitespace-nowrap")},j))}));g.displayName="Badge";export{g as default};
