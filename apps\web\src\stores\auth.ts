import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { User, LoginRequest, RegisterRequest } from '@/types'
import { apiClient } from '@/lib/api'
import { toast } from 'sonner'

interface AuthState {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
}

interface AuthActions {
  login: (credentials: LoginRequest) => Promise<void>
  register: (userData: RegisterRequest) => Promise<void>
  logout: () => Promise<void>
  getCurrentUser: () => Promise<void>
  updateProfile: (data: Partial<User>) => Promise<void>
  clearError: () => void
  setLoading: (loading: boolean) => void
  initialize: () => Promise<void>
}

type AuthStore = AuthState & AuthActions

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Actions
      login: async (credentials: LoginRequest) => {
        try {
          set({ isLoading: true, error: null })

          const authResponse = await apiClient.login(credentials)

          set({
            user: authResponse.user,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          })

          toast.success('Successfully logged in!')
        } catch (error: any) {
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: error.message,
          })
          toast.error(error.message || 'Login failed')
          throw error
        }
      },

      register: async (userData: RegisterRequest) => {
        try {
          set({ isLoading: true, error: null })

          const authResponse = await apiClient.register(userData)

          set({
            user: authResponse.user,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          })

          toast.success('Account created successfully!')
        } catch (error: any) {
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: error.message,
          })
          toast.error(error.message || 'Registration failed')
          throw error
        }
      },

      logout: async () => {
        try {
          set({ isLoading: true })

          await apiClient.logout()

          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          })

          toast.success('Successfully logged out')
        } catch (error: any) {
          // Even if logout fails on server, clear local state
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          })

          toast.error('Logout failed, but you have been signed out locally')
        }
      },

      getCurrentUser: async () => {
        try {
          set({ isLoading: true, error: null })

          const user = await apiClient.getCurrentUser()

          set({
            user,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          })
        } catch (error: any) {
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: error.message,
          })
          throw error
        }
      },

      updateProfile: async (data: Partial<User>) => {
        try {
          set({ isLoading: true, error: null })

          const updatedUser = await apiClient.updateProfile(data)

          set({
            user: updatedUser,
            isLoading: false,
            error: null,
          })

          toast.success('Profile updated successfully!')
        } catch (error: any) {
          set({
            isLoading: false,
            error: error.message,
          })
          toast.error(error.message || 'Failed to update profile')
          throw error
        }
      },

      clearError: () => {
        set({ error: null })
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading })
      },

      // Initialize auth state from stored token
      initialize: async () => {
        const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null

        if (token) {
          try {
            set({ isLoading: true, error: null })
            const currentUser = await apiClient.getCurrentUser()

            set({
              user: currentUser,
              isAuthenticated: true,
              isLoading: false,
              error: null,
            })
          } catch (error: any) {
            // Token is invalid, clear it
            if (typeof window !== 'undefined') {
              localStorage.removeItem('auth_token')
            }
            set({
              user: null,
              isAuthenticated: false,
              isLoading: false,
              error: null,
            })
          }
        } else {
          set({ isLoading: false })
        }
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
)