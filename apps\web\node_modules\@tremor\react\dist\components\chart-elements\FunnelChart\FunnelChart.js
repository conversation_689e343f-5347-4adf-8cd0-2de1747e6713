import{__rest as e}from"tslib";import t from"react";import{ChartTooltipFrame as r,ChartTooltipRow as a}from"../common/ChartTooltip.js";import{BaseColors as n}from"../../../lib/constants.js";import{colorPalette as l}from"../../../lib/theme.js";import{tremorTwMerge as o}from"../../../lib/tremorTwMerge.js";import{getColorClassNames as i,defaultValueFormatter as s}from"../../../lib/utils.js";import c from"../common/NoData.js";import u from"../../../assets/ArrowRightIcon.js";const d=["100%","75%","50%","25%","0%"],m=t.forwardRef(((m,h)=>{var x,g;const{data:p,evolutionGradient:v=!1,gradient:f=!0,valueFormatter:b=s,className:y,calculateFrom:w="first",color:E,variant:X="base",showGridLines:$=!0,showYAxis:k="previous"!==w,showXAxis:N=!0,showArrow:C=!0,xAxisLabel:H="",yAxisLabel:A="",yAxisPadding:B=(k?A?70:45:0),showTooltip:T=!0,onValueChange:F,customTooltip:L,noDataText:z,rotateLabelX:Y,barGap:M="20%"}=m,S=e(m,["data","evolutionGradient","gradient","valueFormatter","className","calculateFrom","color","variant","showGridLines","showYAxis","showXAxis","showArrow","xAxisLabel","yAxisLabel","yAxisPadding","showTooltip","onValueChange","customTooltip","noDataText","rotateLabelX","barGap"]),j=N&&H?25:15,G=L,O=t.useRef(null),R=t.useRef(null),[V,I]=t.useState(0),[D,P]=t.useState(0),[W,q]=t.useState({x:0,y:0}),[K,J]=t.useState(void 0),Q=!!F;const U=t.useMemo((()=>Math.max(...p.map((e=>e.value)))),[p]),Z=V-10-B,_=t.useMemo((()=>{if("number"==typeof M)return M;if("string"==typeof M&&M.endsWith("%")){const e=parseFloat(M.slice(0,-1));return Z*e/100/(p.length-1)}return console.error('Invalid barGap value. It must be a number or a percentage string (e.g., "10%").'),30}),[Z,p.length,M]),ee=t.useMemo((()=>(Z-(p.length-1)*_-_)/p.length),[Z,_,p.length]),te=D-10-(N?((null==Y?void 0:Y.xAxisHeight)||j)+(N&&H?30:10):0),re="previous"===w,ae="center"===X;t.useLayoutEffect((()=>{const e=()=>{if(O.current){const e=O.current.getBoundingClientRect();I(e.width),P(e.height)}};return e(),window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}}),[y]),t.useEffect((()=>{const e=()=>{if(R.current){const e=R.current.getBoundingClientRect();e.right>window.innerWidth&&(R.current.style.left=V-e.width+"px")}};return e(),window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}}),[W,V]);const ne=t.useMemo((()=>te<=0?[]:p.reduce(((e,t,r)=>{var a,n,l,o;const i=e[r-1],s=t.value,c=re&&null!==(a=null==i?void 0:i.value)&&void 0!==a?a:U,u=re&&null!==(n=null==i?void 0:i.barHeight)&&void 0!==n?n:te,d=s/c,m=d*u,h=r*(ee+_)+.5*_,x=u-m+(re?te-(null!==(l=null==i?void 0:i.barHeight)&&void 0!==l?l:te):0),g=null===(o=p[r+1])||void 0===o?void 0:o.value,v=g/c,f=v*u,b=(r+1)*(ee+_)+.5*_;return e.push({value:s,normalizedValue:d,name:t.name,startX:h,startY:x,barHeight:m,nextValue:g,nextNormalizedValue:v,nextBarHeight:f,nextStartX:b}),e}),[])),[p,te,re,ee,_,U]),le=e=>{var t;const r=null===(t=O.current)||void 0===t?void 0:t.getBoundingClientRect();if(!r)return;const a=r.x,s=r.y+window.scrollY,c=a+window.scrollX+B+5,u=c+(r.width-B-5),d=s+(r.height-5-(N?j:0));if(e.pageX<c||e.pageX>u||e.pageY<s||e.pageY>d)return console.log("out of bounds"),q({x:0,y:0});const m=e.pageX-a-ee/2-B-5,h=ne.reduce(((e,t)=>Math.abs(t.startX-m)<Math.abs(e.startX-m)?t:e)),x=ne.findIndex((e=>e===h));q({x:h.startX,y:h.startY,data:{dataKey:h.name,name:h.name,value:h.value,color:null!=E?E:n.Blue,className:o(i(null!=E?E:n.Blue,l.text).textColor,Q?"cursor-pointer":"cursor-default"),fill:"",payload:h},index:x})};return t.createElement("div",Object.assign({ref:h,className:o("tremor-wrapper relative w-full h-80",y)},S),(null==p?void 0:p.length)?t.createElement(t.Fragment,null,t.createElement("svg",{ref:O,xmlns:"http://www.w3.org/2000/svg",className:o("w-full h-full"),onMouseMove:e=>{const t={clientX:e.clientX,clientY:e.clientY,pageX:e.pageX,pageY:e.pageY};le(t)},onTouchMove:e=>{const t=e.touches[0];le(t)},onMouseLeave:()=>q({x:0,y:0}),onTouchEnd:()=>q({x:0,y:0})},d.map(((e,r)=>t.createElement(t.Fragment,{key:`y-axis-${r}`},$?t.createElement("line",{x1:B+5,y1:r*te/4+5,x2:V-5,y2:r*te/4+5,stroke:"currentColor",className:o("stroke-1","stroke-tremor-border","dark:stroke-dark-tremor-border")}):null,t.createElement("text",{x:B-10+5,y:r*te/4+5+5,textAnchor:"end",fill:"",stroke:"",className:o("text-tremor-label","fill-tremor-content","dark:fill-dark-tremor-content")},e)))),ne.map(((e,r)=>{var a,s;return t.createElement("g",{key:`bar-${r}`},t.createElement("rect",{x:e.startX-.5*_+5+B,y:5,width:ee+_,height:te,fill:"currentColor",className:o("z-0",W.index===r?"text-[#d1d5db]/15":"text-transparent")}),f?t.createElement("rect",{x:e.startX+5+B,y:te-(re&&(null===(a=ne[r-1])||void 0===a?void 0:a.barHeight)||te)+5,width:ee,height:(te-e.barHeight-(re&&te-(null===(s=ne[r-1])||void 0===s?void 0:s.barHeight)||0))/(ae?2:1),fill:"url(#base-gradient)",className:o(K&&K.index!==r?"opacity-30":"")}):null,t.createElement("rect",{x:e.startX+5+B,y:(ae?te/2-e.barHeight/2:e.startY)+5,width:ee,height:e.barHeight,fill:"currentColor",className:o(i(null!=E?E:n.Blue,l.text).textColor,K&&K.index!==r?"opacity-30":"",Q?"cursor-pointer":"cursor-default"),onClick:t=>function(e,t,r){r.stopPropagation(),Q&&(t===(null==K?void 0:K.index)?(J(void 0),F(void 0)):(J({data:e,index:t}),F({eventType:"bar",categoryClicked:e.name,[e.name]:e.value,percentage:e.normalizedValue})))}(e,r,t)}),f&&ae?t.createElement("rect",{x:e.startX+5+B,y:te/2+e.barHeight/2+5,width:ee,height:(te-e.barHeight)/2,fill:"url(#base-gradient-revert)",className:o(K&&K.index!==r?"opacity-30":"")}):null,N?t.createElement("foreignObject",{x:e.startX+5+B,y:te+5+10,width:ee,height:(null==Y?void 0:Y.xAxisHeight)||j,transform:Y?`rotate(${null==Y?void 0:Y.angle}, ${e.startX+ee/2+5+B}, ${te+((null==Y?void 0:Y.xAxisHeight)||j)/2+5+((null==Y?void 0:Y.verticalShift)||0)})`:void 0},t.createElement("div",{className:o("truncate text-center !text-tremor-label","text-tremor-content","dark:text-dark-tremor-content"),title:e.name},e.name)):null)})),ne.map(((e,r)=>t.createElement(t.Fragment,{key:`gradient-${r}`},r<p.length-1&&v?t.createElement(t.Fragment,null,ae?t.createElement(t.Fragment,null,t.createElement("polygon",{points:`\n                                    ${e.startX+ee+5+B}, ${te/2+e.nextBarHeight/4+5}\n                                    ${e.nextStartX+5+B}, ${te/2+e.nextBarHeight/4+5}\n                                    ${e.nextStartX+5+B}, ${te/2-e.nextBarHeight/2+5}\n                                    ${e.startX+ee+5+B}, ${te/2-e.barHeight/2+5}\n                                  `,fill:"url(#base-gradient)",className:o("z-10",K&&K.index!==r?"opacity-30":"")}),t.createElement("polygon",{points:`\n                                    ${e.startX+ee+5+B}, ${te/2+e.barHeight/2+5}\n                                    ${e.nextStartX+5+B}, ${te/2+e.nextBarHeight/2+5}\n                                    ${e.nextStartX+5+B}, ${te/2-e.nextBarHeight/4+5}\n                                    ${e.startX+ee+5+B}, ${te/2-e.nextBarHeight/4+5}\n                                  `,fill:"url(#base-gradient-revert)",className:o("z-10",K&&K.index!==r?"opacity-30":"")})):t.createElement("polygon",{points:`\n                                  ${e.startX+ee+5+B}, ${e.startY+5} \n                                  ${e.nextStartX+5+B}, ${te-e.nextBarHeight+5} \n                                  ${e.nextStartX+5+B}, ${te+5} \n                                  ${e.startX+ee+5+B}, ${te+5}\n                                `,fill:"url(#base-gradient)",className:o("z-10",K&&K.index!==r?"opacity-30":"")})):null,r<p.length-1&&N&&C&&_>=14?t.createElement("foreignObject",{x:e.startX+ee+5+B-6+_/2,y:te+5+11,width:12,height:(null==Y?void 0:Y.xAxisHeight)||j},t.createElement("div",{className:o("text-tremor-content","dark:text-dark-tremor-content")},t.createElement(u,{className:"size-3.5 shrink-0"}))):null))),t.createElement("linearGradient",{id:"base-gradient",x1:"0%",y1:"0%",x2:"0%",y2:"100%",className:o(i(null!=E?E:n.Blue,l.text).textColor)},t.createElement("stop",{offset:"5%",stopColor:"currentColor",stopOpacity:.4}),t.createElement("stop",{offset:"95%",stopColor:"currentColor",stopOpacity:0})),t.createElement("linearGradient",{id:"base-gradient-revert",x1:"0%",y1:"0%",x2:"0%",y2:"100%",className:o(i(null!=E?E:n.Blue,l.text).textColor)},t.createElement("stop",{offset:"5%",stopColor:"currentColor",stopOpacity:0}),t.createElement("stop",{offset:"95%",stopColor:"currentColor",stopOpacity:.4})),N&&H?t.createElement("text",{x:V/2+B/2,y:te+5+50,style:{textAnchor:"middle"},fill:"",stroke:"",className:o("text-tremor-default cursor-default font-medium","fill-tremor-content-emphasis","dark:fill-dark-tremor-content-emphasis")},H):null,k&&A?t.createElement("text",{x:-5,y:te/2+10,textAnchor:"middle",style:{textAnchor:"middle"},transform:`rotate(-90, 0, ${te/2})`,fill:"",stroke:"",className:o("text-tremor-default cursor-default font-medium","fill-tremor-content-emphasis","dark:fill-dark-tremor-content-emphasis")},A):null),T?t.createElement("div",{ref:R,className:o("absolute top-0 pointer-events-none",W.data?"visible":"hidden"),tabIndex:-1,role:"dialog",style:{left:W.x+.66*ee}},G?t.createElement(G,{payload:W.data?[W.data]:[],active:!!W.data,label:null===(x=W.data)||void 0===x?void 0:x.name}):t.createElement(r,null,t.createElement("div",{className:o("border-tremor-border border-b px-4 py-2","dark:border-dark-tremor-border")},t.createElement("p",{className:o("font-medium","text-tremor-content-emphasis","dark:text-dark-tremor-content-emphasis")},null===(g=null==W?void 0:W.data)||void 0===g?void 0:g.name)),t.createElement("div",{className:o("px-4 py-2 space-y-1")},W.data?t.createElement(a,{value:b(W.data.value),name:`${(100*W.data.payload.normalizedValue).toFixed(2)}%`,color:null!=E?E:n.Blue}):null))):null):t.createElement(c,{noDataText:z}))}));m.displayName="FunnelChart";const h=r=>{var{data:a}=r,n=e(r,["data"]);const l=a?((e,t)=>{if(e&&e.length>0){if("previous"===t&&e[0].value<=0)return`The value of the first item "${e[0].name}" is not greater than 0. This is not allowed when setting the "calculateFrom" prop to "previous". Please enter a value greater than 0.`;for(const t of e)if(t.value<0)return`Item "${t.name}" has a negative value: ${t.value}. This is not allowed. The value must be greater than or equal to 0.`}return null})(a,n.calculateFrom):null;return l?t.createElement(c,{className:"h-full w-full p-6",noDataText:`Calculation error: ${l}`}):t.createElement(m,Object.assign({data:a},n))};export{h as default};
