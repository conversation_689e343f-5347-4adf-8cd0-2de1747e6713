"use client";import{useFocusRing as Le}from"@react-aria/focus";import{useHover as Ie}from"@react-aria/interactions";import y,{createContext as oe,createRef as Pe,useContext as re,useEffect as ne,useMemo as D,useReducer as De,useRef as ee,useState as le}from"react";import{useActivePress as he}from'../../hooks/use-active-press.js';import{useElementSize as ke}from'../../hooks/use-element-size.js';import{useEvent as b}from'../../hooks/use-event.js';import{useEventListener as Ge}from'../../hooks/use-event-listener.js';import{useId as ae}from'../../hooks/use-id.js';import{useIsoMorphicEffect as He}from'../../hooks/use-iso-morphic-effect.js';import{useLatestValue as Ae}from'../../hooks/use-latest-value.js';import{useOnDisappear as Ue}from'../../hooks/use-on-disappear.js';import{useOutsideClick as Ne}from'../../hooks/use-outside-click.js';import{useOwnerDocument as fe}from'../../hooks/use-owner.js';import{useResolveButtonType as we}from'../../hooks/use-resolve-button-type.js';import{MainTreeProvider as Ce,useMainTreeNode as Ke,useRootContainers as We}from'../../hooks/use-root-containers.js';import{useScrollLock as je}from'../../hooks/use-scroll-lock.js';import{optionalRef as Ve,useSyncRefs as X}from'../../hooks/use-sync-refs.js';import{Direction as G,useTabDirection as Re}from'../../hooks/use-tab-direction.js';import{transitionDataAttributes as Be,useTransition as _e}from'../../hooks/use-transition.js';import{CloseProvider as $e}from'../../internal/close-provider.js';import{FloatingProvider as Je,useFloatingPanel as Xe,useFloatingPanelProps as qe,useFloatingReference as ze,useResolvedAnchor as Ye}from'../../internal/floating.js';import{Hidden as ce,HiddenFeatures as ve}from'../../internal/hidden.js';import{OpenClosedProvider as Qe,ResetOpenClosedProvider as Ze,State as q,useOpenClosed as Fe}from'../../internal/open-closed.js';import{isDisabledReactIssue7711 as xe}from'../../utils/bugs.js';import{Focus as H,FocusResult as Te,FocusableMode as et,focusIn as W,getFocusableElements as me,isFocusableElement as tt}from'../../utils/focus-management.js';import{match as j}from'../../utils/match.js';import'../../utils/micro-task.js';import{getOwnerDocument as ot}from'../../utils/owner.js';import{RenderFeatures as pe,forwardRefWithAs as z,mergeProps as ye,useRender as te}from'../../utils/render.js';import{Keys as V}from'../keyboard.js';import{Portal as rt,useNestedPortals as nt}from'../portal/portal.js';var lt=(f=>(f[f.Open=0]="Open",f[f.Closed=1]="Closed",f))(lt||{}),at=(p=>(p[p.TogglePopover=0]="TogglePopover",p[p.ClosePopover=1]="ClosePopover",p[p.SetButton=2]="SetButton",p[p.SetButtonId=3]="SetButtonId",p[p.SetPanel=4]="SetPanel",p[p.SetPanelId=5]="SetPanelId",p))(at||{});let pt={[0]:o=>({...o,popoverState:j(o.popoverState,{[0]:1,[1]:0}),__demoMode:!1}),[1](o){return o.popoverState===1?o:{...o,popoverState:1,__demoMode:!1}},[2](o,a){return o.button===a.button?o:{...o,button:a.button}},[3](o,a){return o.buttonId===a.buttonId?o:{...o,buttonId:a.buttonId}},[4](o,a){return o.panel===a.panel?o:{...o,panel:a.panel}},[5](o,a){return o.panelId===a.panelId?o:{...o,panelId:a.panelId}}},Ee=oe(null);Ee.displayName="PopoverContext";function se(o){let a=re(Ee);if(a===null){let f=new Error(`<${o} /> is missing a parent <Popover /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(f,se),f}return a}let ue=oe(null);ue.displayName="PopoverAPIContext";function be(o){let a=re(ue);if(a===null){let f=new Error(`<${o} /> is missing a parent <Popover /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(f,be),f}return a}let ge=oe(null);ge.displayName="PopoverGroupContext";function Me(){return re(ge)}let ie=oe(null);ie.displayName="PopoverPanelContext";function st(){return re(ie)}function ut(o,a){return j(a.type,pt,o,a)}let it="div";function dt(o,a){var J;let{__demoMode:f=!1,...C}=o,T=ee(null),S=X(a,Ve(t=>{T.current=t})),p=ee([]),l=De(ut,{__demoMode:f,popoverState:f?0:1,buttons:p,button:null,buttonId:null,panel:null,panelId:null,beforePanelSentinel:Pe(),afterPanelSentinel:Pe(),afterButtonSentinel:Pe()}),[{popoverState:c,button:d,buttonId:r,panel:s,panelId:R,beforePanelSentinel:m,afterPanelSentinel:h,afterButtonSentinel:P},n]=l,v=fe((J=T.current)!=null?J:d),g=D(()=>{if(!d||!s)return!1;for(let E of document.querySelectorAll("body > *"))if(Number(E==null?void 0:E.contains(d))^Number(E==null?void 0:E.contains(s)))return!0;let t=me(),e=t.indexOf(d),u=(e+t.length-1)%t.length,i=(e+1)%t.length,A=t[u],x=t[i];return!s.contains(A)&&!s.contains(x)},[d,s]),_=Ae(r),L=Ae(R),I=D(()=>({buttonId:_,panelId:L,close:()=>n({type:1})}),[_,L,n]),M=Me(),k=M==null?void 0:M.registerPopover,B=b(()=>{var t;return(t=M==null?void 0:M.isFocusWithinPopoverGroup())!=null?t:(v==null?void 0:v.activeElement)&&((d==null?void 0:d.contains(v.activeElement))||(s==null?void 0:s.contains(v.activeElement)))});ne(()=>k==null?void 0:k(I),[k,I]);let[U,O]=nt(),N=Ke(d),w=We({mainTreeNode:N,portals:U,defaultContainers:[d,s]});Ge(v==null?void 0:v.defaultView,"focus",t=>{var e,u,i,A,x,E;t.target!==window&&t.target instanceof HTMLElement&&c===0&&(B()||d&&s&&(w.contains(t.target)||(u=(e=m.current)==null?void 0:e.contains)!=null&&u.call(e,t.target)||(A=(i=h.current)==null?void 0:i.contains)!=null&&A.call(i,t.target)||(E=(x=P.current)==null?void 0:x.contains)!=null&&E.call(x,t.target)||n({type:1})))},!0),Ne(c===0,w.resolveContainers,(t,e)=>{n({type:1}),tt(e,et.Loose)||(t.preventDefault(),d==null||d.focus())});let F=b(t=>{n({type:1});let e=(()=>t?t instanceof HTMLElement?t:"current"in t&&t.current instanceof HTMLElement?t.current:d:d)();e==null||e.focus()}),Q=D(()=>({close:F,isPortalled:g}),[F,g]),K=D(()=>({open:c===0,close:F}),[c,F]),Z={ref:S},$=te();return y.createElement(Ce,{node:N},y.createElement(Je,null,y.createElement(ie.Provider,{value:null},y.createElement(Ee.Provider,{value:l},y.createElement(ue.Provider,{value:Q},y.createElement($e,{value:F},y.createElement(Qe,{value:j(c,{[0]:q.Open,[1]:q.Closed})},y.createElement(O,null,$({ourProps:Z,theirProps:C,slot:K,defaultTag:it,name:"Popover"})))))))))}let Pt="button";function ft(o,a){let f=ae(),{id:C=`headlessui-popover-button-${f}`,disabled:T=!1,autoFocus:S=!1,...p}=o,[l,c]=se("Popover.Button"),{isPortalled:d}=be("Popover.Button"),r=ee(null),s=`headlessui-focus-sentinel-${ae()}`,R=Me(),m=R==null?void 0:R.closeOthers,P=st()!==null;ne(()=>{if(!P)return c({type:3,buttonId:C}),()=>{c({type:3,buttonId:null})}},[P,C,c]);let[n]=le(()=>Symbol()),v=X(r,a,ze(),b(e=>{if(!P){if(e)l.buttons.current.push(n);else{let u=l.buttons.current.indexOf(n);u!==-1&&l.buttons.current.splice(u,1)}l.buttons.current.length>1&&console.warn("You are already using a <Popover.Button /> but only 1 <Popover.Button /> is supported."),e&&c({type:2,button:e})}})),g=X(r,a),_=fe(r),L=b(e=>{var u,i,A;if(P){if(l.popoverState===1)return;switch(e.key){case V.Space:case V.Enter:e.preventDefault(),(i=(u=e.target).click)==null||i.call(u),c({type:1}),(A=l.button)==null||A.focus();break}}else switch(e.key){case V.Space:case V.Enter:e.preventDefault(),e.stopPropagation(),l.popoverState===1&&(m==null||m(l.buttonId)),c({type:0});break;case V.Escape:if(l.popoverState!==0)return m==null?void 0:m(l.buttonId);if(!r.current||_!=null&&_.activeElement&&!r.current.contains(_.activeElement))return;e.preventDefault(),e.stopPropagation(),c({type:1});break}}),I=b(e=>{P||e.key===V.Space&&e.preventDefault()}),M=b(e=>{var u,i;xe(e.currentTarget)||T||(P?(c({type:1}),(u=l.button)==null||u.focus()):(e.preventDefault(),e.stopPropagation(),l.popoverState===1&&(m==null||m(l.buttonId)),c({type:0}),(i=l.button)==null||i.focus()))}),k=b(e=>{e.preventDefault(),e.stopPropagation()}),{isFocusVisible:B,focusProps:U}=Le({autoFocus:S}),{isHovered:O,hoverProps:N}=Ie({isDisabled:T}),{pressed:w,pressProps:Y}=he({disabled:T}),F=l.popoverState===0,Q=D(()=>({open:F,active:w||F,disabled:T,hover:O,focus:B,autofocus:S}),[F,O,B,w,T,S]),K=we(o,l.button),Z=P?ye({ref:g,type:K,onKeyDown:L,onClick:M,disabled:T||void 0,autoFocus:S},U,N,Y):ye({ref:v,id:l.buttonId,type:K,"aria-expanded":l.popoverState===0,"aria-controls":l.panel?l.panelId:void 0,disabled:T||void 0,autoFocus:S,onKeyDown:L,onKeyUp:I,onClick:M,onMouseDown:k},U,N,Y),$=Re(),J=b(()=>{let e=l.panel;if(!e)return;function u(){j($.current,{[G.Forwards]:()=>W(e,H.First),[G.Backwards]:()=>W(e,H.Last)})===Te.Error&&W(me().filter(A=>A.dataset.headlessuiFocusGuard!=="true"),j($.current,{[G.Forwards]:H.Next,[G.Backwards]:H.Previous}),{relativeTo:l.button})}u()}),t=te();return y.createElement(y.Fragment,null,t({ourProps:Z,theirProps:p,slot:Q,defaultTag:Pt,name:"Popover.Button"}),F&&!P&&d&&y.createElement(ce,{id:s,ref:l.afterButtonSentinel,features:ve.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:J}))}let ct="div",vt=pe.RenderStrategy|pe.Static;function Oe(o,a){let f=ae(),{id:C=`headlessui-popover-backdrop-${f}`,transition:T=!1,...S}=o,[{popoverState:p},l]=se("Popover.Backdrop"),[c,d]=le(null),r=X(a,d),s=Fe(),[R,m]=_e(T,c,s!==null?(s&q.Open)===q.Open:p===0),h=b(g=>{if(xe(g.currentTarget))return g.preventDefault();l({type:1})}),P=D(()=>({open:p===0}),[p]),n={ref:r,id:C,"aria-hidden":!0,onClick:h,...Be(m)};return te()({ourProps:n,theirProps:S,slot:P,defaultTag:ct,features:vt,visible:R,name:"Popover.Backdrop"})}let Tt="div",mt=pe.RenderStrategy|pe.Static;function yt(o,a){let f=ae(),{id:C=`headlessui-popover-panel-${f}`,focus:T=!1,anchor:S,portal:p=!1,modal:l=!1,transition:c=!1,...d}=o,[r,s]=se("Popover.Panel"),{close:R,isPortalled:m}=be("Popover.Panel"),h=`headlessui-focus-sentinel-before-${f}`,P=`headlessui-focus-sentinel-after-${f}`,n=ee(null),v=Ye(S),[g,_]=Xe(v),L=qe();v&&(p=!0);let[I,M]=le(null),k=X(n,a,v?g:null,b(t=>s({type:4,panel:t})),M),B=fe(n);He(()=>(s({type:5,panelId:C}),()=>{s({type:5,panelId:null})}),[C,s]);let U=Fe(),[O,N]=_e(c,I,U!==null?(U&q.Open)===q.Open:r.popoverState===0);Ue(O,r.button,()=>{s({type:1})});let w=r.__demoMode?!1:l&&O;je(w,B);let Y=b(t=>{var e;switch(t.key){case V.Escape:if(r.popoverState!==0||!n.current||B!=null&&B.activeElement&&!n.current.contains(B.activeElement))return;t.preventDefault(),t.stopPropagation(),s({type:1}),(e=r.button)==null||e.focus();break}});ne(()=>{var t;o.static||r.popoverState===1&&((t=o.unmount)==null||t)&&s({type:4,panel:null})},[r.popoverState,o.unmount,o.static,s]),ne(()=>{if(r.__demoMode||!T||r.popoverState!==0||!n.current)return;let t=B==null?void 0:B.activeElement;n.current.contains(t)||W(n.current,H.First)},[r.__demoMode,T,n.current,r.popoverState]);let F=D(()=>({open:r.popoverState===0,close:R}),[r.popoverState,R]),Q=ye(v?L():{},{ref:k,id:C,onKeyDown:Y,onBlur:T&&r.popoverState===0?t=>{var u,i,A,x,E;let e=t.relatedTarget;e&&n.current&&((u=n.current)!=null&&u.contains(e)||(s({type:1}),((A=(i=r.beforePanelSentinel.current)==null?void 0:i.contains)!=null&&A.call(i,e)||(E=(x=r.afterPanelSentinel.current)==null?void 0:x.contains)!=null&&E.call(x,e))&&e.focus({preventScroll:!0})))}:void 0,tabIndex:-1,style:{...d.style,..._,"--button-width":ke(r.button,!0).width},...Be(N)}),K=Re(),Z=b(()=>{let t=n.current;if(!t)return;function e(){j(K.current,{[G.Forwards]:()=>{var i;W(t,H.First)===Te.Error&&((i=r.afterPanelSentinel.current)==null||i.focus())},[G.Backwards]:()=>{var u;(u=r.button)==null||u.focus({preventScroll:!0})}})}e()}),$=b(()=>{let t=n.current;if(!t)return;function e(){j(K.current,{[G.Forwards]:()=>{if(!r.button)return;let u=me(),i=u.indexOf(r.button),A=u.slice(0,i+1),E=[...u.slice(i+1),...A];for(let de of E.slice())if(de.dataset.headlessuiFocusGuard==="true"||I!=null&&I.contains(de)){let Se=E.indexOf(de);Se!==-1&&E.splice(Se,1)}W(E,H.First,{sorted:!1})},[G.Backwards]:()=>{var i;W(t,H.Previous)===Te.Error&&((i=r.button)==null||i.focus())}})}e()}),J=te();return y.createElement(Ze,null,y.createElement(ie.Provider,{value:C},y.createElement(ue.Provider,{value:{close:R,isPortalled:m}},y.createElement(rt,{enabled:p?o.static||O:!1},O&&m&&y.createElement(ce,{id:h,ref:r.beforePanelSentinel,features:ve.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:Z}),J({ourProps:Q,theirProps:d,slot:F,defaultTag:Tt,features:mt,visible:O,name:"Popover.Panel"}),O&&m&&y.createElement(ce,{id:P,ref:r.afterPanelSentinel,features:ve.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:$})))))}let Et="div";function bt(o,a){let f=ee(null),C=X(f,a),[T,S]=le([]),p=b(P=>{S(n=>{let v=n.indexOf(P);if(v!==-1){let g=n.slice();return g.splice(v,1),g}return n})}),l=b(P=>(S(n=>[...n,P]),()=>p(P))),c=b(()=>{var v;let P=ot(f);if(!P)return!1;let n=P.activeElement;return(v=f.current)!=null&&v.contains(n)?!0:T.some(g=>{var _,L;return((_=P.getElementById(g.buttonId.current))==null?void 0:_.contains(n))||((L=P.getElementById(g.panelId.current))==null?void 0:L.contains(n))})}),d=b(P=>{for(let n of T)n.buttonId.current!==P&&n.close()}),r=D(()=>({registerPopover:l,unregisterPopover:p,isFocusWithinPopoverGroup:c,closeOthers:d}),[l,p,c,d]),s=D(()=>({}),[]),R=o,m={ref:C},h=te();return y.createElement(Ce,null,y.createElement(ge.Provider,{value:r},h({ourProps:m,theirProps:R,slot:s,defaultTag:Et,name:"Popover.Group"})))}let gt=z(dt),St=z(ft),At=z(Oe),Ct=z(Oe),Rt=z(yt),Bt=z(bt),lo=Object.assign(gt,{Button:St,Backdrop:Ct,Overlay:At,Panel:Rt,Group:Bt});export{lo as Popover,Ct as PopoverBackdrop,St as PopoverButton,Bt as PopoverGroup,At as PopoverOverlay,Rt as PopoverPanel};
