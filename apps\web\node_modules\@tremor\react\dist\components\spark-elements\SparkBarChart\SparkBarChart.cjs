'use client';
"use strict";var e=require("tslib"),a=require("../../../lib/constants.cjs"),t=require("../../../lib/theme.cjs"),r=require("../../../lib/tremorTwMerge.cjs"),i=require("../../../lib/utils.cjs"),l=require("react"),o=require("recharts"),n=require("../../chart-elements/common/utils.cjs"),s=require("../../chart-elements/common/NoData.cjs");function c(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var u=c(l);const m=u.default.forwardRef(((l,c)=>{const{data:m=[],categories:d=[],index:f,colors:g=t.themeColorRange,stack:h=!1,relative:x=!1,animationDuration:v=900,showAnimation:C=!1,noDataText:b,autoMinValue:p=!1,minValue:q,maxValue:j,className:w}=l,y=e.__rest(l,["data","categories","index","colors","stack","relative","animationDuration","showAnimation","noDataText","autoMinValue","minValue","maxValue","className"]),D=n.constructCategoryColors(d,g),N=n.getYAxisDomain(p,q,j);return u.default.createElement("div",Object.assign({ref:c,className:r.tremorTwMerge("w-28 h-12",w)},y),u.default.createElement(o.ResponsiveContainer,{className:"h-full w-full"},(null==m?void 0:m.length)?u.default.createElement(o.BarChart,{data:m,stackOffset:x?"expand":"none",margin:{top:0,left:-1.5,right:-1.5,bottom:0}},u.default.createElement(o.YAxis,{hide:!0,domain:N}),u.default.createElement(o.XAxis,{hide:!0,dataKey:f}),d.map((e=>{var l;return u.default.createElement(o.Bar,{className:r.tremorTwMerge(i.getColorClassNames(null!==(l=D.get(e))&&void 0!==l?l:a.BaseColors.Gray,t.colorPalette.background).fillColor),key:e,name:e,type:"linear",stackId:h||x?"a":void 0,dataKey:e,fill:"",isAnimationActive:C,animationDuration:v})}))):u.default.createElement(s,{noDataText:b})))}));m.displayName="SparkBarChart",module.exports=m;
