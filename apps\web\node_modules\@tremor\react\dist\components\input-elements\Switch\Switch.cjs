'use client';
"use strict";var e=require("tslib"),r=require("@headlessui/react"),t=require("../../../hooks/useInternalState.cjs"),o=require("react"),a=require("../../../lib/theme.cjs"),l=require("../../../lib/tremorTwMerge.cjs"),n=require("../../../lib/utils.cjs"),s=require("../../util-elements/Tooltip/Tooltip.cjs");function d(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var i=d(o);const u=n.makeClassName("Switch"),c=i.default.forwardRef(((d,c)=>{const{checked:m,defaultChecked:g=!1,onChange:b,color:f,name:k,error:h,errorMessage:p,disabled:w,required:C,tooltip:x,id:M}=d,T=e.__rest(d,["checked","defaultChecked","onChange","color","name","error","errorMessage","disabled","required","tooltip","id"]),N=(e=>({bgColor:e?n.getColorClassNames(e,a.colorPalette.background).bgColor:"bg-tremor-brand dark:bg-dark-tremor-brand",ringColor:e?n.getColorClassNames(e,a.colorPalette.ring).ringColor:"ring-tremor-brand-muted dark:ring-dark-tremor-brand-muted"}))(f),[q,j]=t(g,m),[v,E]=o.useState(!1),{tooltipProps:y,getReferenceProps:S}=s.useTooltip(300);return i.default.createElement("div",{className:"flex flex-row items-center justify-start"},i.default.createElement(s.default,Object.assign({text:x},y)),i.default.createElement("div",Object.assign({ref:n.mergeRefs([c,y.refs.setReference]),className:l.tremorTwMerge(u("root"),"flex flex-row relative h-5")},T,S),i.default.createElement("input",{type:"checkbox",className:l.tremorTwMerge(u("input"),"absolute w-5 h-5 cursor-pointer left-0 top-0 opacity-0"),name:k,required:C,checked:q,onChange:e=>{e.preventDefault()}}),i.default.createElement(r.Switch,{checked:q,onChange:e=>{j(e),null==b||b(e)},disabled:w,className:l.tremorTwMerge(u("switch"),"w-10 h-5 group relative inline-flex shrink-0 cursor-pointer items-center justify-center rounded-tremor-full","focus:outline-none",w?"cursor-not-allowed":""),onFocus:()=>E(!0),onBlur:()=>E(!1),id:M},i.default.createElement("span",{className:l.tremorTwMerge(u("sr-only"),"sr-only")},"Switch ",q?"on":"off"),i.default.createElement("span",{"aria-hidden":"true",className:l.tremorTwMerge(u("background"),q?N.bgColor:"bg-tremor-border dark:bg-dark-tremor-border","pointer-events-none absolute mx-auto h-3 w-9 rounded-tremor-full transition-colors duration-100 ease-in-out")}),i.default.createElement("span",{"aria-hidden":"true",className:l.tremorTwMerge(u("round"),q?l.tremorTwMerge(N.bgColor,"translate-x-5 border-tremor-background dark:border-dark-tremor-background"):"translate-x-0 bg-tremor-border dark:bg-dark-tremor-border border-tremor-background dark:border-dark-tremor-background","pointer-events-none absolute left-0 inline-block h-5 w-5 transform rounded-tremor-full border-2 shadow-tremor-input duration-100 ease-in-out transition",v?l.tremorTwMerge("ring-2",N.ringColor):"")}))),h&&p?i.default.createElement("p",{className:l.tremorTwMerge(u("errorMessage"),"text-sm text-red-500 mt-1 ")},p):null)}));c.displayName="Switch",module.exports=c;
