'use client';
"use strict";var e=require("tslib"),r=require("../../../lib/tremorTwMerge.cjs"),t=require("react"),a=require("date-fns"),o=require("date-fns/locale"),n=require("@headlessui/react"),l=require("../../../assets/CalendarIcon.cjs"),d=require("../../../assets/XCircleIcon.cjs"),s=require("../Calendar/Calendar.cjs"),i=require("./datePickerUtils.cjs"),u=require("../../../hooks/useInternalState.cjs"),c=require("../DateRangePicker/dateRangePickerUtils.cjs"),m=require("../selectUtils.cjs");function b(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var f=b(t);const k=a.startOfToday(),p=f.default.forwardRef(((b,p)=>{var g;const{value:w,defaultValue:h,onValueChange:v,minDate:y,maxDate:x,placeholder:N="Select date",disabled:q=!1,locale:D=o.enUS,enableClear:C=!0,displayFormat:T,className:j,enableYearNavigation:M=!1,weekStartsOn:E=0,disabledDates:P}=b,S=e.__rest(b,["value","defaultValue","onValueChange","minDate","maxDate","placeholder","disabled","locale","enableClear","displayFormat","className","enableYearNavigation","weekStartsOn","disabledDates"]),[O,V]=u(h,w),F=t.useMemo((()=>{const e=[];return y&&e.push({before:y}),x&&e.push({after:x}),[...e,...null!=P?P:[]]}),[y,x,P]),I=O?c.formatSelectedDates(O,void 0,D,T):N,U=a.startOfMonth(null!==(g=null!=O?O:x)&&void 0!==g?g:k),R=C&&!q;return f.default.createElement(n.Popover,Object.assign({ref:p,as:"div",className:r.tremorTwMerge("relative w-full min-w-[10rem] text-tremor-default","focus:ring-2 focus:ring-tremor-brand-muted dark:focus:ring-dark-tremor-brand-muted",j)},S),f.default.createElement(n.PopoverButton,{disabled:q,className:r.tremorTwMerge("w-full outline-none text-left whitespace-nowrap truncate focus:ring-2 transition duration-100 rounded-tremor-default flex flex-nowrap border pl-3 py-2","border-tremor-border shadow-tremor-input text-tremor-content-emphasis focus:border-tremor-brand-subtle focus:ring-tremor-brand-muted","dark:border-dark-tremor-border dark:shadow-dark-tremor-input dark:text-dark-tremor-content-emphasis dark:focus:border-dark-tremor-brand-subtle dark:focus:ring-dark-tremor-brand-muted",R?"pr-8":"pr-4",m.getSelectButtonColors(m.hasValue(O),q))},f.default.createElement(l,{className:r.tremorTwMerge(i.makeDatePickerClassName("calendarIcon"),"flex-none shrink-0 h-5 w-5 mr-2 -ml-0.5","text-tremor-content-subtle","dark:text-dark-tremor-content-subtle"),"aria-hidden":"true"}),f.default.createElement("p",{className:"truncate"},I)),R&&O?f.default.createElement("button",{type:"button",className:r.tremorTwMerge("absolute outline-none inset-y-0 right-0 flex items-center transition duration-100 mr-4"),onClick:e=>{e.preventDefault(),null==v||v(void 0),V(void 0)}},f.default.createElement(d,{className:r.tremorTwMerge("flex-none h-4 w-4","text-tremor-content-subtle","dark:text-dark-tremor-content-subtle")})):null,f.default.createElement(n.Transition,{enter:"transition ease duration-100 transform",enterFrom:"opacity-0 -translate-y-4",enterTo:"opacity-100 translate-y-0",leave:"transition ease duration-100 transform",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 -translate-y-4"},f.default.createElement(n.PopoverPanel,{anchor:"bottom start",className:r.tremorTwMerge("z-10 min-w-min divide-y overflow-y-auto outline-none rounded-tremor-default p-3 border [--anchor-gap:4px]","bg-tremor-background border-tremor-border divide-tremor-border shadow-tremor-dropdown","dark:bg-dark-tremor-background dark:border-dark-tremor-border dark:divide-dark-tremor-border dark:shadow-dark-tremor-dropdown")},(({close:e})=>f.default.createElement(s,{showOutsideDays:!0,mode:"single",defaultMonth:U,selected:O,weekStartsOn:E,onSelect:r=>{null==v||v(r),V(r),e()},locale:D,disabled:F,enableYearNavigation:M})))))}));p.displayName="DatePicker",module.exports=p;
