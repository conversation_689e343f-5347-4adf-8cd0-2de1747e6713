"use strict";var e=require("tslib"),t=require("react"),r=require("../common/ChartTooltip.cjs"),a=require("../../../lib/constants.cjs"),l=require("../../../lib/theme.cjs"),o=require("../../../lib/tremorTwMerge.cjs"),n=require("../../../lib/utils.cjs"),s=require("../common/NoData.cjs"),i=require("../../../assets/ArrowRightIcon.cjs");function u(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var d=u(t);const c=["100%","75%","50%","25%","0%"],m=d.default.forwardRef(((t,u)=>{var m,f;const{data:g,evolutionGradient:h=!1,gradient:x=!0,valueFormatter:p=n.defaultValueFormatter,className:v,calculateFrom:w="first",color:b,variant:y="base",showGridLines:E=!0,showYAxis:C="previous"!==w,showXAxis:T=!0,showArrow:X=!0,xAxisLabel:$="",yAxisLabel:M="",yAxisPadding:N=(C?M?70:45:0),showTooltip:k=!0,onValueChange:H,customTooltip:B,noDataText:A,rotateLabelX:F,barGap:L="20%"}=t,z=e.__rest(t,["data","evolutionGradient","gradient","valueFormatter","className","calculateFrom","color","variant","showGridLines","showYAxis","showXAxis","showArrow","xAxisLabel","yAxisLabel","yAxisPadding","showTooltip","onValueChange","customTooltip","noDataText","rotateLabelX","barGap"]),Y=T&&$?25:15,j=B,S=d.default.useRef(null),q=d.default.useRef(null),[G,O]=d.default.useState(0),[P,R]=d.default.useState(0),[V,I]=d.default.useState({x:0,y:0}),[D,_]=d.default.useState(void 0),W=!!H;const K=d.default.useMemo((()=>Math.max(...g.map((e=>e.value)))),[g]),J=G-10-N,Q=d.default.useMemo((()=>{if("number"==typeof L)return L;if("string"==typeof L&&L.endsWith("%")){const e=parseFloat(L.slice(0,-1));return J*e/100/(g.length-1)}return console.error('Invalid barGap value. It must be a number or a percentage string (e.g., "10%").'),30}),[J,g.length,L]),U=d.default.useMemo((()=>(J-(g.length-1)*Q-Q)/g.length),[J,Q,g.length]),Z=P-10-(T?((null==F?void 0:F.xAxisHeight)||Y)+(T&&$?30:10):0),ee="previous"===w,te="center"===y;d.default.useLayoutEffect((()=>{const e=()=>{if(S.current){const e=S.current.getBoundingClientRect();O(e.width),R(e.height)}};return e(),window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}}),[v]),d.default.useEffect((()=>{const e=()=>{if(q.current){const e=q.current.getBoundingClientRect();e.right>window.innerWidth&&(q.current.style.left=G-e.width+"px")}};return e(),window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}}),[V,G]);const re=d.default.useMemo((()=>Z<=0?[]:g.reduce(((e,t,r)=>{var a,l,o,n;const s=e[r-1],i=t.value,u=ee&&null!==(a=null==s?void 0:s.value)&&void 0!==a?a:K,d=ee&&null!==(l=null==s?void 0:s.barHeight)&&void 0!==l?l:Z,c=i/u,m=c*d,f=r*(U+Q)+.5*Q,h=d-m+(ee?Z-(null!==(o=null==s?void 0:s.barHeight)&&void 0!==o?o:Z):0),x=null===(n=g[r+1])||void 0===n?void 0:n.value,p=x/u,v=p*d,w=(r+1)*(U+Q)+.5*Q;return e.push({value:i,normalizedValue:c,name:t.name,startX:f,startY:h,barHeight:m,nextValue:x,nextNormalizedValue:p,nextBarHeight:v,nextStartX:w}),e}),[])),[g,Z,ee,U,Q,K]),ae=e=>{var t;const r=null===(t=S.current)||void 0===t?void 0:t.getBoundingClientRect();if(!r)return;const s=r.x,i=r.y+window.scrollY,u=s+window.scrollX+N+5,d=u+(r.width-N-5),c=i+(r.height-5-(T?Y:0));if(e.pageX<u||e.pageX>d||e.pageY<i||e.pageY>c)return console.log("out of bounds"),I({x:0,y:0});const m=e.pageX-s-U/2-N-5,f=re.reduce(((e,t)=>Math.abs(t.startX-m)<Math.abs(e.startX-m)?t:e)),g=re.findIndex((e=>e===f));I({x:f.startX,y:f.startY,data:{dataKey:f.name,name:f.name,value:f.value,color:null!=b?b:a.BaseColors.Blue,className:o.tremorTwMerge(n.getColorClassNames(null!=b?b:a.BaseColors.Blue,l.colorPalette.text).textColor,W?"cursor-pointer":"cursor-default"),fill:"",payload:f},index:g})};return d.default.createElement("div",Object.assign({ref:u,className:o.tremorTwMerge("tremor-wrapper relative w-full h-80",v)},z),(null==g?void 0:g.length)?d.default.createElement(d.default.Fragment,null,d.default.createElement("svg",{ref:S,xmlns:"http://www.w3.org/2000/svg",className:o.tremorTwMerge("w-full h-full"),onMouseMove:e=>{const t={clientX:e.clientX,clientY:e.clientY,pageX:e.pageX,pageY:e.pageY};ae(t)},onTouchMove:e=>{const t=e.touches[0];ae(t)},onMouseLeave:()=>I({x:0,y:0}),onTouchEnd:()=>I({x:0,y:0})},c.map(((e,t)=>d.default.createElement(d.default.Fragment,{key:`y-axis-${t}`},E?d.default.createElement("line",{x1:N+5,y1:t*Z/4+5,x2:G-5,y2:t*Z/4+5,stroke:"currentColor",className:o.tremorTwMerge("stroke-1","stroke-tremor-border","dark:stroke-dark-tremor-border")}):null,d.default.createElement("text",{x:N-10+5,y:t*Z/4+5+5,textAnchor:"end",fill:"",stroke:"",className:o.tremorTwMerge("text-tremor-label","fill-tremor-content","dark:fill-dark-tremor-content")},e)))),re.map(((e,t)=>{var r,s;return d.default.createElement("g",{key:`bar-${t}`},d.default.createElement("rect",{x:e.startX-.5*Q+5+N,y:5,width:U+Q,height:Z,fill:"currentColor",className:o.tremorTwMerge("z-0",V.index===t?"text-[#d1d5db]/15":"text-transparent")}),x?d.default.createElement("rect",{x:e.startX+5+N,y:Z-(ee&&(null===(r=re[t-1])||void 0===r?void 0:r.barHeight)||Z)+5,width:U,height:(Z-e.barHeight-(ee&&Z-(null===(s=re[t-1])||void 0===s?void 0:s.barHeight)||0))/(te?2:1),fill:"url(#base-gradient)",className:o.tremorTwMerge(D&&D.index!==t?"opacity-30":"")}):null,d.default.createElement("rect",{x:e.startX+5+N,y:(te?Z/2-e.barHeight/2:e.startY)+5,width:U,height:e.barHeight,fill:"currentColor",className:o.tremorTwMerge(n.getColorClassNames(null!=b?b:a.BaseColors.Blue,l.colorPalette.text).textColor,D&&D.index!==t?"opacity-30":"",W?"cursor-pointer":"cursor-default"),onClick:r=>function(e,t,r){r.stopPropagation(),W&&(t===(null==D?void 0:D.index)?(_(void 0),H(void 0)):(_({data:e,index:t}),H({eventType:"bar",categoryClicked:e.name,[e.name]:e.value,percentage:e.normalizedValue})))}(e,t,r)}),x&&te?d.default.createElement("rect",{x:e.startX+5+N,y:Z/2+e.barHeight/2+5,width:U,height:(Z-e.barHeight)/2,fill:"url(#base-gradient-revert)",className:o.tremorTwMerge(D&&D.index!==t?"opacity-30":"")}):null,T?d.default.createElement("foreignObject",{x:e.startX+5+N,y:Z+5+10,width:U,height:(null==F?void 0:F.xAxisHeight)||Y,transform:F?`rotate(${null==F?void 0:F.angle}, ${e.startX+U/2+5+N}, ${Z+((null==F?void 0:F.xAxisHeight)||Y)/2+5+((null==F?void 0:F.verticalShift)||0)})`:void 0},d.default.createElement("div",{className:o.tremorTwMerge("truncate text-center !text-tremor-label","text-tremor-content","dark:text-dark-tremor-content"),title:e.name},e.name)):null)})),re.map(((e,t)=>d.default.createElement(d.default.Fragment,{key:`gradient-${t}`},t<g.length-1&&h?d.default.createElement(d.default.Fragment,null,te?d.default.createElement(d.default.Fragment,null,d.default.createElement("polygon",{points:`\n                                    ${e.startX+U+5+N}, ${Z/2+e.nextBarHeight/4+5}\n                                    ${e.nextStartX+5+N}, ${Z/2+e.nextBarHeight/4+5}\n                                    ${e.nextStartX+5+N}, ${Z/2-e.nextBarHeight/2+5}\n                                    ${e.startX+U+5+N}, ${Z/2-e.barHeight/2+5}\n                                  `,fill:"url(#base-gradient)",className:o.tremorTwMerge("z-10",D&&D.index!==t?"opacity-30":"")}),d.default.createElement("polygon",{points:`\n                                    ${e.startX+U+5+N}, ${Z/2+e.barHeight/2+5}\n                                    ${e.nextStartX+5+N}, ${Z/2+e.nextBarHeight/2+5}\n                                    ${e.nextStartX+5+N}, ${Z/2-e.nextBarHeight/4+5}\n                                    ${e.startX+U+5+N}, ${Z/2-e.nextBarHeight/4+5}\n                                  `,fill:"url(#base-gradient-revert)",className:o.tremorTwMerge("z-10",D&&D.index!==t?"opacity-30":"")})):d.default.createElement("polygon",{points:`\n                                  ${e.startX+U+5+N}, ${e.startY+5} \n                                  ${e.nextStartX+5+N}, ${Z-e.nextBarHeight+5} \n                                  ${e.nextStartX+5+N}, ${Z+5} \n                                  ${e.startX+U+5+N}, ${Z+5}\n                                `,fill:"url(#base-gradient)",className:o.tremorTwMerge("z-10",D&&D.index!==t?"opacity-30":"")})):null,t<g.length-1&&T&&X&&Q>=14?d.default.createElement("foreignObject",{x:e.startX+U+5+N-6+Q/2,y:Z+5+11,width:12,height:(null==F?void 0:F.xAxisHeight)||Y},d.default.createElement("div",{className:o.tremorTwMerge("text-tremor-content","dark:text-dark-tremor-content")},d.default.createElement(i,{className:"size-3.5 shrink-0"}))):null))),d.default.createElement("linearGradient",{id:"base-gradient",x1:"0%",y1:"0%",x2:"0%",y2:"100%",className:o.tremorTwMerge(n.getColorClassNames(null!=b?b:a.BaseColors.Blue,l.colorPalette.text).textColor)},d.default.createElement("stop",{offset:"5%",stopColor:"currentColor",stopOpacity:.4}),d.default.createElement("stop",{offset:"95%",stopColor:"currentColor",stopOpacity:0})),d.default.createElement("linearGradient",{id:"base-gradient-revert",x1:"0%",y1:"0%",x2:"0%",y2:"100%",className:o.tremorTwMerge(n.getColorClassNames(null!=b?b:a.BaseColors.Blue,l.colorPalette.text).textColor)},d.default.createElement("stop",{offset:"5%",stopColor:"currentColor",stopOpacity:0}),d.default.createElement("stop",{offset:"95%",stopColor:"currentColor",stopOpacity:.4})),T&&$?d.default.createElement("text",{x:G/2+N/2,y:Z+5+50,style:{textAnchor:"middle"},fill:"",stroke:"",className:o.tremorTwMerge("text-tremor-default cursor-default font-medium","fill-tremor-content-emphasis","dark:fill-dark-tremor-content-emphasis")},$):null,C&&M?d.default.createElement("text",{x:-5,y:Z/2+10,textAnchor:"middle",style:{textAnchor:"middle"},transform:`rotate(-90, 0, ${Z/2})`,fill:"",stroke:"",className:o.tremorTwMerge("text-tremor-default cursor-default font-medium","fill-tremor-content-emphasis","dark:fill-dark-tremor-content-emphasis")},M):null),k?d.default.createElement("div",{ref:q,className:o.tremorTwMerge("absolute top-0 pointer-events-none",V.data?"visible":"hidden"),tabIndex:-1,role:"dialog",style:{left:V.x+.66*U}},j?d.default.createElement(j,{payload:V.data?[V.data]:[],active:!!V.data,label:null===(m=V.data)||void 0===m?void 0:m.name}):d.default.createElement(r.ChartTooltipFrame,null,d.default.createElement("div",{className:o.tremorTwMerge("border-tremor-border border-b px-4 py-2","dark:border-dark-tremor-border")},d.default.createElement("p",{className:o.tremorTwMerge("font-medium","text-tremor-content-emphasis","dark:text-dark-tremor-content-emphasis")},null===(f=null==V?void 0:V.data)||void 0===f?void 0:f.name)),d.default.createElement("div",{className:o.tremorTwMerge("px-4 py-2 space-y-1")},V.data?d.default.createElement(r.ChartTooltipRow,{value:p(V.data.value),name:`${(100*V.data.payload.normalizedValue).toFixed(2)}%`,color:null!=b?b:a.BaseColors.Blue}):null))):null):d.default.createElement(s,{noDataText:A}))}));m.displayName="FunnelChart";module.exports=t=>{var{data:r}=t,a=e.__rest(t,["data"]);const l=r?((e,t)=>{if(e&&e.length>0){if("previous"===t&&e[0].value<=0)return`The value of the first item "${e[0].name}" is not greater than 0. This is not allowed when setting the "calculateFrom" prop to "previous". Please enter a value greater than 0.`;for(const t of e)if(t.value<0)return`Item "${t.name}" has a negative value: ${t.value}. This is not allowed. The value must be greater than or equal to 0.`}return null})(r,a.calculateFrom):null;return l?d.default.createElement(s,{className:"h-full w-full p-6",noDataText:`Calculation error: ${l}`}):d.default.createElement(m,Object.assign({data:r},a))};
