'use client';
import{__rest as e}from"tslib";import r from"react";import"../../../contexts/BaseColorContext.js";import"../../../contexts/IndexContext.js";import t from"../../../contexts/RootStylesContext.js";import"../../../contexts/SelectedValueContext.js";import{tremorTwMerge as o}from"../../../lib/tremorTwMerge.js";import{makeClassName as d}from"../../../lib/utils.js";const l=d("AccordionList"),n=r.forwardRef(((d,n)=>{const{children:a,className:m}=d,s=e(d,["children","className"]),c=r.Children.count(a);return r.createElement("div",Object.assign({ref:n,className:o(l("root"),"rounded-tremor-default","shadow-tremor-card","dark:shadow-dark-tremor-card",m)},s),r.Children.map(a,((e,d)=>0===d?r.createElement(t.Provider,{value:o("rounded-t-tremor-default border")},r.cloneElement(e)):d===c-1?r.createElement(t.Provider,{value:o("rounded-b-tremor-default border-l border-r border-b")},r.cloneElement(e)):r.createElement(t.Provider,{value:o("border-l border-r border-b")},r.cloneElement(e)))))}));n.displayName="AccordionList";export{n as default};
