import{__rest as e}from"tslib";import r from"react";import{colorPalette as t}from"../../../lib/theme.js";import{tremorTwMerge as o}from"../../../lib/tremorTwMerge.js";import{getColorClassNames as a,makeClassName as l}from"../../../lib/utils.js";const m=l("Callout"),d=r.forwardRef(((l,d)=>{const{title:s,icon:i,color:n,className:c,children:b}=l,f=e(l,["title","icon","color","className","children"]),p=i;return r.createElement("div",Object.assign({ref:d,className:o(m("root"),"flex flex-col overflow-hidden rounded-tremor-default text-tremor-default border-l-4 py-3 pr-3 pl-4",n?o(a(n,t.background).bgColor,a(n,t.darkBorder).borderColor,a(n,t.darkText).textColor,"dark:bg-opacity-10 bg-opacity-10"):o("bg-tremor-brand-faint border-tremor-brand-emphasis text-tremor-brand-emphasis","dark:bg-dark-tremor-brand-muted/70 dark:border-dark-tremor-brand-emphasis dark:text-dark-tremor-brand-emphasis"),c)},f),r.createElement("div",{className:o(m("header"),"flex items-start")},p?r.createElement(p,{className:o(m("icon"),"flex-none h-5 w-5 mr-1.5")}):null,r.createElement("h4",{className:o(m("title"),"font-semibold")},s)),r.createElement("p",{className:o(m("body"),"overflow-y-auto",b?"mt-2":"")},b))}));d.displayName="Callout";export{d as default};
