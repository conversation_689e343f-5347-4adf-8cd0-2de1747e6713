import{__rest as e}from"tslib";import{tremorTwMerge as r}from"../../../lib/tremorTwMerge.js";import{makeClassName as t}from"../../../lib/utils.js";import a from"react";const m=t("Divider"),o=a.forwardRef(((t,o)=>{const{className:l,children:d}=t,s=e(t,["className","children"]);return a.createElement("div",Object.assign({ref:o,className:r(m("root"),"w-full mx-auto my-6 flex justify-between gap-3 items-center text-tremor-default","text-tremor-content","dark:text-dark-tremor-content",l)},s),d?a.createElement(a.Fragment,null,a.createElement("div",{className:r("w-full h-[1px] bg-tremor-border dark:bg-dark-tremor-border")}),a.createElement("div",{className:r("text-inherit whitespace-nowrap")},d),a.createElement("div",{className:r("w-full h-[1px] bg-tremor-border dark:bg-dark-tremor-border")})):a.createElement("div",{className:r("w-full h-[1px] bg-tremor-border dark:bg-dark-tremor-border")}))}));o.displayName="Divider";export{o as default};
