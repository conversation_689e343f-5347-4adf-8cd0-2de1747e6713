"use client";import{useFocusRing as pe}from"@react-aria/focus";import{useHover as me}from"@react-aria/interactions";import x,{Fragment as z,createContext as de,useCallback as ce,useContext as fe,useEffect as Te,useMemo as H,useReducer as ye,useRef as Y,useState as Ie}from"react";import{flushSync as L}from"react-dom";import{useActivePress as ge}from'../../hooks/use-active-press.js';import{useDidElementMove as Ee}from'../../hooks/use-did-element-move.js';import{useDisposables as Me}from'../../hooks/use-disposables.js';import{useElementSize as Se}from'../../hooks/use-element-size.js';import{useEvent as E}from'../../hooks/use-event.js';import{useId as U}from'../../hooks/use-id.js';import{useInertOthers as Ae}from'../../hooks/use-inert-others.js';import{useIsoMorphicEffect as B}from'../../hooks/use-iso-morphic-effect.js';import{useOnDisappear as be}from'../../hooks/use-on-disappear.js';import{useOutsideClick as Pe}from'../../hooks/use-outside-click.js';import{useOwnerDocument as ve}from'../../hooks/use-owner.js';import{useResolveButtonType as xe}from'../../hooks/use-resolve-button-type.js';import{useScrollLock as Re}from'../../hooks/use-scroll-lock.js';import{useSyncRefs as N}from'../../hooks/use-sync-refs.js';import{useTextValue as _e}from'../../hooks/use-text-value.js';import{useTrackedPointer as De}from'../../hooks/use-tracked-pointer.js';import{transitionDataAttributes as he,useTransition as Ce}from'../../hooks/use-transition.js';import{useTreeWalker as Fe}from'../../hooks/use-tree-walker.js';import{FloatingProvider as Le,useFloatingPanel as Oe,useFloatingPanelProps as Ge,useFloatingReference as He,useFloatingReferenceProps as Ue,useResolvedAnchor as Be}from'../../internal/floating.js';import{OpenClosedProvider as Ne,State as k,useOpenClosed as ke}from'../../internal/open-closed.js';import{isDisabledReactIssue7711 as we}from'../../utils/bugs.js';import{Focus as g,calculateActiveIndex as W}from'../../utils/calculate-active-index.js';import{disposables as Ke}from'../../utils/disposables.js';import{Focus as Z,FocusableMode as We,focusFrom as je,isFocusableElement as Qe,restoreFocusIfNecessary as ee,sortByDomNode as Je}from'../../utils/focus-management.js';import{match as te}from'../../utils/match.js';import{RenderFeatures as ne,forwardRefWithAs as R,mergeProps as re,useRender as _}from'../../utils/render.js';import{useDescriptions as Ve}from'../description/description.js';import{Keys as y}from'../keyboard.js';import{useLabelContext as Xe,useLabels as oe}from'../label/label.js';import{Portal as $e}from'../portal/portal.js';var qe=(r=>(r[r.Open=0]="Open",r[r.Closed=1]="Closed",r))(qe||{}),ze=(r=>(r[r.Pointer=0]="Pointer",r[r.Other=1]="Other",r))(ze||{}),Ye=(a=>(a[a.OpenMenu=0]="OpenMenu",a[a.CloseMenu=1]="CloseMenu",a[a.GoToItem=2]="GoToItem",a[a.Search=3]="Search",a[a.ClearSearch=4]="ClearSearch",a[a.RegisterItem=5]="RegisterItem",a[a.UnregisterItem=6]="UnregisterItem",a[a.SetButtonElement=7]="SetButtonElement",a[a.SetItemsElement=8]="SetItemsElement",a))(Ye||{});function j(e,n=r=>r){let r=e.activeItemIndex!==null?e.items[e.activeItemIndex]:null,l=Je(n(e.items.slice()),u=>u.dataRef.current.domRef.current),o=r?l.indexOf(r):null;return o===-1&&(o=null),{items:l,activeItemIndex:o}}let Ze={[1](e){return e.menuState===1?e:{...e,activeItemIndex:null,menuState:1}},[0](e){return e.menuState===0?e:{...e,__demoMode:!1,menuState:0}},[2]:(e,n)=>{var u,p,s,m,a;if(e.menuState===1)return e;let r={...e,searchQuery:"",activationTrigger:(u=n.trigger)!=null?u:1,__demoMode:!1};if(n.focus===g.Nothing)return{...r,activeItemIndex:null};if(n.focus===g.Specific)return{...r,activeItemIndex:e.items.findIndex(t=>t.id===n.id)};if(n.focus===g.Previous){let t=e.activeItemIndex;if(t!==null){let d=e.items[t].dataRef.current.domRef,f=W(n,{resolveItems:()=>e.items,resolveActiveIndex:()=>e.activeItemIndex,resolveId:c=>c.id,resolveDisabled:c=>c.dataRef.current.disabled});if(f!==null){let c=e.items[f].dataRef.current.domRef;if(((p=d.current)==null?void 0:p.previousElementSibling)===c.current||((s=c.current)==null?void 0:s.previousElementSibling)===null)return{...r,activeItemIndex:f}}}}else if(n.focus===g.Next){let t=e.activeItemIndex;if(t!==null){let d=e.items[t].dataRef.current.domRef,f=W(n,{resolveItems:()=>e.items,resolveActiveIndex:()=>e.activeItemIndex,resolveId:c=>c.id,resolveDisabled:c=>c.dataRef.current.disabled});if(f!==null){let c=e.items[f].dataRef.current.domRef;if(((m=d.current)==null?void 0:m.nextElementSibling)===c.current||((a=c.current)==null?void 0:a.nextElementSibling)===null)return{...r,activeItemIndex:f}}}}let l=j(e),o=W(n,{resolveItems:()=>l.items,resolveActiveIndex:()=>l.activeItemIndex,resolveId:t=>t.id,resolveDisabled:t=>t.dataRef.current.disabled});return{...r,...l,activeItemIndex:o}},[3]:(e,n)=>{let l=e.searchQuery!==""?0:1,o=e.searchQuery+n.value.toLowerCase(),p=(e.activeItemIndex!==null?e.items.slice(e.activeItemIndex+l).concat(e.items.slice(0,e.activeItemIndex+l)):e.items).find(m=>{var a;return((a=m.dataRef.current.textValue)==null?void 0:a.startsWith(o))&&!m.dataRef.current.disabled}),s=p?e.items.indexOf(p):-1;return s===-1||s===e.activeItemIndex?{...e,searchQuery:o}:{...e,searchQuery:o,activeItemIndex:s,activationTrigger:1}},[4](e){return e.searchQuery===""?e:{...e,searchQuery:"",searchActiveItemIndex:null}},[5]:(e,n)=>{let r=j(e,l=>[...l,{id:n.id,dataRef:n.dataRef}]);return{...e,...r}},[6]:(e,n)=>{let r=j(e,l=>{let o=l.findIndex(u=>u.id===n.id);return o!==-1&&l.splice(o,1),l});return{...e,...r,activationTrigger:1}},[7]:(e,n)=>e.buttonElement===n.element?e:{...e,buttonElement:n.element},[8]:(e,n)=>e.itemsElement===n.element?e:{...e,itemsElement:n.element}},Q=de(null);Q.displayName="MenuContext";function w(e){let n=fe(Q);if(n===null){let r=new Error(`<${e} /> is missing a parent <Menu /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(r,w),r}return n}function et(e,n){return te(n.type,Ze,e,n)}let tt=z;function nt(e,n){let{__demoMode:r=!1,...l}=e,o=ye(et,{__demoMode:r,menuState:r?0:1,buttonElement:null,itemsElement:null,items:[],searchQuery:"",activeItemIndex:null,activationTrigger:1}),[{menuState:u,itemsElement:p,buttonElement:s},m]=o,a=N(n);Pe(u===0,[s,p],(b,S)=>{m({type:1}),Qe(S,We.Loose)||(b.preventDefault(),s==null||s.focus())});let d=E(()=>{m({type:1})}),f=H(()=>({open:u===0,close:d}),[u,d]),c={ref:a},A=_();return x.createElement(Le,null,x.createElement(Q.Provider,{value:o},x.createElement(Ne,{value:te(u,{[0]:k.Open,[1]:k.Closed})},A({ourProps:c,theirProps:l,slot:f,defaultTag:tt,name:"Menu"}))))}let rt="button";function ot(e,n){var h;let r=U(),{id:l=`headlessui-menu-button-${r}`,disabled:o=!1,autoFocus:u=!1,...p}=e,[s,m]=w("Menu.Button"),a=Ue(),t=N(n,He(),E(T=>m({type:7,element:T}))),d=E(T=>{switch(T.key){case y.Space:case y.Enter:case y.ArrowDown:T.preventDefault(),T.stopPropagation(),L(()=>m({type:0})),m({type:2,focus:g.First});break;case y.ArrowUp:T.preventDefault(),T.stopPropagation(),L(()=>m({type:0})),m({type:2,focus:g.Last});break}}),f=E(T=>{switch(T.key){case y.Space:T.preventDefault();break}}),c=E(T=>{var F;if(we(T.currentTarget))return T.preventDefault();o||(s.menuState===0?(L(()=>m({type:1})),(F=s.buttonElement)==null||F.focus({preventScroll:!0})):(T.preventDefault(),m({type:0})))}),{isFocusVisible:A,focusProps:b}=pe({autoFocus:u}),{isHovered:S,hoverProps:D}=me({isDisabled:o}),{pressed:M,pressProps:P}=ge({disabled:o}),v=H(()=>({open:s.menuState===0,active:M||s.menuState===0,disabled:o,hover:S,focus:A,autofocus:u}),[s,S,A,M,o,u]),C=re(a(),{ref:t,id:l,type:xe(e,s.buttonElement),"aria-haspopup":"menu","aria-controls":(h=s.itemsElement)==null?void 0:h.id,"aria-expanded":s.menuState===0,disabled:o||void 0,autoFocus:u,onKeyDown:d,onKeyUp:f,onClick:c},b,D,P);return _()({ourProps:C,theirProps:p,slot:v,defaultTag:rt,name:"Menu.Button"})}let at="div",lt=ne.RenderStrategy|ne.Static;function it(e,n){var J,V;let r=U(),{id:l=`headlessui-menu-items-${r}`,anchor:o,portal:u=!1,modal:p=!0,transition:s=!1,...m}=e,a=Be(o),[t,d]=w("Menu.Items"),[f,c]=Oe(a),A=Ge(),[b,S]=Ie(null),D=N(n,a?f:null,E(i=>d({type:8,element:i})),S),M=ve(t.itemsElement);a&&(u=!0);let P=ke(),[v,C]=Ce(s,b,P!==null?(P&k.Open)===k.Open:t.menuState===0);be(v,t.buttonElement,()=>{d({type:1})});let O=t.__demoMode?!1:p&&t.menuState===0;Re(O,M);let h=t.__demoMode?!1:p&&t.menuState===0;Ae(h,{allowed:ce(()=>[t.buttonElement,t.itemsElement],[t.buttonElement,t.itemsElement])});let T=t.menuState!==0,K=Ee(T,t.buttonElement)?!1:v;Te(()=>{let i=t.itemsElement;i&&t.menuState===0&&i!==(M==null?void 0:M.activeElement)&&i.focus({preventScroll:!0})},[t.menuState,t.itemsElement,M]),Fe(t.menuState===0,{container:t.itemsElement,accept(i){return i.getAttribute("role")==="menuitem"?NodeFilter.FILTER_REJECT:i.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(i){i.setAttribute("role","none")}});let I=Me(),G=E(i=>{var X,$,q;switch(I.dispose(),i.key){case y.Space:if(t.searchQuery!=="")return i.preventDefault(),i.stopPropagation(),d({type:3,value:i.key});case y.Enter:if(i.preventDefault(),i.stopPropagation(),d({type:1}),t.activeItemIndex!==null){let{dataRef:ue}=t.items[t.activeItemIndex];($=(X=ue.current)==null?void 0:X.domRef.current)==null||$.click()}ee(t.buttonElement);break;case y.ArrowDown:return i.preventDefault(),i.stopPropagation(),d({type:2,focus:g.Next});case y.ArrowUp:return i.preventDefault(),i.stopPropagation(),d({type:2,focus:g.Previous});case y.Home:case y.PageUp:return i.preventDefault(),i.stopPropagation(),d({type:2,focus:g.First});case y.End:case y.PageDown:return i.preventDefault(),i.stopPropagation(),d({type:2,focus:g.Last});case y.Escape:i.preventDefault(),i.stopPropagation(),L(()=>d({type:1})),(q=t.buttonElement)==null||q.focus({preventScroll:!0});break;case y.Tab:i.preventDefault(),i.stopPropagation(),L(()=>d({type:1})),je(t.buttonElement,i.shiftKey?Z.Previous:Z.Next);break;default:i.key.length===1&&(d({type:3,value:i.key}),I.setTimeout(()=>d({type:4}),350));break}}),ae=E(i=>{switch(i.key){case y.Space:i.preventDefault();break}}),le=H(()=>({open:t.menuState===0}),[t.menuState]),ie=re(a?A():{},{"aria-activedescendant":t.activeItemIndex===null||(J=t.items[t.activeItemIndex])==null?void 0:J.id,"aria-labelledby":(V=t.buttonElement)==null?void 0:V.id,id:l,onKeyDown:G,onKeyUp:ae,role:"menu",tabIndex:t.menuState===0?0:void 0,ref:D,style:{...m.style,...c,"--button-width":Se(t.buttonElement,!0).width},...he(C)}),se=_();return x.createElement($e,{enabled:u?e.static||v:!1},se({ourProps:ie,theirProps:m,slot:le,defaultTag:at,features:lt,visible:K,name:"Menu.Items"}))}let st=z;function ut(e,n){let r=U(),{id:l=`headlessui-menu-item-${r}`,disabled:o=!1,...u}=e,[p,s]=w("Menu.Item"),m=p.activeItemIndex!==null?p.items[p.activeItemIndex].id===l:!1,a=Y(null),t=N(n,a);B(()=>{if(!p.__demoMode&&p.menuState===0&&m&&p.activationTrigger!==0)return Ke().requestAnimationFrame(()=>{var I,G;(G=(I=a.current)==null?void 0:I.scrollIntoView)==null||G.call(I,{block:"nearest"})})},[p.__demoMode,a,m,p.menuState,p.activationTrigger,p.activeItemIndex]);let d=_e(a),f=Y({disabled:o,domRef:a,get textValue(){return d()}});B(()=>{f.current.disabled=o},[f,o]),B(()=>(s({type:5,id:l,dataRef:f}),()=>s({type:6,id:l})),[f,l]);let c=E(()=>{s({type:1})}),A=E(I=>{if(o)return I.preventDefault();s({type:1}),ee(p.buttonElement)}),b=E(()=>{if(o)return s({type:2,focus:g.Nothing});s({type:2,focus:g.Specific,id:l})}),S=De(),D=E(I=>{S.update(I),!o&&(m||s({type:2,focus:g.Specific,id:l,trigger:0}))}),M=E(I=>{S.wasMoved(I)&&(o||m||s({type:2,focus:g.Specific,id:l,trigger:0}))}),P=E(I=>{S.wasMoved(I)&&(o||m&&s({type:2,focus:g.Nothing}))}),[v,C]=oe(),[O,h]=Ve(),T=H(()=>({active:m,focus:m,disabled:o,close:c}),[m,o,c]),F={id:l,ref:t,role:"menuitem",tabIndex:o===!0?void 0:-1,"aria-disabled":o===!0?!0:void 0,"aria-labelledby":v,"aria-describedby":O,disabled:void 0,onClick:A,onFocus:b,onPointerEnter:D,onMouseEnter:D,onPointerMove:M,onMouseMove:M,onPointerLeave:P,onMouseLeave:P},K=_();return x.createElement(C,null,x.createElement(h,null,K({ourProps:F,theirProps:u,slot:T,defaultTag:st,name:"Menu.Item"})))}let pt="div";function mt(e,n){let[r,l]=oe(),o=e,u={ref:n,"aria-labelledby":r,role:"group"},p=_();return x.createElement(l,null,p({ourProps:u,theirProps:o,slot:{},defaultTag:pt,name:"Menu.Section"}))}let dt="header";function ct(e,n){let r=U(),{id:l=`headlessui-menu-heading-${r}`,...o}=e,u=Xe();B(()=>u.register(l),[l,u.register]);let p={id:l,ref:n,role:"presentation",...u.props};return _()({ourProps:p,theirProps:o,slot:{},defaultTag:dt,name:"Menu.Heading"})}let ft="div";function Tt(e,n){let r=e,l={ref:n,role:"separator"};return _()({ourProps:l,theirProps:r,slot:{},defaultTag:ft,name:"Menu.Separator"})}let yt=R(nt),It=R(ot),gt=R(it),Et=R(ut),Mt=R(mt),St=R(ct),At=R(Tt),rn=Object.assign(yt,{Button:It,Items:gt,Item:Et,Section:Mt,Heading:St,Separator:At});export{rn as Menu,It as MenuButton,St as MenuHeading,Et as MenuItem,gt as MenuItems,Mt as MenuSection,At as MenuSeparator};
