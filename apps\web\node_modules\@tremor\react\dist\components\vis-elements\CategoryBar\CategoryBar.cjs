'use client';
"use strict";var e=require("tslib"),t=require("react"),r=require("../../util-elements/Tooltip/Tooltip.cjs"),l=require("../../../lib/theme.cjs"),a=require("../../../lib/tremorTwMerge.cjs"),s=require("../../../lib/utils.cjs");function o(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var m=o(t);const n=s.makeClassName("CategoryBar"),u=(e,t)=>e?e/t*100:0,i=({values:e})=>{const r=t.useMemo((()=>s.sumNumericArray(e)),[e]);let l=0,o=0;return m.default.createElement("div",{className:a.tremorTwMerge(n("labels"),"relative flex w-full text-tremor-default h-5 mb-2","text-tremor-content","dark:text-dark-tremor-content")},e.slice(0,e.length).map(((e,t)=>{l+=e;const s=(e>=.1*r||o>=.09*r)&&r-l>=.15*r&&l>=.1*r;o=s?0:o+=e;const n=u(e,r);return m.default.createElement("div",{key:`item-${t}`,className:"flex items-center justify-end",style:{width:`${n}%`}},m.default.createElement("span",{className:a.tremorTwMerge(s?"block":"hidden","left-1/2 translate-x-1/2")},l))})),m.default.createElement("div",{className:a.tremorTwMerge("absolute bottom-0 flex items-center left-0")},"0"),m.default.createElement("div",{className:a.tremorTwMerge("absolute bottom-0 flex items-center right-0")},r))},c=m.default.forwardRef(((o,c)=>{const{values:d=[],colors:f=l.themeColorRange,markerValue:g,showLabels:b=!0,tooltip:v,showAnimation:w=!1,className:h}=o,N=e.__rest(o,["values","colors","markerValue","showLabels","tooltip","showAnimation","className"]),p=t.useMemo((()=>((e,t,r)=>{if(void 0===e)return"";let a=0;for(let o=0;o<t.length;o++){const m=t[o],n=s.getColorClassNames(r[o],l.colorPalette.background).bgColor;if(a+=m,a>=e)return n}return""})(g,d,f)),[g,d,f]),{tooltipProps:x,getReferenceProps:M}=r.useTooltip(),k=t.useMemo((()=>s.sumNumericArray(d)),[d]),y=t.useMemo((()=>u(g,k)),[g,k]);return m.default.createElement(m.default.Fragment,null,m.default.createElement(r.default,Object.assign({text:v},x)),m.default.createElement("div",Object.assign({ref:c,className:a.tremorTwMerge(n("root"),h)},N),b?m.default.createElement(i,{values:d}):null,m.default.createElement("div",{className:a.tremorTwMerge(n("barWrapper"),"relative w-full flex items-center h-2")},m.default.createElement("div",{className:a.tremorTwMerge("flex-1 flex items-center h-full overflow-hidden rounded-tremor-full")},d.map(((e,t)=>{var r;const o=null!==(r=f[t])&&void 0!==r?r:"gray",u=e/k*100;return m.default.createElement("div",{key:`item-${t}`,className:a.tremorTwMerge(n("categoryBar"),"h-full",s.getColorClassNames(o,l.colorPalette.background).bgColor),style:{width:`${u}%`}})}))),void 0!==g?m.default.createElement("div",Object.assign({ref:x.refs.setReference,className:a.tremorTwMerge(n("markerWrapper"),"absolute right-1/2 -translate-x-1/2 w-5"),style:{left:`${y}%`,transition:w?"all 1s":""}},M),m.default.createElement("div",{className:a.tremorTwMerge(n("marker"),"ring-2 mx-auto rounded-tremor-full h-4 w-1","ring-tremor-brand-inverted","dark:ring-dark-tremor-brand-inverted",p)})):null)))}));c.displayName="CategoryBar",module.exports=c;
