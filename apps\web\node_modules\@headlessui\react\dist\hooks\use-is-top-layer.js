import{useId as n}from"react";import{DefaultMap as f}from'../utils/default-map.js';import{createStore as u}from'../utils/store.js';import{useIsoMorphicEffect as c}from'./use-iso-morphic-effect.js';import{useStore as l}from'./use-store.js';let p=new f(()=>u(()=>[],{ADD(r){return this.includes(r)?this:[...this,r]},REMOVE(r){let e=this.indexOf(r);if(e===-1)return this;let t=this.slice();return t.splice(e,1),t}}));function x(r,e){let t=p.get(e),i=n(),h=l(t);if(c(()=>{if(r)return t.dispatch("ADD",i),()=>t.dispatch("REMOVE",i)},[t,r]),!r)return!1;let s=h.indexOf(i),o=h.length;return s===-1&&(s=o,o+=1),s===o-1}export{x as useIsTopLayer};
