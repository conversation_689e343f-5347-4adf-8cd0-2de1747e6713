'use client';
import{__rest as e}from"tslib";import r from"../../../assets/ArrowDownHeadIcon.js";import t,{useRef as a,Children as o,useMemo as l,isValidElement as n}from"react";import s from"../../../assets/XCircleIcon.js";import{tremorTwMerge as d}from"../../../lib/tremorTwMerge.js";import{makeClassName as m}from"../../../lib/utils.js";import{constructValueToNameMapping as c,getSelectButtonColors as i,hasValue as u}from"../selectUtils.js";import{Listbox as b,ListboxButton as p,Transition as f,ListboxOptions as h}from"@headlessui/react";import k from"../../../hooks/useInternalState.js";const w=m("Select"),v=t.forwardRef(((m,v)=>{const{defaultValue:g="",value:x,onValueChange:y,placeholder:E="Select...",disabled:N=!1,icon:C,enableClear:j=!1,required:I,children:V,name:D,error:F=!1,errorMessage:M,className:S,id:q}=m,A=e(m,["defaultValue","value","onValueChange","placeholder","disabled","icon","enableClear","required","children","name","error","errorMessage","className","id"]),T=a(null),z=o.toArray(V),[H,O]=k(g,x),R=C,U=l((()=>{const e=t.Children.toArray(V).filter(n);return c(e)}),[V]);return t.createElement("div",{className:d("w-full min-w-[10rem] text-tremor-default",S)},t.createElement("div",{className:"relative"},t.createElement("select",{title:"select-hidden",required:I,className:d("h-full w-full absolute left-0 top-0 -z-10 opacity-0"),value:H,onChange:e=>{e.preventDefault()},name:D,disabled:N,id:q,onFocus:()=>{const e=T.current;e&&e.focus()}},t.createElement("option",{className:"hidden",value:"",disabled:!0,hidden:!0},E),z.map((e=>{const r=e.props.value,a=e.props.children;return t.createElement("option",{className:"hidden",key:r,value:r},a)}))),t.createElement(b,Object.assign({as:"div",ref:v,defaultValue:H,value:H,onChange:e=>{null==y||y(e),O(e)},disabled:N,id:q},A),(({value:e})=>{var a;return t.createElement(t.Fragment,null,t.createElement(p,{ref:T,className:d("w-full outline-none text-left whitespace-nowrap truncate rounded-tremor-default focus:ring-2 transition duration-100 border pr-8 py-2","border-tremor-border shadow-tremor-input focus:border-tremor-brand-subtle focus:ring-tremor-brand-muted","dark:border-dark-tremor-border dark:shadow-dark-tremor-input dark:focus:border-dark-tremor-brand-subtle dark:focus:ring-dark-tremor-brand-muted",R?"pl-10":"pl-3",i(u(e),N,F))},R&&t.createElement("span",{className:d("absolute inset-y-0 left-0 flex items-center ml-px pl-2.5")},t.createElement(R,{className:d(w("Icon"),"flex-none h-5 w-5","text-tremor-content-subtle","dark:text-dark-tremor-content-subtle")})),t.createElement("span",{className:"w-[90%] block truncate"},e&&null!==(a=U.get(e))&&void 0!==a?a:E),t.createElement("span",{className:d("absolute inset-y-0 right-0 flex items-center mr-3")},t.createElement(r,{className:d(w("arrowDownIcon"),"flex-none h-5 w-5","text-tremor-content-subtle","dark:text-dark-tremor-content-subtle")}))),j&&H?t.createElement("button",{type:"button",className:d("absolute inset-y-0 right-0 flex items-center mr-8"),onClick:e=>{e.preventDefault(),O(""),null==y||y("")}},t.createElement(s,{className:d(w("clearIcon"),"flex-none h-4 w-4","text-tremor-content-subtle","dark:text-dark-tremor-content-subtle")})):null,t.createElement(f,{enter:"transition ease duration-100 transform",enterFrom:"opacity-0 -translate-y-4",enterTo:"opacity-100 translate-y-0",leave:"transition ease duration-100 transform",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 -translate-y-4"},t.createElement(h,{anchor:"bottom start",className:d("z-10 w-[var(--button-width)] divide-y overflow-y-auto outline-none rounded-tremor-default max-h-[228px]  border [--anchor-gap:4px]","bg-tremor-background border-tremor-border divide-tremor-border shadow-tremor-dropdown","dark:bg-dark-tremor-background dark:border-dark-tremor-border dark:divide-dark-tremor-border dark:shadow-dark-tremor-dropdown")},V)))}))),F&&M?t.createElement("p",{className:d("errorMessage","text-sm text-rose-500 mt-1")},M):null)}));v.displayName="Select";export{v as default};
