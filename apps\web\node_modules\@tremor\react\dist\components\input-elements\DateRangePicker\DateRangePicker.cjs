'use client';
"use strict";var e=require("tslib"),r=require("react"),t=require("../../../assets/CalendarIcon.cjs"),a=require("../../../assets/XCircleIcon.cjs"),o=require("date-fns"),n=require("../../../lib/tremorTwMerge.cjs"),l=require("../selectUtils.cjs"),d=require("./dateRangePickerUtils.cjs"),s=require("../Calendar/Calendar.cjs");require("../Select/Select.cjs");var u=require("../Select/SelectItem.cjs"),m=require("date-fns/locale"),i=require("../../../hooks/useInternalState.cjs"),c=require("@headlessui/react");function f(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var b=f(r);const p=o.startOfToday(),k=b.default.forwardRef(((f,k)=>{var g,v;const{value:w,defaultValue:h,onValueChange:x,enableSelect:y=!0,minDate:N,maxDate:E,placeholder:T="Select range",selectPlaceholder:M="Select range",disabled:S=!1,locale:C=m.enUS,enableClear:q=!0,displayFormat:D,children:j,className:O,enableYearNavigation:P=!1,weekStartsOn:V=0,disabledDates:F}=f,_=e.__rest(f,["value","defaultValue","onValueChange","enableSelect","minDate","maxDate","placeholder","selectPlaceholder","disabled","locale","enableClear","displayFormat","children","className","enableYearNavigation","weekStartsOn","disabledDates"]),[B,I]=i(h,w),[R,L]=r.useState(!1),[U,Y]=r.useState(!1),z=r.useMemo((()=>{const e=[];return N&&e.push({before:N}),E&&e.push({after:E}),[...e,...null!=F?F:[]]}),[N,E,F]),X=r.useMemo((()=>{const e=new Map;return j?b.default.Children.forEach(j,(r=>{var t;e.set(r.props.value,{text:null!==(t=l.getNodeText(r))&&void 0!==t?t:r.props.value,from:r.props.from,to:r.props.to})})):d.defaultOptions.forEach((r=>{e.set(r.value,{text:r.text,from:r.from,to:p})})),e}),[j]),A=r.useMemo((()=>{if(j)return l.constructValueToNameMapping(j);const e=new Map;return d.defaultOptions.forEach((r=>e.set(r.value,r.text))),e}),[j]),G=(null==B?void 0:B.selectValue)||"",H=d.parseStartDate(null==B?void 0:B.from,N,G,X),J=d.parseEndDate(null==B?void 0:B.to,E,G,X),K=H||J?d.formatSelectedDates(H,J,C,D):T,Q=o.startOfMonth(null!==(v=null!==(g=null!=J?J:H)&&void 0!==g?g:E)&&void 0!==v?v:p),W=q&&!S;return b.default.createElement("div",Object.assign({ref:k,className:n.tremorTwMerge("w-full min-w-[10rem] relative flex justify-between text-tremor-default max-w-sm shadow-tremor-input dark:shadow-dark-tremor-input rounded-tremor-default",O)},_),b.default.createElement(c.Popover,{as:"div",className:n.tremorTwMerge("w-full",y?"rounded-l-tremor-default":"rounded-tremor-default",R&&"ring-2 ring-tremor-brand-muted dark:ring-dark-tremor-brand-muted z-10")},b.default.createElement("div",{className:"relative w-full"},b.default.createElement(c.PopoverButton,{onFocus:()=>L(!0),onBlur:()=>L(!1),disabled:S,className:n.tremorTwMerge("w-full outline-none text-left whitespace-nowrap truncate focus:ring-2 transition duration-100 rounded-l-tremor-default flex flex-nowrap border pl-3 py-2","rounded-l-tremor-default border-tremor-border text-tremor-content-emphasis focus:border-tremor-brand-subtle focus:ring-tremor-brand-muted","dark:border-dark-tremor-border dark:text-dark-tremor-content-emphasis dark:focus:border-dark-tremor-brand-subtle dark:focus:ring-dark-tremor-brand-muted",y?"rounded-l-tremor-default":"rounded-tremor-default",W?"pr-8":"pr-4",l.getSelectButtonColors(l.hasValue(H||J),S))},b.default.createElement(t,{className:n.tremorTwMerge(d.makeDateRangePickerClassName("calendarIcon"),"flex-none shrink-0 h-5 w-5 -ml-0.5 mr-2","text-tremor-content-subtle","dark:text-dark-tremor-content-subtle"),"aria-hidden":"true"}),b.default.createElement("p",{className:"truncate"},K)),W&&H?b.default.createElement("button",{type:"button",className:n.tremorTwMerge("absolute outline-none inset-y-0 right-0 flex items-center transition duration-100 mr-4"),onClick:e=>{e.preventDefault(),null==x||x({}),I({})}},b.default.createElement(a,{className:n.tremorTwMerge(d.makeDateRangePickerClassName("clearIcon"),"flex-none h-4 w-4","text-tremor-content-subtle","dark:text-dark-tremor-content-subtle")})):null),b.default.createElement(c.Transition,{enter:"transition ease duration-100 transform",enterFrom:"opacity-0 -translate-y-4",enterTo:"opacity-100 translate-y-0",leave:"transition ease duration-100 transform",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 -translate-y-4"},b.default.createElement(c.PopoverPanel,{anchor:"bottom start",focus:!0,className:n.tremorTwMerge("min-w-min divide-y overflow-y-auto outline-none rounded-tremor-default p-3 border [--anchor-gap:4px]","bg-tremor-background border-tremor-border divide-tremor-border shadow-tremor-dropdown","dark:bg-dark-tremor-background dark:border-dark-tremor-border dark:divide-dark-tremor-border dark:shadow-dark-tremor-dropdown")},b.default.createElement(s,Object.assign({mode:"range",showOutsideDays:!0,defaultMonth:Q,selected:{from:H,to:J},onSelect:e=>{null==x||x({from:null==e?void 0:e.from,to:null==e?void 0:e.to}),I({from:null==e?void 0:e.from,to:null==e?void 0:e.to})},locale:C,disabled:z,enableYearNavigation:P,classNames:{day_range_middle:n.tremorTwMerge("!rounded-none aria-selected:!bg-tremor-background-subtle aria-selected:dark:!bg-dark-tremor-background-subtle aria-selected:!text-tremor-content aria-selected:dark:!bg-dark-tremor-background-subtle"),day_range_start:"rounded-r-none rounded-l-tremor-small aria-selected:text-tremor-brand-inverted dark:aria-selected:text-dark-tremor-brand-inverted",day_range_end:"rounded-l-none rounded-r-tremor-small aria-selected:text-tremor-brand-inverted dark:aria-selected:text-dark-tremor-brand-inverted"},weekStartsOn:V},f))))),y&&b.default.createElement(c.Listbox,{as:"div",className:n.tremorTwMerge("w-48 -ml-px rounded-r-tremor-default",U&&"ring-2 ring-tremor-brand-muted dark:ring-dark-tremor-brand-muted z-10"),value:G,onChange:e=>{const{from:r,to:t}=X.get(e),a=null!=t?t:p;null==x||x({from:r,to:a,selectValue:e}),I({from:r,to:a,selectValue:e})},disabled:S},(({value:e})=>{var r;return b.default.createElement(b.default.Fragment,null,b.default.createElement(c.ListboxButton,{onFocus:()=>Y(!0),onBlur:()=>Y(!1),className:n.tremorTwMerge("w-full outline-none text-left whitespace-nowrap truncate rounded-r-tremor-default transition duration-100 border px-4 py-2","border-tremor-border text-tremor-content-emphasis focus:border-tremor-brand-subtle","dark:border-dark-tremor-border  dark:text-dark-tremor-content-emphasis dark:focus:border-dark-tremor-brand-subtle",l.getSelectButtonColors(l.hasValue(e),S))},e&&null!==(r=A.get(e))&&void 0!==r?r:M),b.default.createElement(c.Transition,{enter:"transition ease duration-100 transform",enterFrom:"opacity-0 -translate-y-4",enterTo:"opacity-100 translate-y-0",leave:"transition ease duration-100 transform",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 -translate-y-4"},b.default.createElement(c.ListboxOptions,{anchor:"bottom end",className:n.tremorTwMerge("[--anchor-gap:4px] divide-y overflow-y-auto outline-none border min-w-44","shadow-tremor-dropdown bg-tremor-background border-tremor-border divide-tremor-border rounded-tremor-default","dark:shadow-dark-tremor-dropdown dark:bg-dark-tremor-background dark:border-dark-tremor-border dark:divide-dark-tremor-border")},null!=j?j:d.defaultOptions.map((e=>b.default.createElement(u,{key:e.value,value:e.value},e.text))))))})))}));k.displayName="DateRangePicker",module.exports=k;
