import{tremorTwMerge as r}from"../../lib/tremorTwMerge.js";import e from"react";const t=r=>["string","number"].includes(typeof r)?r:r instanceof Array?r.map(t).join(""):"object"==typeof r&&r?t(r.props.children):void 0;function o(r){const o=new Map;return e.Children.map(r,(r=>{var e;o.set(r.props.value,null!==(e=t(r))&&void 0!==e?e:r.props.value)})),o}function d(r,o){return e.Children.map(o,(e=>{var o;if((null!==(o=t(e))&&void 0!==o?o:e.props.value).toLowerCase().includes(r.toLowerCase()))return e}))}const n=(e,t,o=!1)=>r(t?"bg-tremor-background-subtle dark:bg-dark-tremor-background-subtle":"bg-tremor-background dark:bg-dark-tremor-background",!t&&"hover:bg-tremor-background-muted dark:hover:bg-dark-tremor-background-muted",e?"text-tremor-content-emphasis dark:text-dark-tremor-content-emphasis":"text-tremor-content dark:text-dark-tremor-content",t&&"text-tremor-content-subtle dark:text-dark-tremor-content-subtle",o&&"text-red-500 placeholder:text-red-500 dark:text-red-500 dark:placeholder:text-red-500",o?"border-red-500 dark:border-red-500":"border-tremor-border dark:border-dark-tremor-border");function a(r){return null!=r&&""!==r}export{o as constructValueToNameMapping,d as getFilteredOptions,t as getNodeText,n as getSelectButtonColors,a as hasValue};
